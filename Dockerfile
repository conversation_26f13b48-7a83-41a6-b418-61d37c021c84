ARG BASE=node:20.18.0
FROM ${BASE} AS base

WORKDIR /app

# Install dependencies (this step is cached as long as the dependencies don't change)
COPY package.json package-lock.json ./

RUN npm install

# Copy the rest of your app's source code
COPY . .

# Expose the port the app runs on
EXPOSE 5173

FROM base AS prod

# Define environment variables
ARG VITE_LOG_LEVEL=debug
ARG CUSTOM_BACKEND_API
ARG CUSTOM_BASE_URL
ARG CUSTOM_GITHUB_CLIENT_ID
ARG CUSTOM_GOOGLE_CLIENT_ID
ARG VITE_TURNSTILE_SITE_KEY

ENV VITE_LOG_LEVEL=${VITE_LOG_LEVEL}
ENV CUSTOM_BACKEND_API=${CUSTOM_BACKEND_API}
ENV CUSTOM_BASE_URL=${CUSTOM_BASE_URL}
ENV CUSTOM_GITHUB_CLIENT_ID=${CUSTOM_GITHUB_CLIENT_ID}
ENV CUSTOM_GOOGLE_CLIENT_ID=${CUSTOM_GOOGLE_CLIENT_ID}
ENV VITE_TURNSTILE_SITE_KEY=${VITE_TURNSTILE_SITE_KEY}
ENV NODE_ENV=production

RUN npm run build

CMD [ "npm", "run", "preview"]
