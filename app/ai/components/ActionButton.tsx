import React from 'react';
import clsx from 'clsx';
import { motion } from 'framer-motion';
import { Tooltip } from 'react-tooltip';
import { FaSpinner } from 'react-icons/fa';
import 'react-tooltip/dist/react-tooltip.css';

interface ActionButtonProps {
  icon: React.ElementType;
  id:string;
  label: string;
  onClick: () => void;
  disabled?: boolean;
  variant?: 'default' | 'download';
  openPosition?:'top'
    | 'top-start'
    | 'top-end'
    | 'right'
    | 'right-start'
    | 'right-end'
    | 'bottom'
    | 'bottom-start'
    | 'bottom-end'
    | 'left'
    | 'left-start'
    | 'left-end';
  downloading?: boolean;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  icon: Icon,
  id,
  label,
  onClick,
  disabled = false,
  variant = 'default',
  openPosition = 'bottom',
  downloading = false,
}) => {
  const isDownload = variant === 'download';

  const extraStyles = isDownload
    ? {
        backgroundImage:
          'linear-gradient(45deg, rgba(255,255,255,0.05) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.05) 50%, rgba(255,255,255,0.05) 75%, transparent 75%, transparent)',
        backgroundSize: '8px 8px',
      }
    : {};

  return (
    <>
      <motion.button
        onClick={onClick}
        disabled={disabled}
        whileHover={{ scale: disabled ? 1 : 1.05 }}
        whileTap={{ scale: disabled ? 1 : 0.95 }}
        data-tooltip-id={id}
        data-tooltip-content={label}
        style={extraStyles}
        className={clsx(
          'flex items-center rounded-lg transition-all duration-300 relative group',
          disabled && 'opacity-50 cursor-not-allowed',
          isDownload
            ? 'bg-[#0B1931] text-white py-1.5 px-2.5 shadow-md hover:bg-[#152A4A] hover:shadow-lg hover:scale-105'
            : 'bg-[#334155]/20 text-[#94A3B8] p-6px ml-4px border border-[#334155]/30 backdrop-blur-sm hover:bg-[#334155]/40 hover:text-[#f8fafc] hover:border-[#334155] hover:scale-105',
        )}
      >
        {downloading ? (
          <FaSpinner className="animate-spin text-gray-400 w-18px h-18px" />
        ) : (
          <Icon className="w-18px h-18px" />
        )}
      </motion.button>

      <Tooltip id={id} place={openPosition} className="react-tooltip" />
    </>
  );
};

export default ActionButton;
