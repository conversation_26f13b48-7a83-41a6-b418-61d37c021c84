import { AnimatePresence, motion } from 'framer-motion';
import type { ActionAlert } from '~/types/actions';
import { classNames } from '~/utils/classNames';
import styles from '~/ai/styles/BaseChat.module.scss';
import FiSrCross from '~/assets/icons/fiSrCross';
import { useStore } from '@nanostores/react';
import { chatStore, setErrorDetails } from '~/ai/lib/stores/chat';
import WithTooltip from './Tooltip';
import { ChatBubbleLeftIcon } from '@heroicons/react/24/outline';

interface Props {
  alert: ActionAlert;
  clearAlert: () => void;
  postMessage: (message: string) => void;
}

function reducedMessage(content: string): string {
  const lines = content.split('\n');
  return lines.slice(0, 8).join('\n');
}

export default function ChatAlert({ alert, clearAlert, postMessage }: Props) {
  const { description, content, source } = alert;
  const {errorDetails} =useStore(chatStore)
  const isPreview = source === 'preview';
  const title = isPreview ? 'Preview Error' : 'Terminal Error';
  const message = isPreview
    ? 'We encountered an error while running the preview. Would you like Biela to analyze and help resolve this issue?'
    : 'We encountered an error while running terminal commands. Would you like Biela to analyze and help resolve this issue?';

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className={`rounded-t-lg relative border border-b-[0px] bg-transparent overflow-hidden border-biela-elements-borderColor bg-biela-elements-background-depth-2 py-[16px] px-[12px]`}
      >
        <div className="flex items-start ">
          {/* Content */}
          <div className="mx-3 flex-1">
            <motion.h3
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.1 }}
              className={`text-sm font-extralight text-white/90 mb-3`}
            >
              {title}
            </motion.h3>

            <div className={'overflow-hidden rounded-lg border px-[16px] py-[12px] border-[rgba(74,222,128,0.1019607843)]'}>
              <div className={classNames(styles.errorWrapperParent)}>
                <div className={'relative z-2'}>
                  <div className={classNames(styles.errorWrapper, 'flex gap-[10px]')}>
                    {/* Icon */}
                    <motion.div
                      className="flex-shrink-0 transition-all duration-200"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2 }}
                    >
                      <img src="/info-red.svg" alt="Error" />
                    </motion.div>
                    <motion.div>
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.2 }}
                      >
                        <p className={'relative z-2 text-sm font-extralight text-white/90'}>{message}</p>
                        {description && (
                          <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.2 }}
                            className={`error-details ${errorDetails ? 'show' : 'hide'} w-[-webkit-fill-available] relative z-2  p-2  rounded mt-4 mb-4`}
                          >
                            <p className={'min-h-[32px] max-w-[320px] mx-[auto] text-sm font-extralight text-white/90'}>Error: {description}</p>
                          </motion.div>
                        )}
                      </motion.div>
                    </motion.div>
                  </div>
                </div>
                <motion.div
                  className="mt-4"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <div className={classNames('flex gap-3 relative z-2 items-center')}>
                    <button
                      onClick={() =>
                        postMessage(
                          `*Fix this ${isPreview ? 'preview' : 'terminal'} error* \n\`\`\`${isPreview ? 'js' : 'sh'}\n${reducedMessage(content)}\n\`\`\`\n`,
                        )
                      }
                      className={'flex items-center justify-center gap-2 px-3 py-2 rounded-md transition-colors bg-[#4ADE80] text-black'}
                    >
                      <ChatBubbleLeftIcon className="w-4 h-4"/>
                      <p className={'text-[15px] font-normal tracking-[0.4px]'}>
                        Ask BIELA
                      </p>
                    </button>
                    <button
                      onClick={clearAlert}
                      className={'flex items-center  border border-white/5 justify-center gap-2 px-3 py-2 rounded-md transition-colors text-white/70 bg-transparent hover:bg-white/5'}
                    >
                        <FiSrCross />
                        Dismiss
                    </button>
                    <WithTooltip tooltip={`${errorDetails ? 'Hide Error Details' : 'Error Details'}`}>
                      <button
                        onClick={()=>{
                          setErrorDetails(!errorDetails)
                        }}
                        className="p-[4px] border rounded-full bg-transparent hover:bg-white/5 border-[#2E2E2E] flex items-center"
                        data-state="closed"
                      >
                        <p
                          className={
                            `${errorDetails ? 'i-ph:caret-down' : 'i-ph:caret-up'} min-w-[14px] min-h-[14px] bg-white`
                          }
                        ></p>
                      </button>
                    </WithTooltip>

                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
