import { motion } from 'framer-motion';
import { memo } from 'react';
import { classNames } from '~/utils/classNames';
import { cubicEasingFn } from '~/utils/easings';
import { genericMemo } from '~/utils/react';
import { ChatBubbleLeftIcon } from '@heroicons/react/24/outline';

interface ChatCodeSliderOption<T> {
  value: T;
  text: string;
}

export interface ChatCodeSliderOptions<T> {
  left: ChatCodeSliderOption<T>;
  right: ChatCodeSliderOption<T>;
}

interface ChatCodeSliderProps<T> {
  selected: T;
  options: ChatCodeSliderOptions<T>;
  setChatCodeSelected?: (selected: T) => void;
  isOtherTabActive: boolean;
  disableLayoutAnimation?: boolean;
  modelColor: string;
}

export const ChatCodeSlider = genericMemo(
  <T,>({
    selected,
    options,
    setChatCodeSelected,
    isOtherTabActive,
    disableLayoutAnimation = false,
    modelColor,
  }: ChatCodeSliderProps<T>) => {
    const isLeftSelected = selected === options.left.value;

    return (
      <div className="flex">
        <div className="flex items-center flex-1 gap-1 show-prompt rounded-md overflow-hidden border-0">
          <ChatCodeSliderButton
            variant="code"
            selected={isOtherTabActive ? false : !isLeftSelected}
            setChatCodeSelected={() => setChatCodeSelected?.(options.right.value)}
            disableLayoutAnimation={disableLayoutAnimation}
            modelColor={modelColor}
          >
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
                />
              </svg>
              <span className="text-[15px] font-normal tracking-[0.4px]">{options.right.text}</span>
            </div>
          </ChatCodeSliderButton>

          <ChatCodeSliderButton
            variant="chat" // chat mode button
            selected={isOtherTabActive ? false : isLeftSelected}
            setChatCodeSelected={() => setChatCodeSelected?.(options.left.value)}
            disableLayoutAnimation={disableLayoutAnimation}
          >
            <div className="flex items-center gap-2">
              <ChatBubbleLeftIcon className="w-4 h-4" />
              <span className="text-[15px] font-normal tracking-[0.4px]">{options.left.text}</span>
            </div>
          </ChatCodeSliderButton>
        </div>
      </div>
    );
  },
);

interface ChatCodeSliderButtonProps {
  selected: boolean;
  variant: 'chat' | 'code';
  children: JSX.Element;
  setChatCodeSelected: () => void;
  disableLayoutAnimation?: boolean;
  modelColor?: string;
}

const ChatCodeSliderButton = memo(
  ({
    selected,
    variant,
    children,
    setChatCodeSelected,
    disableLayoutAnimation = false,
    modelColor,
  }: ChatCodeSliderButtonProps) => {
    const baseClasses =
      'text-sm px-3 py-1.5 rounded-md relative hover:bg-white/5 transition-all flex-1 ease-in-out duration-300';

    const selectedClasses = variant === 'chat' ? 'text-[#000] bg-[#800080]' : 'text-[#010101] bg-[#4ADE80]';
    const notSelectedClasses = 'text-white/70 text-biela-elements-item-contentActive bg-transparent';

    return (
      <button
        onClick={setChatCodeSelected}
        className={classNames(baseClasses, selected ? selectedClasses : notSelectedClasses)}
      >
        <span className="relative z-10 flex gap-2 justify-center">{children}</span>
        {selected && (
          <motion.span
            {...(!disableLayoutAnimation && { layoutId: 'pill-tab-chat-code' })}
            transition={{ duration: 0.2, ease: cubicEasingFn }}
            className="absolute inset-0 z-0 rounded-md"
            style={{
              backgroundColor: variant === 'chat' ? '#942894' : modelColor || '#4ADE80',
            }}
          ></motion.span>
        )}
      </button>
    );
  },
);
