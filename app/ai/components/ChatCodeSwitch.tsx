import { useStore } from '@nanostores/react';
import { memo, useEffect, useState } from 'react';
import { chatStore, setChatMode } from '~/ai/lib/stores/chat';
import { ChatCodeSlider, type ChatCodeSliderOptions } from '~/ai/components/ChatCodeSlider';
import { AI_MODELS, AI_MODEL_STORAGE_KEY } from '~/utils/constants';

const modeOptions: ChatCodeSliderOptions<'chat' | 'code'> = {
  left: {
    value: 'chat',
    text: 'Chat',
  },
  right: {
    value: 'code',
    text: 'Code',
  },
};

interface ChatCodeSwitchProps {
  activeTab: string;
  changeTabToChat: () => void;
  disableLayoutAnimation?: boolean;
}

export const ChatCodeSwitch = memo(
  ({ activeTab, changeTabToChat, disableLayoutAnimation = false }: ChatCodeSwitchProps) => {
    const mode = useStore(chatStore).mode;
    const [modelColor, setModelColor] = useState(() => {
      const currentModelId = localStorage.getItem(AI_MODEL_STORAGE_KEY) || AI_MODELS[0].value;
      const currentModel = AI_MODELS.find((model) => model.value === currentModelId) || AI_MODELS[0];

      return currentModel.color;
    });

    useEffect(() => {
      const handleModelChangeEvent = (event: CustomEvent<{ modelValue: string; source?: string }>) => {
        if (event.detail && event.detail.modelValue) {
          const newModel = AI_MODELS.find((model) => model.value === event.detail.modelValue);

          if (newModel) {
            setModelColor(newModel.color);
          }
        }
      };

      window.addEventListener('biela:changeAIModel', handleModelChangeEvent as EventListener);

      return () => {
        window.removeEventListener('biela:changeAIModel', handleModelChangeEvent as EventListener);
      };
    }, []);

    return (
      <ChatCodeSlider
        isOtherTabActive={activeTab !== 'chat'}
        selected={mode}
        options={modeOptions}
        disableLayoutAnimation={disableLayoutAnimation}
        modelColor={modelColor}
        setChatCodeSelected={(mode) => {
          changeTabToChat();
          setChatMode(mode);
        }}
      />
    );
  },
);
