import { AnimatePresence, motion } from 'framer-motion';
import { classNames } from '~/utils/classNames';
import FiSrCheck from '~/assets/icons/fiSrCheck';
import FiSrCross from '~/assets/icons/fiSrCross';
import styles from '../styles/BaseChat.module.scss';

interface Props {
  title: string;
  message: string;
  primaryButtonText: string;
  secondaryButtonText: string;
  postMessage: () => void;
  clearAlert: () => void;
}

export default function ChatSuggestion({
  title,
  message,
  primaryButtonText,
  secondaryButtonText,
  postMessage,
  clearAlert,
}: Props) {
  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className={`rounded-t-lg relative overflow-hidden py-3 border border-white/5`}
      >
        <div className="flex items-start">
          <div className="mx-3 flex-1">
            <motion.h3
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.1 }}
              className={`text-sm font-extralight text-white/90 mb-3`}
            >
              {title}
            </motion.h3>
            <div className={'overflow-hidden rounded-lg border px-[16px] py-[12px] border-[rgba(74,222,128,0.1019607843)]'}>
              <div className={classNames(styles.wrapperParent)}>
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                  className={`text-sm text-left text-biela-elements-button-secondary-text`}
                >
                  <p className={'relative z-2 text-sm font-extralight text-white/90'}>{message}</p>
                </motion.div>

                <motion.div
                  className="mt-4"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <div className={classNames('flex gap-3 relative z-2')}>
                    <button
                      onClick={postMessage}
                      className={'flex items-center justify-center gap-2 px-3 py-2 rounded-md transition-colors bg-[#4ADE80] text-dark text-[15px] font-normal tracking-[0.4px]'}
                    >
                        <FiSrCheck color={'#000'} />
                        {primaryButtonText}
                    </button>
                    <button
                      onClick={clearAlert}
                      className={'flex items-center  border border-white/5 justify-center gap-2 px-3 py-2 rounded-md transition-colors text-white/70 bg-transparent hover:bg-white/5'}
                    >
                        <FiSrCross />
                        {secondaryButtonText}
                    </button>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
