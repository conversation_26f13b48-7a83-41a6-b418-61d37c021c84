import { FC, type ReactNode, UIEvent } from 'react';
import { useTranslation } from 'react-i18next';
import { Dialog, DialogButton, DialogDescription, DialogRoot, DialogTitle } from './Dialog';
import _ from 'lodash';
import { LoadingOverlay } from '~/ai/components/LoadingOverlay';

export enum DialogConfirmationType {
  DELETE = 'delete',
  DUPLICATE = 'duplicate',
  DOWNLOAD = 'download',
  EXPORT = 'export',
  FORK = 'fork',
  ROLLBACK = 'rollback',
  SAVE = 'save',
  RESTORE = 'restore',
}

interface ConfirmationDialogProps {
  isOpen: boolean;
  type: DialogConfirmationType;
  description: string;
  onConfirm: (event: UIEvent) => Promise<void> | void;
  onCancel: () => void;
  isLoading?: boolean;
  content?: ReactNode;
  containerClassName?: string;
}

export const emptyConfirmationDialogHandler = Object.fromEntries(
  Object.values(DialogConfirmationType).map((type) => [type, () => {}]),
) as Record<DialogConfirmationType, () => void>;

const ConfirmationDialog: FC<ConfirmationDialogProps> = ({
  isOpen,
  type,
  description,
  onConfirm,
  onCancel,
  isLoading,
  content,
  containerClassName,
}) => {
  const { t } = useTranslation('translation');
  const chatTypes = [DialogConfirmationType.FORK, DialogConfirmationType.DELETE, DialogConfirmationType.SAVE];
  const isChatAction = chatTypes.includes(type);
  const titleKey = isChatAction ? 'titleChat' : 'titleMessage';
  const loadingKey = isChatAction ? 'loadingOverlayChat' : 'loadingOverlayMessages';
  const typeLabel = t(`${type}Button`);
  const typeNoun = t(`${type}Noun`);
  const typeInfinitive = t(`${type}Infinitive`);
  const nounCapitalized = _.capitalize(typeNoun);

  return (
    <DialogRoot open={isOpen}>
      <Dialog onBackdrop={onCancel} onClose={onCancel} className={containerClassName}>
        <DialogTitle>
          {t(titleKey, { typeCapitalized: nounCapitalized })}
        </DialogTitle>
        <DialogDescription asChild>
          <div>
            <p>
              {t('dialogDescriptionText', {type: typeInfinitive, description})}
            </p>
            {content}
            <p className="mt-4">
              {t('confirmText', { type: typeInfinitive })}
            </p>
          </div>
        </DialogDescription>
        <div className="px-6 pb-6 bg-bolt-elements-background-depth-2 flex gap-2 footer-modal">
          <DialogButton type="secondary" onClick={onCancel}>
            {t('cancelButton', 'Cancel')}
          </DialogButton>
          <DialogButton type="danger" disabled={isLoading} onClick={onConfirm}>
            <>
              {typeLabel}
              {isLoading && (
                <LoadingOverlay
                  message={t(loadingKey, { type: typeInfinitive })}
                />
              )}
            </>
          </DialogButton>
        </div>
      </Dialog>
    </DialogRoot>
  );
};

export default ConfirmationDialog;
