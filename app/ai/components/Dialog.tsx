import * as RadixDialog from '@radix-ui/react-dialog';
import { motion, type Variants } from 'framer-motion';
import React, { memo, type ReactNode } from 'react';
import { classNames } from '~/utils/classNames';
import { cubicEasingFn } from '~/utils/easings';

export { Close as DialogClose,Root as DialogRoot } from '@radix-ui/react-dialog';

const transition = {
  duration: 0.15,
  ease: cubicEasingFn,
};

export const dialogBackdropVariants = {
  closed: {
    opacity: 0,
    transition,
  },
  open: {
    opacity: 1,
    transition,
  },
} satisfies Variants;

export const dialogVariants = {
  closed: {
    x: '-50%',
    y: '-40%',
    scale: 0.96,
    opacity: 0,
    transition,
  },
  open: {
    x: '-50%',
    y: '-50%',
    scale: 1,
    opacity: 1,
    transition,
  },
} satisfies Variants;

interface DialogButtonProps {
  type: 'primary' | 'secondary' | 'danger';
  children: ReactNode;
  onClick?: (event: React.UIEvent) => void;
  disabled?: boolean;
}

export const DialogButton = memo(({ type, children, onClick, disabled }: DialogButtonProps) => {
  return (
    <button
      disabled={disabled}
      className={classNames(
        'inline-flex items-center justify-center rounded-lg px-5 py-5 md:px-6 md:py-4 text-lg text-white leading-none focus:outline-none border border-[#374151] md:min-w-[190px] w-full md:w-auto cursor-pointer',
        {
          'bg-biela-elements-button-primary-background text-biela-elements-button-primary-text hover:bg-biela-elements-button-primary-backgroundHover':
            type === 'primary',
          'bg-[#202938] hover:bg-gray-600': type === 'secondary',
          'bg-[#FF554A] hover:bg-biela-elements-button-danger-backgroundHover border-none': type === 'danger',
        },
      )}
      onClick={onClick}
    >
      {type === 'danger'}
      {children}
    </button>
  );
});

export const DialogTitle = memo(({ className, children, ...props }: RadixDialog.DialogTitleProps) => {
  return (
    <RadixDialog.Title
      className={classNames('px-6 py-5 flex items-center justify-between text-white text-xl ', className)}
      {...props}
    >
      {children}
    </RadixDialog.Title>
  );
});

export const DialogDescription = memo(({ className, children, ...props }: RadixDialog.DialogDescriptionProps) => {
  return (
    <RadixDialog.Description
      className={classNames('border-b border-t border-gray-700/50 mb-6 px-6 py-5 text-gray-400 ', className)}
      {...props}
    >
      {children}
    </RadixDialog.Description>
  );
});

interface DialogProps {
  children: ReactNode | ReactNode[];
  className?: string;
  hasRoot?: boolean; // New prop to conditionally add Root
  open?: boolean; // Allow controlling the open state
  onOpenChange?: (open: boolean) => void; // Callback for dialog state changes
  onBackdrop?: (event: React.UIEvent) => void;
}

export const Dialog = memo(({ className, children, hasRoot = false, open, onOpenChange, onBackdrop }: DialogProps) => {
  const DialogContent = (
    <RadixDialog.Portal>
      <RadixDialog.Overlay onClick={onBackdrop} asChild>
        <motion.div
          className="bg-black/50 fixed inset-0 z-max"
          initial="closed"
          animate="open"
          exit="closed"
          variants={dialogBackdropVariants}
        />
      </RadixDialog.Overlay>
      <RadixDialog.Content asChild>
        <motion.div
          className={classNames(
            'fixed top-[50%] left-[50%] z-max max-h-[85vh] w-[90vw] max-w-[600px] translate-x-[-50%] translate-y-[-50%] border border-biela-elements-borderColor rounded-lg bg-biela-elements-background-depth-2 shadow-lg focus:outline-none overflow-hidden modal-delete-chat',
            className,
          )}
          style={{borderRadius: '12px', border: '2px solid rgba(0, 0, 0, 0.00)', background: '#111727'}}
          initial="closed"
          animate="open"
          exit="closed"
          variants={dialogVariants}
        >
          {children}
        </motion.div>
      </RadixDialog.Content>
    </RadixDialog.Portal>
  );

  return hasRoot ? (
    <RadixDialog.Root open onOpenChange={onOpenChange}>
      {DialogContent}
    </RadixDialog.Root>
  ) : (
    DialogContent
  );
});
