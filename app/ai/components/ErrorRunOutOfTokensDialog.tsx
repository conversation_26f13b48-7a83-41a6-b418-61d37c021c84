import React, { FC, useState, useEffect } from 'react';
import * as RadixDialog from '@radix-ui/react-dialog';
import { motion, type Variants, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import UpgradePlansView from '~/ai/components/UpgradePlansView';
import TokenCalculator from '~/backOffice/components/billings/components/TokenCalculator';
import { TopUp } from '~/types/billing';
import { backendApiFetch } from '~/ai/lib/backend-api';

interface ErrorRunOutOfTokensDialogProps {
  isOpen: boolean;
  onBuyTokens: (tokens: string) => void;
  onClose: () => void;
}

const transition = {
  duration: 0.15,
  ease: [0.4, 0, 0.2, 1],
};

const backdropVariants: Variants = {
  closed: { opacity: 0, transition },
  open: { opacity: 1, transition },
};

const dialogVariants: Variants = {
  closed: { x: '-50%', y: '-40%', scale: 0.96, opacity: 0, transition },
  open: { x: '-50%', y: '-50%', scale: 1, opacity: 1, transition },
};

const contentVariants: Variants = {
  initial: { opacity: 0, x: -20, transition: { duration: 0.2 } },
  animate: { opacity: 1, x: 0, transition: { duration: 0.2 } },
  exit: { opacity: 0, x: 20, transition: { duration: 0.2 } },
};

const closeButtonVariants: Variants = {
  initial: { scale: 0.8, rotate: -180, opacity: 0 },
  animate: {
    scale: 1,
    rotate: 0,
    opacity: 1,
    transition: { type: 'spring', stiffness: 300, damping: 20 },
  },
  hover: {
    scale: 1.1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    transition: { type: 'spring', stiffness: 400, damping: 10 },
  },
  tap: { scale: 0.95 },
};

const ErrorRunOutOfTokensDialog: FC<ErrorRunOutOfTokensDialogProps> = ({ isOpen, onBuyTokens, onClose }) => {
  const { t } = useTranslation('translation');
  const [showPlans, setShowPlans] = useState(false);
  const [showTokenCalculator, setShowTokenCalculator] = useState(false);
  const [activeTopUp, setActiveTopUp] = useState<TopUp | null>(null);

  useEffect(() => {
    const fetchTopUps = async (): Promise<void> => {
      try {
        const response = await backendApiFetch('/plans/get-topUp', {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
        });
        if (!response.ok) throw new Error('Failed to fetch top-ups');
        const data = (await response.json()) as TopUp;
        setActiveTopUp(data);
      } catch (error) {
        console.error('Error fetching top-ups:', error);
      }
    };
    void fetchTopUps();
  }, []);

  const handleUpgradeClick = () => setShowPlans(true);
  const handleBackClick = () => setShowPlans(false);
  const handleBuyTokensClick = () => setShowTokenCalculator(true);
  const handleTokenPurchase = (tokens: string) => {
    setShowTokenCalculator(false);
    onBuyTokens(tokens);
  };

  const showMainDialog = isOpen && !showTokenCalculator;

  return (
    <>
      <RadixDialog.Root
        open={showMainDialog}
        onOpenChange={(open) => {
          if (!open) onClose();
        }}
      >
        <RadixDialog.Portal>
          <RadixDialog.Overlay asChild>
            <motion.div
              className="bg-black/50 fixed inset-0 z-40"
              initial="closed"
              animate="open"
              exit="closed"
              variants={backdropVariants}
              onClick={onClose}
            />
          </RadixDialog.Overlay>

          <RadixDialog.Content asChild>
            <motion.div
              onClick={(e) => e.stopPropagation()}
              className={`
                fixed top-[50%] left-[50%] z-max translate-x-[-50%] translate-y-[-50%]
                rounded-xl backdrop-blur-sm shadow-xl overflow-hidden overflow-y-auto w-[90vw] max-h-[85vh]
                ${!showPlans ? ' max-w-[900px]' : ''}
              `}
              style={{ background: '#111727', borderRadius: '12px' }}
              initial="closed"
              animate="open"
              exit="closed"
              variants={dialogVariants}
            >
              <div className="relative overflow-hidden">
                <AnimatePresence mode="wait" initial={false}>
                  {!showPlans ? (
                    <motion.div
                      key="main-view"
                      initial="initial"
                      animate="animate"
                      exit="exit"
                      variants={contentVariants}
                      className="w-full"
                    >
                      <div className="grid justify-items-center md:flex items-center gap-4 xl:gap-2 px-6 py-5 relative">
                        <div className="flex items-center">
                          <div className="p-2 bg-gray-800/80 rounded-lg mr-3">
                            <svg
                              className="w-5 h-5 text-red-500"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4Z"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M12 8V12"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <circle cx="12" cy="16" r="1" fill="currentColor" />
                            </svg>
                          </div>
                          <h2 className="text-white text-title font-light">
                            {t('TokensExhausted', "You've run out of tokens.")}
                          </h2>
                        </div>
                        <motion.button
                          onClick={onClose}
                          className="absolute right-4 top-4 p-2 rounded-full bg-gray-800/30 backdrop-blur-sm border border-gray-700/30"
                          aria-label="Close dialog"
                          initial="initial"
                          animate="animate"
                          whileHover="hover"
                          whileTap="tap"
                          variants={closeButtonVariants}
                        >
                          <svg
                            className="w-5 h-5 text-gray-300"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <motion.path
                              d="M18 6L6 18M6 6L18 18"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </motion.button>
                      </div>
                      <div className="border-b border-t border-gray-700/30 mb-6 px-6 py-5 text-gray-300 text-normal font-light">
                        <p>{t('TokensExhaustedDescription', 'To continue building your project, choose an option:')}</p>
                      </div>
                      <div className="px-6 pb-6">
                        <div className="flex flex-col md:flex-row gap-3 w-full">
                          <button
                            onClick={handleUpgradeClick}
                            className="flex-1 inline-flex items-center justify-center rounded-lg px-4 py-3 md:py-4 text-small text-white leading-none focus:outline-none bg-gradient-to-r from-purple-600 to-indigo-700 hover:from-indigo-700 hover:to-purple-600 hover:scale-105 shadow-md hover:shadow-lg transition-all font-light gap-2"
                          >
                            <svg
                              className="w-5 h-5 mr-1.5"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M16 6L18.29 8.29L13.41 13.17L9.41 9.17L2 16.59L3.41 18L9.41 12L13.41 16L19.71 9.71L22 12V6H16Z"
                                fill="currentColor"
                              />
                            </svg>
                            {t('upgradePlan', 'Upgrade My Plan')}
                          </button>

                          <button
                            onClick={handleBuyTokensClick}
                            className="flex-1 inline-flex items-center justify-center rounded-lg px-3 md:px-6 py-1.5 text-white font-light gap-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 shadow-lg shadow-green-500/20 transition-all"
                          >
                            <svg
                              className="w-5 h-5 mr-1.5"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                              <circle cx="12" cy="12" r="4" fill="currentColor" />
                            </svg>
                            {t('buyMoreTokens', 'Buy More Tokens')}
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  ) : (
                    <motion.div
                      key="plans-view"
                      initial="initial"
                      animate="animate"
                      exit="exit"
                      variants={contentVariants}
                      className="w-full"
                    >
                      <div className="px-6 py-5">
                        <UpgradePlansView onBack={handleBackClick} />
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          </RadixDialog.Content>
        </RadixDialog.Portal>
      </RadixDialog.Root>

      {isOpen && (
        <TokenCalculator
          showTokenCalculator={showTokenCalculator}
          setShowTokenCalculator={setShowTokenCalculator}
          activeTopUp={activeTopUp}
          onPurchase={handleTokenPurchase}
        />
      )}
    </>
  );
};

export default ErrorRunOutOfTokensDialog;
