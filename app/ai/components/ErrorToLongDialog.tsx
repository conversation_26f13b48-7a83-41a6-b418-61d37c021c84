import React, { FC, useState, useEffect } from 'react';
import * as RadixDialog from '@radix-ui/react-dialog';
import { motion, type Variants } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import {
  AI_MODELS,
  AI_MODEL_STORAGE_KEY,
  DEFAULT_MODEL,
  CONTEXT_ALTERNATIVE_MODELS,
  BIG_CONTEXT_WINDOW,
} from '~/utils/constants';
import { fetchCurrentUserPlan } from '~/lib/stores/billing';
import { fetchPlans } from '~/api/plansApi';
import styles from '~/ai/styles/ErrorTooLongDialog.module.scss';

interface Plan {
  id: string;
  monthly: {
    name: string;
    price: number;
    features: string[];
    [key: string]: any;
  } | null;
  yearly: {
    name: string;
    price: number;
    features: string[];
    [key: string]: any;
  } | null;
  [key: string]: any;
}

interface UpgradePackage {
  name: string;
  price: number | null;
  billingCycle: string;
}

interface ErrorTooLongDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onModelSwitch?: (modelValue: string) => void;
}

const transition = {
  duration: 0.15,
  ease: [0.4, 0, 0.2, 1],
};

const backdropVariants: Variants = {
  closed: {
    opacity: 0,
    transition,
  },
  open: {
    opacity: 1,
    transition,
  },
};

const dialogVariants: Variants = {
  closed: {
    x: '-50%',
    y: '-40%',
    scale: 0.96,
    opacity: 0,
    transition,
  },
  open: {
    x: '-50%',
    y: '-50%',
    scale: 1,
    opacity: 1,
    transition,
  },
};

const ErrorTooLongDialog: FC<ErrorTooLongDialogProps> = ({ isOpen, onClose, onModelSwitch }) => {
  const { t } = useTranslation('translation');
  const { t: c } = useTranslation('constants');
  const [hasLargeContextFeature, setHasLargeContextFeature] = useState<boolean | null>(null);
  const [isReady, setIsReady] = useState(false);
  const [upgradePackage, setUpgradePackage] = useState<UpgradePackage | null>(null);

  const getCurrentModel = () => {
    try {
      return localStorage.getItem(AI_MODEL_STORAGE_KEY) || DEFAULT_MODEL.value;
    } catch (e) {
      return DEFAULT_MODEL.value;
    }
  };

  const currentModelValue = getCurrentModel();
  const currentModel = AI_MODELS.find((model) => model.value === currentModelValue);
  const isDefaultModel = currentModel?.id === DEFAULT_MODEL.id;

  const [selectedModelValue, setSelectedModelValue] = useState<string | null>(null);

  const findUpgradePackage = async () => {
    try {
      const plans: Plan[] = await fetchPlans();

      const eligiblePlans = plans.filter((plan) => {
        const monthlyFeatures = plan.monthly?.features || [];
        const yearlyFeatures = plan.yearly?.features || [];

        return monthlyFeatures.includes(BIG_CONTEXT_WINDOW) || yearlyFeatures.includes(BIG_CONTEXT_WINDOW);
      });

      if (eligiblePlans.length === 0) {
        setUpgradePackage({ name: '', price: null, billingCycle: '' });
        return;
      }

      const sortedPlans = [...eligiblePlans].sort((a, b) => {
        if (a.monthly?.features.includes(BIG_CONTEXT_WINDOW) && b.monthly?.features.includes(BIG_CONTEXT_WINDOW)) {
          return a.monthly.price - b.monthly.price;
        }

        if (a.monthly?.features.includes(BIG_CONTEXT_WINDOW)) {
          return -1;
        }

        if (b.monthly?.features.includes(BIG_CONTEXT_WINDOW)) {
          return 1;
        }

        return (a.yearly?.price || Infinity) - (b.yearly?.price || Infinity);
      });

      const cheapestPlan = sortedPlans[0];

      if (cheapestPlan.monthly?.features.includes(BIG_CONTEXT_WINDOW)) {
        setUpgradePackage({
          name: cheapestPlan.monthly.name,
          price: cheapestPlan.monthly.price,
          billingCycle: 'monthly',
        });
      } else if (cheapestPlan.yearly?.features.includes(BIG_CONTEXT_WINDOW)) {
        setUpgradePackage({
          name: cheapestPlan.yearly.name,
          price: cheapestPlan.yearly.price,
          billingCycle: 'yearly',
        });
      } else {
        setUpgradePackage({ name: '', price: null, billingCycle: '' });
      }
    } catch (error) {
      console.error('Error fetching plans:', error);
      setUpgradePackage({ name: '', price: null, billingCycle: '' });
    }
  };

  useEffect(() => {
    let isMounted = true;

    const loadData = async () => {
      if (!isOpen) {
        setIsReady(false);
        return;
      }

      setIsReady(false);

      try {
        await Promise.allSettled([loadUserPlan(), findUpgradePackage()]);

        if (!isMounted) {
          return;
        }

        if (isDefaultModel && CONTEXT_ALTERNATIVE_MODELS.length > 0) {
          setSelectedModelValue(CONTEXT_ALTERNATIVE_MODELS[0].value);
        }

        setIsReady(true);
      } catch (error) {
        if (isMounted) {
          setHasLargeContextFeature(false);
          setIsReady(true);
        }
      }
    };

    const loadUserPlan = async () => {
      try {
        let userPlan;

        try {
          userPlan = await fetchCurrentUserPlan();
        } catch (error) {
          if (isMounted) {
            setHasLargeContextFeature(false);
          }

          return;
        }

        if (!userPlan) {
          if (isMounted) {
            setHasLargeContextFeature(false);
          }

          return;
        }

        const hasFeature = userPlan.features?.includes(BIG_CONTEXT_WINDOW);

        if (isMounted) {
          setHasLargeContextFeature(hasFeature || false);
        }
      } catch (error) {
        if (isMounted) {
          setHasLargeContextFeature(false);
        }
      }
    };

    loadData();

    return () => {
      isMounted = false;
    };
  }, [isOpen, isDefaultModel]);

  const switchToModel = () => {
    if (!selectedModelValue) {
      return;
    }

    const event = new CustomEvent('biela:changeAIModel', {
      detail: { modelValue: selectedModelValue },
    });
    window.dispatchEvent(event);

    if (onModelSwitch) {
      onModelSwitch(selectedModelValue);
    }

    onClose();
  };

  const handleUpgradeClick = () => {
    window.open('/settings', '_blank');
    onClose();
  };

  if (!isOpen || !isReady) {
    return null;
  }

  const getUpgradeText = () => {
    if (!upgradePackage || !upgradePackage.name) {
      return t('UpgradePlan', 'a premium plan');
    }

    return upgradePackage.name;
  };

  return (
    <RadixDialog.Root open={isOpen}>
      <RadixDialog.Portal>
        <RadixDialog.Overlay asChild>
          <motion.div
            className="bg-black/60 fixed inset-0 z-40 backdrop-blur-sm"
            initial="closed"
            animate="open"
            exit="closed"
            variants={backdropVariants}
            onClick={onClose}
          />
        </RadixDialog.Overlay>

        <RadixDialog.Content asChild>
          <motion.div
            onClick={(e) => e.stopPropagation()}
            className="fixed top-[50%] left-[50%] z-max max-h-[85vh] w-[90vw] max-w-[550px] translate-x-[-50%] translate-y-[-50%] rounded-xl bg-[#111727] shadow-2xl focus:outline-none overflow-hidden flex flex-col"
            initial="closed"
            animate="open"
            exit="closed"
            variants={dialogVariants}
          >
            <div className="relative flex-shrink-0">
              <div
                className={styles.etld_headerBar}
                style={{
                  background: currentModel ? `${currentModel.color}` : '#c084fc',
                }}
              />
              <div className="px-7 py-5">
                <h2 className="text-white text-xl font-medium flex items-center">
                  {currentModel && (
                    <span className="mr-3 opacity-90" style={{ color: currentModel.color, fontSize: '1.2rem' }}>
                      {currentModel.icon}
                    </span>
                  )}
                  {t('ContextLimitReached', 'Context Limit Reached')}
                </h2>
              </div>
            </div>

            <div className="border-b border-t border-gray-700/20 px-7 py-6 text-white/90 overflow-y-auto">
              {isDefaultModel ? (
                <>
                  <p className="mb-4 text-[15px] leading-relaxed">
                    {t(
                      'ClaudeContextDescription1',
                      "You've reached the context capacity for this project with Claude. Don't worry - we can continue by switching to a Gemini model with a significantly larger context window.",
                    )}
                  </p>
                  <p className="mb-5 text-[15px] leading-relaxed">
                    {t(
                      'ClaudeContextDescription2',
                      'Gemini models offer up to 5x more context capacity, allowing you to keep all your code, chat history, and continue building your project without interruption.',
                    )}
                  </p>

                  {hasLargeContextFeature === false && (
                    <div className="mt-3 mb-4 rounded-lg p-4 bg-[rgba(26,36,59,0.7)] border border-[rgba(255,255,255,0.06)]">
                      <div className="flex items-start">
                        <div
                          className={styles.etld_iconContainer}
                          style={{
                            backgroundColor: `${currentModel?.color}25`,
                          }}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            style={{ color: currentModel?.color || '#8B5CF6' }}
                          >
                            <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"></path>
                          </svg>
                        </div>
                        <div>
                          <p
                            className="text-white font-medium text-[15px] mb-1"
                            style={{ color: currentModel?.color || '#8B5CF6' }}
                          >
                            {t('PremiumFeatureRequired', 'Premium feature required')}
                          </p>
                          <p className="text-white/70 text-[13px] leading-relaxed">
                            {t(
                              'LargeContextUpgradeInfo',
                              'Large context window models require a higher subscription tier. Upgrade to unlock these models.',
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="mt-5 space-y-3">
                    <h3 className="text-white text-[15px] font-medium mb-3">
                      {hasLargeContextFeature
                        ? t('SelectModelToContinue', 'Select a model to continue:')
                        : t('PremiumModelsAvailable', 'Premium models available with upgrade:')}
                    </h3>

                    {CONTEXT_ALTERNATIVE_MODELS.map((alternativeModel) => {
                      const isModelDisabled = !hasLargeContextFeature;

                      return (
                        <div
                          key={alternativeModel.id}
                          className={`flex items-start p-3.5 rounded-lg ${
                            isModelDisabled ? 'cursor-not-allowed opacity-85' : 'cursor-pointer'
                          } transition-all duration-200 ${
                            selectedModelValue === alternativeModel.value && !isModelDisabled
                              ? 'ring-2 ring-offset-1 ring-offset-[#131b2e]'
                              : isModelDisabled
                                ? ''
                                : 'hover:bg-[#1d2842]'
                          }`}
                          style={{
                            backgroundColor:
                              selectedModelValue === alternativeModel.value && !isModelDisabled
                                ? `${alternativeModel.color}15`
                                : 'rgba(26, 36, 59, 0.7)',
                            ringColor: alternativeModel.color,
                            position: 'relative',
                          }}
                          onClick={() => {
                            if (!isModelDisabled) {
                              setSelectedModelValue(alternativeModel.value);
                            }
                          }}
                        >
                          {isModelDisabled && <div className={styles.etld_modelOverlay} />}

                          <div
                            className={styles.etld_modelIconContainer}
                            style={{
                              backgroundColor: `${alternativeModel.color}25`,
                            }}
                          >
                            <span className={styles.etld_modelIcon} style={{ color: alternativeModel.color }}>
                              {alternativeModel.icon}
                            </span>

                            {isModelDisabled && (
                              <span className={styles.etld_lockIconCorner} style={{ color: alternativeModel.color }}>
                                <svg
                                  width="14"
                                  height="14"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M19 11H5C3.89543 11 3 11.8954 3 13V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V13C21 11.8954 20.1046 11 19 11Z"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                  <path
                                    d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                </svg>
                              </span>
                            )}
                          </div>
                          <div style={{ position: 'relative', zIndex: 2 }}>
                            <div className="text-white font-medium text-[15px]">
                              {alternativeModel.name}

                              {isModelDisabled && (
                                <span className={styles.etld_lockIconInline} style={{ color: alternativeModel.color }}>
                                  <svg
                                    width="14"
                                    height="14"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      d="M19 11H5C3.89543 11 3 11.8954 3 13V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V13C21 11.8954 20.1046 11 19 11Z"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    />
                                    <path
                                      d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    />
                                  </svg>
                                </span>
                              )}
                            </div>
                            <div className="text-white/70 text-[13px] mt-0.5 leading-snug">
                              {c(alternativeModel.description.key, alternativeModel.description.defaultValue)}
                            </div>
                            <div className="mt-2 text-[12px] space-y-1.5">
                              <div className="flex items-center text-white/70">
                                <span
                                  className="inline-block w-1.5 h-1.5 rounded-full mr-1.5"
                                  style={{ backgroundColor: alternativeModel.color }}
                                ></span>
                                {c(
                                  alternativeModel.contextWindowInfo.key,
                                  alternativeModel.contextWindowInfo.defaultValue,
                                )}
                              </div>
                              <div className="flex items-center text-white/70">
                                <span
                                  className="inline-block w-1.5 h-1.5 rounded-full mr-1.5"
                                  style={{ backgroundColor: alternativeModel.color }}
                                ></span>
                                {c(alternativeModel.performance.key, alternativeModel.performance.defaultValue)}{' '}
                                {t('Performance', 'performance')}
                              </div>
                            </div>

                            {isModelDisabled && (
                              <div
                                className={styles.etld_upgradeTooltip}
                                style={{
                                  backgroundColor: `${alternativeModel.color}15`,
                                  borderColor: `${alternativeModel.color}30`,
                                }}
                              >
                                <div className={styles.etld_upgradeIcon} style={{ color: alternativeModel.color }}>
                                  <svg
                                    width="14"
                                    height="14"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"
                                      fill="currentColor"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    />
                                  </svg>
                                </div>
                                <span style={{ color: alternativeModel.color, display: 'flex', alignItems: 'center' }}>
                                  {t('UpgradeTooltip', 'Unlock by upgrading to ')}
                                  <strong className={styles.etld_packageName} style={{ color: alternativeModel.color }}>
                                    {getUpgradeText()}
                                  </strong>
                                  <span className={styles.etld_packageSuffix}>
                                    {t('UpgradeTooltipSuffix', 'package')}
                                  </span>
                                </span>
                              </div>
                            )}
                          </div>
                          {selectedModelValue === alternativeModel.value && !isModelDisabled && (
                            <div className="ml-auto">
                              <div
                                className={styles.etld_checkCircle}
                                style={{ backgroundColor: alternativeModel.color }}
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="12"
                                  height="12"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="3"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="text-white"
                                >
                                  <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </>
              ) : (
                <div className="space-y-5">
                  <p className="text-[15px] leading-relaxed text-white">
                    {t(
                      'AlternativeLimitExplanation',
                      'It looks like the AI has hit the processing limit for this project. Most of the space is being used by the imported files, not the chat itself.',
                    )}
                  </p>

                  <div className="bg-[#1A243B] rounded-lg p-5 border border-gray-700/20">
                    <h3 className="text-white text-[15px] font-medium mb-3">
                      {t('SuggestedSolutions', 'Suggested solutions:')}
                    </h3>

                    <div className="space-y-4">
                      <div className="flex">
                        <div
                          className="w-6 h-6 rounded-md flex items-center justify-center mr-3 mt-0.5 flex-shrink-0"
                          style={{ backgroundColor: `${currentModel?.color}15` }}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="14"
                            height="14"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            style={{ color: currentModel?.color }}
                          >
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                          </svg>
                        </div>
                        <div>
                          <p className="text-white text-[15px]">{t('ReimportProject', 'Reimport as a new project')}</p>
                          <p className="text-white/75 text-[14px] leading-relaxed">
                            {t(
                              'ReimportProjectDescription',
                              'This will clear the chat history and free up some context space, while preserving your files.',
                            )}
                          </p>
                        </div>
                      </div>

                      <div className="flex">
                        <div
                          className="w-6 h-6 rounded-md flex items-center justify-center mr-3 mt-0.5 flex-shrink-0"
                          style={{ backgroundColor: `${currentModel?.color}15` }}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="14"
                            height="14"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            style={{ color: currentModel?.color }}
                          >
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                            <line x1="8" y1="12" x2="16" y2="12"></line>
                            <line x1="12" y1="16" x2="12" y2="16"></line>
                            <line x1="12" y1="8" x2="12" y2="8"></line>
                          </svg>
                        </div>
                        <div>
                          <p className="text-white text-[15px]">
                            {t('BreakIntoProjects', 'Break into multiple projects')}
                          </p>
                          <p className="text-white/75 text-[14px] leading-relaxed">
                            {t(
                              'BreakIntoProjectsDescription',
                              'Split your work into smaller components that can be developed separately.',
                            )}
                          </p>
                        </div>
                      </div>

                      <div className="flex">
                        <div
                          className="w-6 h-6 rounded-md flex items-center justify-center mr-3 mt-0.5 flex-shrink-0"
                          style={{ backgroundColor: `${currentModel?.color}15` }}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="14"
                            height="14"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            style={{ color: currentModel?.color }}
                          >
                            <path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242"></path>
                            <path d="M12 12v9"></path>
                            <path d="m8 17 4 4 4-4"></path>
                          </svg>
                        </div>
                        <div>
                          <p className="text-white text-[15px]">{t('ExportWork', 'Export completed work')}</p>
                          <p className="text-white/75 text-[14px] leading-relaxed">
                            {t(
                              'ExportWorkDescription',
                              'Download and archive finished files to free up context space.',
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <p className="text-[14px] leading-relaxed text-white/75">
                    {t(
                      'AlternativeContextNote',
                      'To get the most out of your available context, consider removing any unused files or libraries and focusing on the core files needed for the current development phase.',
                    )}
                  </p>
                </div>
              )}
            </div>

            <div className="px-7 py-5 flex gap-3 justify-end bg-[#0d1526] flex-shrink-0">
              {isDefaultModel ? (
                <>
                  <button
                    onClick={onClose}
                    className="px-5 py-2.5 rounded-md text-white/80 hover:text-white/100 bg-[#1A243B] hover:bg-[#1E2A45] transition-colors text-sm font-medium"
                  >
                    {t('cancel', 'Cancel')}
                  </button>

                  {hasLargeContextFeature ? (
                    selectedModelValue && (
                      <button
                        onClick={switchToModel}
                        className="px-5 py-2.5 rounded-md text-white transition-colors text-sm font-medium flex items-center"
                        style={{
                          backgroundColor: currentModel?.color || '#4ADE80',
                        }}
                      >
                        {t('ContinueWithSelectedModel', 'Continue with selected model')}
                      </button>
                    )
                  ) : (
                    <button
                      onClick={handleUpgradeClick}
                      className="px-5 py-2.5 rounded-md text-white transition-colors text-sm font-medium flex items-center"
                      style={{
                        backgroundColor: currentModel?.color || '#4ADE80',
                      }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="mr-2"
                      >
                        <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"></path>
                      </svg>
                      {t('UpgradeToContinue', 'Upgrade to continue')}
                    </button>
                  )}
                </>
              ) : (
                <button
                  onClick={onClose}
                  className="px-6 py-2.5 rounded-md text-white transition-colors text-sm font-medium"
                  style={{ backgroundColor: currentModel?.color || '#4ADE80' }}
                >
                  {t('Close', 'Close')}
                </button>
              )}
            </div>
          </motion.div>
        </RadixDialog.Content>
      </RadixDialog.Portal>
    </RadixDialog.Root>
  );
};

export default ErrorTooLongDialog;
