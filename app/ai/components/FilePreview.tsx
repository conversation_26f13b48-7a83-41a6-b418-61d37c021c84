import React, { useEffect, useRef, useState } from 'react';

interface FilePreviewProps {
  files: File[];
  imageDataList: string[];
  onRemove: (index: number) => void;
}

// Utility Function to get File Preview:
const getFilePreview = (file: File) => {
  const fileType = file.type;
  const fileName = file.name.slice(0, 4).toLocaleLowerCase();
  const fileExtension = file.name.split('.').pop()?.toLocaleLowerCase() || '';

  const fileIcon: { [key: string]: string } = {
    pdf: '/pdf-svgrepo-com.svg',
    word: '/txt-svgrepo-com.svg',
    excel: '/xls-svgrepo-com.svg',
    zip: '/rar-svgrepo-com.svg',
    rar: '/rar-svgrepo-com.svg',
    text: '/txt-svgrepo-com.svg',
    json: '/txt-svgrepo-com.svg',
    html: '/html-svgrepo-com.svg',
    css: '/css-svgrepo-com.svg',
    js: '/js-svgrepo-com.svg',
    default: '/default-icon.png',
  };

  const fileKey = Object.keys(fileIcon).find((key) => fileType.includes(key)) || 'default';
  return {
    icon: fileIcon[fileKey],
    displayName: `${fileName}.${fileExtension}`,
  };
};

const FilePreview: React.FC<FilePreviewProps> = ({ files, imageDataList, onRemove }) => {
  if (!files || files.length === 0) {
    return null;
  }

  const [hoverStates, setHoverStates] = useState({});
  const hoverTimersRef = useRef({});

  const handleImageHover = (key) => {
    if (hoverTimersRef.current[key]) {
      clearTimeout(hoverTimersRef.current[key]);
    }
    hoverTimersRef.current[key] = setTimeout(() => {
      setHoverStates(prev => ({ ...prev, [key]: true }));
    }, 1000);
  };

  useEffect(() => {
    return () => {
      Object.values(hoverTimersRef.current).forEach(timer => clearTimeout(timer));
    };
  }, []);

  const handleImageLeave = (key) => {
    if (hoverTimersRef.current[key]) {
      clearTimeout(hoverTimersRef.current[key]);
      delete hoverTimersRef.current[key];
    }
    setHoverStates(prev => ({ ...prev, [key]: false }));
  };

  return (
    <div className="flex flex-row overflow-x-auto show-prompt">
      {files.map((file, index) => {
        const fileType = file.type;
        const fileUrl = imageDataList[index];
        const { icon, displayName } = getFilePreview(file);
        const key = file.name + file.size;
        return (
          <div key={key} className="mr-2"
               onMouseEnter={() => handleImageHover(key)}
                onMouseLeave={() => handleImageLeave(key)}
            >
            {fileUrl && (
              <div className="relative p-0 m-6">
                <div className="rounded-xl h-16 w-16">
                  {fileType.startsWith('image/') ? (
                    <img src={fileUrl} alt={file.name} className="h-full w-full rounded-xl object-cover" />
                  ) : (
                    <a
                      href={fileUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex flex-col gap-2 items-center justify-center w-full h-full"
                    >
                      <img src={icon} alt={file.name} className="size-8 object-cover" />
                      <p className="text-xs truncate w-full text-center text-gray-400">{displayName}</p>
                    </a>
                  )}
                </div>
                <button
                  onClick={() => onRemove(index)}
                  className="absolute -top-1 -right-1.5 z-10 bg-gray-200 rounded-full border border-transparent w-5 h-5 shadow-md hover:bg-white transition-colors flex items-center justify-center"
                >
                  <div className="i-ph:x w-4 h-4 text-black" />
                </button>
              </div>
            )}
            {hoverStates[key] && (
              <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[1000]">
                <div
                  className="relative max-h-[80%] m-auto"
                  style={{
                    transformOrigin: 'bottom left',
                    animation: 'preview-expand 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
                  }}
                >
                  <img
                    src={fileUrl}
                    alt="Preview"
                    className="max-w-[380px] max-h-[275px] object-contain rounded-lg"
                  />
                  <button
                    onClick={() => handleImageLeave(key)}
                    className="absolute -top-2 -right-2 bg-gray-200 rounded-full border border-transparent w-5 h-5 shadow-md hover:bg-white transition-colors flex items-center justify-center"
                  >
                    <div className="i-ph:x w-4 h-4 text-black" />
                  </button>
                </div>
                <style>
                  {`
          @keyframes preview-expand {
            from {
              transform: scale(0);
              opacity: 0;
            }
            to {
              transform: scale(1);
              opacity: 1;
            }
          }
        `}
                </style>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default FilePreview;
