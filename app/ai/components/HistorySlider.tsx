import { motion } from 'framer-motion';
import { memo } from 'react';
import { classNames } from '~/utils/classNames';
import { cubicEasingFn } from '~/utils/easings';
import { genericMemo } from '~/utils/react';
import { ChatBubbleLeftIcon, CircleStackIcon } from '@heroicons/react/24/outline';

interface HistorySliderOption<T> {
  value: T;
  text: string;
}

export interface HistorySliderOptions<T> {
  all: HistorySliderOption<T>;
  database: HistorySliderOption<T>;
  code: HistorySliderOption<T>;
}

interface HistorySliderProps<T> {
  selected: T;
  options: HistorySliderOptions<T>;
  setHistorySelected?: (selected: T) => void;
}

export const HistorySlider = genericMemo(<T,>({
                                                selected,
                                                options,
                                                setHistorySelected,
                                              }: HistorySliderProps<T>) => {
  const isAllSelected = selected === options.all.value;
  const isDatabaseSelected = selected === options.database.value;
  const isCodeSelected = selected === options.code.value;

  return (
   <div className="flex">
     <div className="flex items-center gap-1.5 show-prompt rounded-md p-1 overflow-hidden">
       <HistorySliderButton
         variant="all"
         selected={isAllSelected}
         setHistorySelected={() => setHistorySelected?.(options.all.value)}
       >
         <span className="flex items-center gap-2">
           <ChatBubbleLeftIcon className="w-4 h-4" />

           <span className="text-[13px] font-normal tracking-[0.4px]">{options.all.text}</span>
         </span>
       </HistorySliderButton>

       <HistorySliderButton
         variant="database"
         selected={isDatabaseSelected}
         setHistorySelected={() => setHistorySelected?.(options.database.value)}
       >
       <span className="flex items-center gap-2">
           <CircleStackIcon className="w-4 h-4" />

         <span className="text-[13px] font-normal tracking-[0.4px]">{options.database.text}</span>
       </span>
       </HistorySliderButton>

       <HistorySliderButton
         variant="code"
         selected={isCodeSelected}
         setHistorySelected={() => setHistorySelected?.(options.code.value)}
       >
        <span className="flex items-center gap-2">
           <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
           <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                 d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
         </svg>
         <span className="text-[13px] font-normal tracking-[0.4px]">{options.code.text}</span>
        </span>
       </HistorySliderButton>
     </div>

   </div>
  );
});

interface HistorySliderButtonProps {
  selected: boolean;
  variant: 'all' | 'database' | 'code';
  children: JSX.Element;
  setHistorySelected: () => void;
}

const HistorySliderButton = memo(({
                                    selected,
                                    variant,
                                    children,
                                    setHistorySelected,
                                  }: HistorySliderButtonProps) => {
  const baseClasses = 'text-sm px-2.5 py-2 rounded-md relative hover:bg-white/5';

  // Define colors for each variant
  let selectedClasses = '';
  let bgColor = '';
  switch (variant) {
    case 'all':
      selectedClasses = 'text-[#010101] bg-[#1E90FF]'; // Dodger Blue for All
      bgColor = '#4ADE80';
      break;
    case 'database':
      selectedClasses = 'text-[#010101] bg-[#FFA500]'; // Orange for Database
      bgColor = '#4ADE80';
      break;
    case 'code':
      selectedClasses = 'text-[#010101] bg-[#4ADE80]'; // Green for Code
      bgColor = '#4ADE80';
      break;
    default:
      selectedClasses = '';
      bgColor = '';
  }

  const notSelectedClasses = 'text-white/70 bg-transparent';

  return (
    <button
      onClick={setHistorySelected}
      className={classNames(
        baseClasses,
        selected ? selectedClasses : notSelectedClasses
      )}
    >
      <span className="relative z-10 flex">{children}</span>
      {selected && (
        <motion.span
          layoutId="pill-tab-history"
          transition={{ duration: 0.2, ease: cubicEasingFn }}
          className="absolute inset-0 z-0 rounded-md"
          style={{ backgroundColor: bgColor }}
        ></motion.span>
      )}
    </button>
  );
});
