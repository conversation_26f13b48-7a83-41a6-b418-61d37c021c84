import { useStore } from '@nanostores/react';
import { memo } from 'react';
import { historyStore, setHistoryMode } from '~/ai/lib/stores/history';


import { HistorySlider, type HistorySliderOptions } from '~/ai/components/HistorySlider';

const modeOptions: HistorySliderOptions<'all' | 'database' | 'code'> = {
  all: {
    value: 'all',
    text: 'All',
  },
  database: {
    value: 'database',
    text: 'Database',
  },
  code: {
    value: 'code',
    text: 'Code',
  },
};

export const HistorySwitch = memo(() => {
  const mode = useStore(historyStore).mode;

  return (
    <HistorySlider
      selected={mode}
      options={modeOptions}
      setHistorySelected={(mode) => setHistoryMode(mode)}
    />
  );
});
