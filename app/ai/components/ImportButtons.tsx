import type { Message } from 'ai';
import { toast } from 'react-toastify';
import { ImportFolderButtonClient } from '~/ai/components/ImportFolderButton.client';

type ChatData = {
  messages?: Message[]; // Standard biela format
  description?: string; // Optional description
};

export function ImportButtons(importChat: ((description: string, messages: Message[]) => Promise<void>) | undefined) {
  return (
    <div className="flex flex-col items-center justify-center w-auto mb-[10px]">
      <input
        type="file"
        id="chat-import"
        className="hidden"
        accept=".json"
        onChange={async (e) => {
          const file = e.target.files?.[0];

          if (file && importChat) {
            try {
              const reader = new FileReader();

              reader.onload = async (e) => {
                try {
                  const content = e.target?.result as string;
                  const data = JSON.parse(content) as ChatData;

                  // Standard format
                  if (Array.isArray(data.messages)) {
                    await importChat(data.description || 'Imported Chat', data.messages);
                    return;
                  }

                  toast.error('Invalid chat file format');
                } catch (error: unknown) {
                  if (error instanceof Error) {
                    toast.error('Failed to parse chat file: ' + error.message);
                  } else {
                    toast.error('Failed to parse chat file');
                  }
                }
              };
              reader.onerror = () => toast.error('Failed to read chat file');
              reader.readAsText(file);
            } catch (error) {
              toast.error(error instanceof Error ? error.message : 'Failed to import chat');
            }
            e.target.value = ''; // Reset file input
          } else {
            toast.error('Something went wrong');
          }
        }}
      />
      <div className="flex flex-col items-center gap-4 max-w-2xl text-center">
        <div className="flex gap-2">
          <button
            onClick={() => {
              const input = document.getElementById('chat-import');
              input?.click();
            }}
            className="px-4 py-2 font-light rounded-lg border border-biela-elements-borderColor bg-biela-elements-prompt-background text-biela-elements-textPrimary hover:bg-biela-elements-background-depth-3 transition-all flex items-center gap-2"
          >
            <div className="i-ph:upload-simple " />
            Import Chat
          </button>
          <ImportFolderButtonClient
            importChat={importChat}
            className="px-4 py-2 font-light rounded-lg border border-biela-elements-borderColor bg-biela-elements-prompt-background text-biela-elements-textPrimary hover:bg-biela-elements-background-depth-3 transition-all flex items-center gap-2"
          />
        </div>
      </div>
    </div>
  );
}
