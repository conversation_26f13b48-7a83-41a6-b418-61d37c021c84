import React, { useState } from 'react';
import type { Message } from 'ai';
import { toast } from 'react-toastify';
import { isBinaryFile, shouldIncludeFile } from '~/utils/fileUtils';
import { createChatFromFolder } from '~/utils/folderImport';
import { logStore } from '~/ai/lib/stores/logs';
import { useTranslation } from 'react-i18next';
import { useChatHistory } from '~/ai/lib/persistence';

interface ImportFolderButtonProps {
  className?: string;
  importChat?: (description: string, messages: Message[]) => Promise<void>;
  projectName: string;
  showNameError: boolean;
  setShowNameError: (value: boolean) => void;
}

export const ImportFolderButtonClient: React.FC<ImportFolderButtonProps> = ({
  className,
  importChat,
  projectName,
  showNameError,
  setShowNameError,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useTranslation('translation');
  const { importChat: importChatHistory } = useChatHistory();

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setShowNameError(false); // clear error

    const allFiles = Array.from(e.target.files || []);
    const filteredFiles = allFiles.filter((file) => {
      const path = file.webkitRelativePath.split('/').slice(1).join('/');
      return shouldIncludeFile(path);
    });

    if (filteredFiles.length === 0) {
      toast.error(t('noFilesFound', 'No file found in the Selected folder!'));
      logStore.logError('File import failed - no valid files', new Error('No valid files found'), {
        folderName: 'Unknown Folder',
      });
      return;
    }

    const folderName = filteredFiles[0]?.webkitRelativePath.split('/')[0] || 'Unknown Folder';
    setIsLoading(true);

    try {
      const fileChecks = await Promise.all(
        filteredFiles.map(async (file) => ({
          file,
          isBinary: await isBinaryFile(file),
        })),
      );

      const textFiles = fileChecks.filter((f) => !f.isBinary).map((f) => f.file);
      const binaryFilePaths = fileChecks
        .filter((f) => f.isBinary)
        .map((f) => f.file.webkitRelativePath.split('/').slice(1).join('/'));

      if (textFiles.length === 0) {
        toast.error(t('noTextFiles', 'No text files found in the selected folder'));
        logStore.logError('File import failed - no text files', new Error('No text files found'), { folderName });
        return;
      }

      if (binaryFilePaths.length > 0) {
        toast.info(t('skippingBinary', `Skipping ${binaryFilePaths.length} binary files`));
        logStore.logWarning('Skipping binary files during import', {
          folderName,
          binaryCount: binaryFilePaths.length,
        });
      }

      const messages = await createChatFromFolder(textFiles, binaryFilePaths, folderName);

      const description = projectName || folderName;
      const finalImportChat = importChat || importChatHistory;

      if (finalImportChat) {
        await finalImportChat(description, [...messages], true);
      } else {
        logStore.logSystem('Folder imported successfully', { folderName });
      }
    } catch (error) {
      toast.error(t('folderImportFailed', 'Failed to import folder'));
      logStore.logError('Failed to import folder', error, { folderName });
      console.error('Failed to import folder:', error);
    } finally {
      setIsLoading(false);
      e.target.value = ''; // Reset file input
    }
  };

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!projectName.trim()) {
      setShowNameError(true);
      return;
    }

    setShowNameError(false);
    const input = document.getElementById('folder-import') as HTMLInputElement;
    input?.click();
  };

  return (
    <div className="flex flex-col w-full">
      <input
        type="file"
        id="folder-import"
        className="hidden"
        webkitdirectory=""
        directory=""
        onChange={handleFileChange}
        {...({} as any)}
      />
      <button
        type="button"
        onClick={handleClick}
        className={`${className} ${isLoading ? 'cursor-not-allowed opacity-50' : ''}`}
        disabled={isLoading}
      >
        <div className="i-ph:upload-simple" />
        {isLoading ? t('importing', 'Importing...') : t('importFolder', 'Import Folder')}
      </button>
     
    </div>
  );
};
