import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useStore } from "@nanostores/react";
import { workbenchStore } from "~/ai/lib/stores/workbench";
import type { ActionState } from "~/ai/lib/runtime/action-runner";

const LoadingAnimation: React.FC = () => {
  // 1) Typing dots
  const [dots, setDots] = useState(".");
  useEffect(() => {
    const iv = setInterval(() => {
      setDots((prev) => (prev.length >= 3 ? "." : prev + "."));
    }, 500);
    return () => clearInterval(iv);
  }, []);

  // 2) “<PERSON><PERSON><PERSON> is working on your project” typing
  const fullText = "<PERSON><PERSON><PERSON> is working on your project";
  const [displayText, setDisplayText] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTypingComplete, setIsTypingComplete] = useState(false);
  useEffect(() => {
    if (currentIndex < fullText.length) {
      const t = setTimeout(() => {
        setDisplayText((prev) => prev + fullText[currentIndex]);
        setCurrentIndex((i) => i + 1);
      }, 100);
      return () => clearTimeout(t);
    } else {
      setIsTypingComplete(true);
    }
  }, [currentIndex]);

  // 3) Get the latest artifact and its latest file action
  const artifactsMap = useStore(workbenchStore.artifacts);
  const artifactKeys = Object.keys(artifactsMap);
  const latestArtifactKey = artifactKeys.length > 0 ? artifactKeys[artifactKeys.length - 1] : null;
  const [currentFileAction, setCurrentFileAction] = useState<string | null>(null);

  // Subscribe to the actions of the latest artifact to get the latest file action
  useEffect(() => {
    if (!latestArtifactKey || !artifactsMap[latestArtifactKey]) {
      setCurrentFileAction(null);
      return;
    }

    const artifact = artifactsMap[latestArtifactKey];
    const runnerStore = artifact?.runner?.actions;

    if (!runnerStore || typeof runnerStore.get !== "function") {
      setCurrentFileAction(null);
      return;
    }

    // Function to find the latest running file action
    const findLatestFileAction = () => {
      const actionsObj = runnerStore.get();
      const fileActions = Object.values(actionsObj)
        .filter(a => a.type === "file");

      // First try to find a running file action
      const runningAction = fileActions.find(a => a.status === "running");

      if (runningAction) {
        const actionText = `${runningAction.mode === "update" ? "Update" : "Create"} ${runningAction.filePath}`;
        setCurrentFileAction(actionText);
        return;
      }

      // If no running action, use the last file action
      if (fileActions.length > 0) {
        const lastAction = fileActions[fileActions.length - 1];
        const actionText = `${lastAction.mode === "update" ? "Update" : "Create"} ${lastAction.filePath}`;
        setCurrentFileAction(actionText);
      } else {
        setCurrentFileAction(null);
      }
    };

    // Initial computation
    findLatestFileAction();

    // Subscribe to changes
    const unsubscribe = runnerStore.subscribe(findLatestFileAction);

    return () => {
      unsubscribe();
    };
  }, [latestArtifactKey, artifactsMap]);

  return (
    <div className="flex flex-col items-center justify-center p-4 bg-[#0A0F1C]">
      <div className="text-center max-w-md">
        <div className="relative mb-8">
          {/* Robot head */}
          <motion.div
            className="w-24 h-24 mx-auto bg-green-500 rounded-t-3xl relative"
            animate={{ y: [0, -10, 0] }}
            transition={{ repeat: Infinity, duration: 2 }}
          >
            {/* Eyes */}
            <div className="absolute top-8.5 left-6.5 w-4.5 h-4.5 bg-white rounded-full">
              <motion.div
                className="w-2 h-2 bg-black rounded-full relative top-1.2 left-1.2"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ repeat: Infinity, duration: 2 }}
              />
            </div>
            <div className="absolute top-8.5 right-6.5 w-4.5 h-4.5 bg-white rounded-full">
              <motion.div
                className="w-2 h-2 bg-black rounded-full relative top-1.2 left-1.2"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ repeat: Infinity, duration: 2, delay: 0.1 }}
              />
            </div>

            {/* Mouth */}
            <motion.div
              className="absolute bottom-6 left-1/2 transform -translate-x-1/2 w-12 h-2 bg-white rounded-lg"
              animate={{ width: [48, 32, 48], height: [8, 5, 8] }}
              transition={{ repeat: Infinity, duration: 1.5 }}
            />

            {/* Antenna */}
            <motion.div
              className="absolute -top-5 left-1/2 transform -translate-x-1/2 w-2 h-5 bg-green-700"
              animate={{ rotateZ: [-5, 5, -5] }}
              transition={{ repeat: Infinity, duration: 1.5 }}
            >
              <motion.div
                className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-green-400-light rounded-full"
                animate={{
                  boxShadow: [
                    "0 0 5px #4ade80",
                    "0 0 15px #4ade80",
                    "0 0 5px #4ade80"
                  ]
                }}
                transition={{ repeat: Infinity, duration: 1.5 }}
              />
            </motion.div>
          </motion.div>

          {/* Robot body */}
          <div className="w-42 h-12 mx-auto bg-green-600 rounded-b-xl relative">
            {/* Buttons */}
            <motion.div
              className="absolute top-4.5 left-4.5 w-3 h-3 bg-green-500 rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ repeat: Infinity, duration: 1, delay: 0.2 }}
            />
            <motion.div
              className="absolute top-4.5 left-12.5 w-3 h-3 bg-green-500 rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ repeat: Infinity, duration: 1, delay: 0.4 }}
            />
            <motion.div
              className="absolute top-4.5 right-12.5 w-3 h-3 bg-green-500 rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ repeat: Infinity, duration: 1, delay: 0.6 }}
            />
            <motion.div
              className="absolute top-4.5 right-4.5 w-3 h-3 bg-green-500 rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ repeat: Infinity, duration: 1, delay: 0.8 }}
            />
          </div>
        </div>

        <div
          className="text-sm text-green-600 mb-6 relative font-mono
                     bg-gray-50 p-3 rounded-md border border-gray-200
                     inline-block min-w-[280px]"
        >
          <span>$ </span>
          <span>{displayText}</span>
          {isTypingComplete && <span className="absolute">{dots}</span>}
          <motion.span
            className={`inline-block w-2 h-4 bg-green-700 ml-1${isTypingComplete ? ' ml-6' : ''}`}
            animate={{ opacity: [1, 0, 1] }}
            transition={{ repeat: Infinity, duration: 1 }}
          />
        </div>

        {currentFileAction && (
          <motion.p
            key={`file-action-${currentFileAction}`}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            className="text-sm text-white italic"
          >
            {currentFileAction}
          </motion.p>
        )}

        {/* Progress bar */}
        <div className="w-full h-2 bg-gray-200 rounded-full mt-6 overflow-hidden">
          <motion.div
            className="h-full bg-gradient-to-r from-green-400 to-green-600"
            initial={{ width: "0%" }}
            animate={{ width: "100%" }}
            transition={{
              repeat: Infinity,
              duration: 2,
              ease: "easeInOut",
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default LoadingAnimation;
