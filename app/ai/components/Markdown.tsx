import { memo, useEffect, useMemo, useRef, useState } from 'react';
import ReactMarkdown, { type Components } from 'react-markdown';
import { createScopedLogger } from '~/utils/logger';
import { allowedHTMLElements, rehypePlugins, remarkPlugins } from '~/utils/markdown';
import { Artifact } from './Artifact';

import styles from '../styles/Markdown.module.scss';
import { AnimatePresence, motion } from 'framer-motion';
import { useStore } from '@nanostores/react';
import { chatStore } from '~/ai/lib/stores/chat';

const logger = createScopedLogger('MarkdownComponent');

interface MarkdownProps {
  children: string;
  html?: boolean;
  limitedMarkdown?: boolean;

  // If you need userMessage, add it to the interface as well:
  userMessage?: boolean;
}

export const Markdown = memo(
  ({ children, userMessage = false, html = false, limitedMarkdown = false }: MarkdownProps) => {
    logger.trace('Render');

    const { showPrompt } = useStore(chatStore);

    if (typeof children !== 'string') {
      console.error('Invalid Markdown input:', children);
      return null;
    }

    // Check if the content contains an artifact.
    const hasArtifact = children.includes('__bielaArtifact__');

    // Prepare content by stripping code fences if necessary.
    const content = stripCodeFenceFromArtifact(children);

    const components = useMemo(() => {
      localStorage.setItem('isBouncing', 'false');
      return {
        div: ({ className, children, node, ...props }) => {
          if (className?.includes('__bielaArtifact__')) {
            const messageId = node?.properties.dataMessageId as string;

            if (!messageId) {
              logger.error(`Invalid message id ${messageId}`);
            }

            return <Artifact messageId={messageId} />;
          }

          return (
            <div
              className={`${className}  ${!hasArtifact && !showPrompt && 'no-artefact'} ${userMessage && '!p-0 !border-0'}`}
              {...props}
            >
              {!hasArtifact && !userMessage && (
                <p className="message-content-title text-sm font-medium text-white/90">Message Content</p>
              )}
              <AnimatePresence>
                {userMessage ? (
                  <motion.div
                    className="actions"
                    initial={{ height: 0 }}
                    animate={{ height: 'auto' }}
                    exit={{ height: '0px' }}
                    transition={{ duration: 0.15 }}
                  >
                    {children}
                  </motion.div>
                ) : showPrompt ? (
                  <motion.div
                    className="actions"
                    initial={{ height: 0 }}
                    animate={{ height: 'auto' }}
                    exit={{ height: '0px' }}
                    transition={{ duration: 0.15 }}
                  >
                    {children}
                  </motion.div>
                ) : hasArtifact ? (
                  <motion.div
                    className={`actions ${hasArtifact && !showPrompt && 'has-artefact'}`}
                    initial={{ height: 0 }}
                    animate={{ height: 'auto' }}
                    exit={{ height: '0px' }}
                    transition={{ duration: 0.15 }}
                  >
                    {children}
                  </motion.div>
                ) : null}
              </AnimatePresence>
            </div>
          );
        },
        p: ({ children, ...props }) => (
          <p className={'text-sm text-white/70 leading-relaxed'} {...props}>
            {children}
          </p>
        ),
        pre: (props) => {
          const { children, ...rest } = props;
          const preRef = useRef<HTMLPreElement>(null);
          const [autoScroll, setAutoScroll] = useState(false);

          useEffect(() => {
            const pre = preRef.current;

            if (!pre) {
              return;
            }

            setAutoScroll(pre.scrollWidth > pre.clientWidth);

            pre.scrollLeft = 0;
          }, [children]);

          useEffect(() => {
            const shikiPres = document.querySelectorAll('pre.shiki');
            shikiPres.forEach((pre) => {
              const onEnter = () => {
                if (pre.scrollWidth > pre.clientWidth) {
                  pre.scrollTo({ left: pre.scrollWidth, behavior: 'smooth' });
                }
              };
              const onLeave = () => {
                pre.scrollTo({ left: 0, behavior: 'smooth' });
              };
              pre.addEventListener('mouseenter', onEnter);
              pre.addEventListener('mouseleave', onLeave);

              return () => {
                pre.removeEventListener('mouseenter', onEnter);
                pre.removeEventListener('mouseleave', onLeave);
              };
            });
          }, []);

          const animationRef = useRef<number | null>(null);

          const animateScroll = (element: HTMLElement, to: number, duration = 1200) => {
            if (animationRef.current) {
              cancelAnimationFrame(animationRef.current);
            }

            const start = element.scrollLeft;
            const change = to - start;
            const startTime = performance.now();

            function animate(time: number) {
              const elapsed = time - startTime;
              const progress = Math.min(elapsed / duration, 1);
              element.scrollLeft = start + change * progress;

              if (progress < 1) {
                animationRef.current = requestAnimationFrame(animate);
              } else {
                animationRef.current = null;
              }
            }
            animationRef.current = requestAnimationFrame(animate);
          };

          const handleMouseEnter = () => {
            const pre = preRef.current;

            if (pre && pre.scrollWidth > pre.clientWidth) {
              animateScroll(pre, pre.scrollWidth, 4000);
            }
          };

          const handleMouseLeave = () => {
            const pre = preRef.current;

            if (animationRef.current) {
              cancelAnimationFrame(animationRef.current);
              animationRef.current = null;
            }

            if (pre) {
              animateScroll(pre, 0, 800);
            }
          };

          return (
            <pre
              ref={preRef}
              className={autoScroll ? 'auto-scroll' : ''}
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
              {...rest}
            >
              {children}
            </pre>
          );
        },
        a: (props) => {
          const { children, ...rest } = props;
          return (
            <a {...rest} target="_blank" className={'text-sm text-white/70 leading-relaxed'} rel="noopener noreferrer">
              {children}
            </a>
          );
        },
        ul: ({ children, ...props }) => (
          <ul
            className={'mb-4 manrope'}
            style={{ marginBottom: '16px', borderColor: 'rgba(255, 255, 255, 0.05)' }}
            {...props}
          >
            {children}
          </ul>
        ),
        ol: ({ children, ...props }) => (
          <ol className="manrope mb-4" style={{ borderColor: 'rgba(255, 255, 255, 0.05)' }} {...props}>
            {children}
          </ol>
        ),
        li: ({ children, ...props }) => (
          <li className={'text-sm leading-relaxed'} style={{ padding: '4px' }} {...props}>
            {children}
          </li>
        ),
        h1: ({ children, ...props }) => (
          <h2 className={'text-sm leading-relaxed'} style={{ marginBottom: '10px' }} {...props}>
            {children}
          </h2>
        ),
        h2: ({ children, ...props }) => (
          <h3 className={'text-sm leading-relaxed'} {...props}>
            {children}
          </h3>
        ),
        h3: ({ children, ...props }) => (
          <h4 className={'text-sm leading-relaxed'} {...props}>
            {children}
          </h4>
        ),
      } as Components;
    }, [userMessage, showPrompt, hasArtifact]);

    return (
      <>
        {/* If no artifact is detected, show a title */}
        {/*{!hasArtifact && !userMessage &&  <h1 className="message-content-title text-white">Message Content</h1>}*/}
        <ReactMarkdown
          allowedElements={allowedHTMLElements}
          className={styles.MarkdownContent}
          components={components}
          remarkPlugins={remarkPlugins(!!limitedMarkdown)}
          rehypePlugins={rehypePlugins(!!html)}
        >
          {content}
        </ReactMarkdown>
      </>
    );
  },
);

export const stripCodeFenceFromArtifact = (content: string) => {
  if (!content || !content.includes('__bielaArtifact__')) {
    return content;
  }

  const lines = content.split('\n');
  const artifactLineIndex = lines.findIndex((line) => line.includes('__bielaArtifact__'));

  if (artifactLineIndex === -1) {
    return content;
  }

  if (artifactLineIndex > 0 && lines[artifactLineIndex - 1]?.trim().match(/^```\w*$/)) {
    lines[artifactLineIndex - 1] = '';
  }

  if (artifactLineIndex < lines.length - 1 && lines[artifactLineIndex + 1]?.trim().match(/^```$/)) {
    lines[artifactLineIndex + 1] = '';
  }

  return lines.join('\n');
};
