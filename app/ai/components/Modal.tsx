import React, { ReactNode, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { ArrowDownTrayIcon, ChatBubbleLeftIcon, DocumentDuplicateIcon, TrashIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message: string;
  actionLabel: string;
  onAction: (...args: any[]) => void; // can specify more strictly if you want
  variant?: 'default' | 'export' | 'download' | 'duplicate' | 'delete';
  initialValues?: {
    title: string;
    description: string;
  };
  children?: ReactNode;
  isAction:boolean;
}

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  message,
  actionLabel,
  onAction,
  variant = 'default',
  initialValues,
  children,
  isAction = true
}) => {
  const [formValues, setFormValues] = useState(initialValues || { title: '', description: '' });

  const getVariantStyles = () => {
    switch (variant) {
      case 'export':
        return 'bg-[#4ADE80]/5 text-[#4ADE80] border-[#4ADE80]/10';
      case 'download':
        return 'bg-[#A78BFA]/5 text-[#A78BFA] border-[#A78BFA]/10';
      case 'duplicate':
        return 'bg-[#22D3EE]/5 text-[#22D3EE] border-[#22D3EE]/10';
      case 'delete':
        return 'bg-red-500/5 text-red-500 border-red-500/10';
      default:
        return 'bg-white/5 text-white border-white/10';
    }
  };

  const getActionIcon = () => {
    switch (variant) {
      case 'export':
        return ChatBubbleLeftIcon;
      case 'download':
        return ArrowDownTrayIcon;
      case 'duplicate':
        return DocumentDuplicateIcon;
      case 'delete':
        return TrashIcon;
      default:
        return null;
    }
  };

  const ActionIcon = getActionIcon();

  const handleSubmit = () => {
    if (variant === 'default') {
      onAction(formValues.title, formValues.description);
    } else {
      onAction();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          style={{zIndex:'1000'}}
          className="fixed inset-0 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
        >
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            className={clsx(
              "w-full max-w-3xl rounded-xl border bg-[#1A1F2E] overflow-hidden",
              getVariantStyles()
            )}
          >
            <div className="p-6">
              <h2 className="text-2xl font-medium text-white mb-4">{title}</h2>
              <div className="mb-6">
                <p className="text-white/90 font-light mb-3">{message.split('?')[0]}</p>
                {/* <p className="text-white text-lg font-normal">Are you sure you want to continue?</p> */}
              </div>

              {children}

              {variant === 'default' && !children && (
                <div className="space-y-4 mb-6">
                  <div>
                    <label className="block text-sm text-white/70 mb-2">Title</label>
                    <input
                      type="text"
                      value={formValues.title}
                      onChange={(e) => setFormValues({ ...formValues, title: e.target.value })}
                      className="w-full bg-white/5 border border-white/10 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#4ADE80]"
                      placeholder="Enter rule title"
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-white/70 mb-2">Description</label>
                    <textarea
                      value={formValues.description}
                      onChange={(e) => setFormValues({ ...formValues, description: e.target.value })}
                      className="w-full bg-white/5 border border-white/10 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#4ADE80] min-h-[100px]"
                      placeholder="Enter rule description"
                    />
                  </div>
                </div>
              )}

              <div className="flex items-center justify-end gap-3">
                <button
                  onClick={onClose}
                  className="px-6 py-2 rounded-lg bg-white/5 text-white hover:bg-white/10 transition-colors"
                >
                  Cancel
                </button>
              {isAction && ( <button
                  onClick={handleSubmit}
                  className={clsx(
                    "px-6 py-2 rounded-lg transition-colors flex items-center gap-2",
                    variant === 'export' && "bg-[#4ADE80] text-black hover:bg-[#4ADE80]/90",
                    variant === 'download' && "bg-[#A78BFA] text-black hover:bg-[#A78BFA]/90",
                    variant === 'duplicate' && "bg-[#22D3EE] text-black hover:bg-[#22D3EE]/90",
                    variant === 'delete' && "bg-red-500 text-white hover:bg-red-600",
                    variant === 'default' && "bg-[#4ADE80] text-black hover:bg-[#4ADE80]/90"
                  )}
                >
                  {ActionIcon && <ActionIcon className="w-4 h-4" />}
                  {actionLabel}
                </button>)}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Modal;
