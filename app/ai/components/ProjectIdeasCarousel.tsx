import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import {
  FaBook,
  FaBriefcase,
  FaCloud,
  FaComments,
  FaDumbbell,
  FaHome,
  FaMusic,
  FaPalette,
  FaPlane,
  FaStore,
  FaTasks,
  FaUtensils
} from 'react-icons/fa';
import '~/ai/styles/index.css';

interface ProjectIdeasCarouselProps {
  setPrompt: (prompt: string) => void;
}

const ProjectIdeasCarousel: React.FC<ProjectIdeasCarouselProps> = ({ setPrompt }) => {
  const { t } = useTranslation('translation');
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);

  const suggestions = [
    {
      text: t('suggestions.weatherDashboard','Create a weather dashboard'),
      icon: <FaCloud />,
      borderColor: 'border-sky-400',
    },
    {
      text: t('suggestions.ecommercePlatform','Build an e-commerce platform'),
      icon: <FaStore />,
      borderColor: 'border-green-400',
    },
    {
      text: t('suggestions.socialMediaApp','Design a social media app'),
      icon: <FaComments />,
      borderColor: 'border-purple-400',
    },
    {
      text: t('suggestions.portfolioWebsite','Generate a portfolio website'),
      icon: <FaPalette />,
      borderColor: 'border-pink-400',
    },
    {
      text: t('suggestions.taskManagementApp','Create a task management app'),
      icon: <FaTasks />,
      borderColor: 'border-yellow-400',
    },
    {
      text: t('suggestions.fitnessTracker','Build a fitness tracker'),
      icon: <FaDumbbell />,
      borderColor: 'border-red-400',
    },
    {
      text: t('suggestions.recipeSharingPlatform','Design a recipe sharing platform'),
      icon: <FaUtensils />,
      borderColor: 'border-orange-400',
    },
    {
      text: t('suggestions.travelBookingSite','Create a travel booking site'),
      icon: <FaPlane />,
      borderColor: 'border-teal-400',
    },
    {
      text: t('suggestions.learningPlatform','Build a learning platform'),
      icon: <FaBook />,
      borderColor: 'border-indigo-400',
    },
    {
      text: t('suggestions.musicStreamingApp','Design a music streaming app'),
      icon: <FaMusic />,
      borderColor: 'border-pink-400',
    },
    {
      text: t('suggestions.realEstateListing','Create a real estate listing'),
      icon: <FaHome />,
      borderColor: 'border-orange-400',
    },
    {
      text: t('suggestions.jobBoard','Build a job board'),
      icon: <FaBriefcase />,
      borderColor: 'border-yellow-400',
    },
  ];

  const extendedSuggestions = [...suggestions, ...suggestions];

  useEffect(() => {
    if (!scrollContainerRef.current) return;

    let scrollAmount = 0;
    const distance = 0.5;
    const scrollWidth = scrollContainerRef.current.scrollWidth;

    const scroll = () => {
      if (!scrollContainerRef.current) return;

      scrollAmount += distance;

      if (scrollAmount >= scrollWidth / 2) {
        scrollAmount = 0;
      }

      scrollContainerRef.current.scrollLeft = scrollAmount;
      animationRef.current = requestAnimationFrame(scroll);
    };

    const animationRef = { current: requestAnimationFrame(scroll) };

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  const handleMouseEnter = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.style.animationPlayState = 'paused';
    }
  };

  const handleMouseLeave = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.style.animationPlayState = 'running';
    }
  };

  return (
    <div className="mt-12 z-10">
      <div
        className="flex items-center gap-4 overflow-x-auto w-[100%] pb-4 hide-scrollbar blur-margins"
        style={{
          width: '100%',
          position: 'relative',
        }}
      >
        <div
          ref={scrollContainerRef}
          className="flex animate-scroll"
          style={{
            display: 'flex',
            gap: '1rem',
            animation: 'scroll 60s linear infinite',
            width: 'max-content',
          }}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {extendedSuggestions.map((suggestion, index) => (
            <motion.button
              key={`${suggestion.text}-${index}`}
              whileHover={{
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                borderColor: 'rgba(255, 255, 255, 0.2)',
              }}
              whileTap={{ scale: 0.98 }}
              className={`flex items-center gap-2 bg-gradient-to-r ${suggestion.borderColor} backdrop-blur-sm px-4 py-2 rounded-lg border border-gray-700/50 transition-all duration-300 whitespace-nowrap`}
              onClick={() => setPrompt(suggestion.text)}
            >
              <span className="text-xl">{suggestion.icon}</span>
              <span className="font-light">{suggestion.text}</span>
            </motion.button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProjectIdeasCarousel;
