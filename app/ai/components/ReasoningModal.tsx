import React, { useEffect, useRef, useState } from 'react';
import { ChevronDown, ChevronUp, Lightbulb } from 'lucide-react';
import '../styles/ReasoningModalAnimations.css';
import { useTranslation } from 'react-i18next';

interface ReasoningModalProps {
  reasoning: string;
}

let hasOpenedFirst = false;

export const ReasoningModal: React.FC<ReasoningModalProps> = ({ reasoning }) => {
  const { t } = useTranslation('translation');
  const [isExpanded, setIsExpanded] = useState<boolean>(() => {
    if (!hasOpenedFirst) {
      hasOpenedFirst = true;
      return true;
    }

    return false;
  });
  const [isThinking, setIsThinking] = useState(true);

  useEffect(() => {
    if (!reasoning) {
      return;
    }

    setIsThinking(true);

    const timer = setTimeout(() => {
      setIsThinking(false);
    }, 500);

    return () => clearTimeout(timer);
  }, [reasoning]);

  if (!reasoning) {
    return null;
  }

  return (
    <div className="w-full mb-5">
      <div className="rounded-md overflow-hidden">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full flex items-center justify-between px-5 py-3 bg-[#1e2235]"
          aria-expanded={isExpanded}
        >
          <div className="flex items-center space-x-2">
            <Lightbulb size={16} className="text-[#67E8A4]" />
            <span className="text-[15px] font-light text-[#67E8A4]">{t('AIReasoning', 'AI Reasoning')}</span>
          </div>
          <div className="flex items-center space-x-2">
            <div
              className={`px-2 py-0.5 text-xs rounded ${isThinking ? 'bg-[#1f2c25]' : 'bg-[#2a3b30]'} text-[#67E8A4] flex items-center`}
            >
              {isThinking ? (
                <>
                  <span className="mr-1.5 text-[15px] font-light">{t('thinking', 'Thinking')}</span>
                  <span className="flex space-x-1.5">
                    <span className="h-2 w-2 rounded-full bg-[#67E8A4] animate-pulse-strong"></span>
                    <span className="h-2 w-2 rounded-full bg-[#67E8A4] animate-pulse-strong delay-300"></span>
                    <span className="h-2 w-2 rounded-full bg-[#67E8A4] animate-pulse-strong delay-600"></span>
                  </span>
                </>
              ) : (
                <span className="text-[15px] font-light">
                  {(() => {
                    const text = t('completed', 'completed');
                    return text.charAt(0).toUpperCase() + text.slice(1);
                  })()}
                </span>
              )}
            </div>
            {isExpanded ? (
              <ChevronUp size={16} className="text-[#67E8A4]" />
            ) : (
              <ChevronDown size={16} className="text-[#67E8A4]" />
            )}
          </div>
        </button>
        {isExpanded && (
          <div className="bg-[#171828] text-[#E2E8F0] border-l-2 border-[#67E8A4]/30 shadow-inner-custom">
            <div className="px-5 py-4 text-sm leading-relaxed">
              <div className="whitespace-pre-wrap">{reasoning}</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
