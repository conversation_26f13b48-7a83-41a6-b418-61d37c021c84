import React, { useState } from 'react';
import { useStore } from '@nanostores/react';
import { roadmapStore } from '~/ai/lib/stores/roadmap';
import { PlayIcon } from 'lucide-react';
import { ChatBubbleLeftIcon } from '@heroicons/react/24/outline';
import ScrollToBottom from './ScrollToBottom';

/**
 * A simple collapsible section component with a rotating chevron.
 * The `title` prop can be a string or a JSX element, which
 * lets us easily insert a colored circle in the title.
 */
const CollapsibleSection = ({ title, items, defaultOpen = true }) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  return (
    <div>
      {/* Section header (click to toggle) */}
      <div
        className="min-[800px]:max-w-[var(--chat-min-width)] flex items-center justify-between cursor-pointer px-4 py-2 hover:bg-[#2E3440] text-white font-semibold rounded"
        onClick={() => setIsOpen(!isOpen)}
      >
        {/* We accept `title` as either text or JSX */}
        {typeof title === 'string' ? <span>{title}</span> : title}

        {/* Rotating chevron-down icon (points down by default, rotates up when open) */}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`text-white/50 ml-2 h-4 w-4 transform transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          strokeWidth={2}
        >
          {/* Chevron-down path from Heroicons */}
          <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
        </svg>
      </div>

      {/* If open, show the list of items */}
      {isOpen && (
        <ul className="px-4 py-2 space-y-2 min-[800px]:max-w-[var(--chat-min-width)]">
          {items.map((item) => (
            <li
              key={item.id}
              className="relative  rounded-lg border border-white/10 bg-[#1B1E2A] hover:bg-[#1E2436] px-3 py-2"
            >
              <div className="text-white font-medium text-sm flex justify-between items-center ">
                {/* If you don't need the code label, remove the next span */}
                <div className="flex items-center gap-2">
                  {item.code && (
                    <span className="text-xs text-white/50">
                        {item.code}
                      </span>
                  )}
                  {item.content}
                </div>

                {/* Play button for in-progress tasks */}
                {item.status === 'in-progress' && (
                  <PlayIcon className="w-4 h-4 text-[#4ADE80]" />
                )}

                {/* Comment icon for done tasks */}
                {item.status === 'done' && (
                  <ChatBubbleLeftIcon className="w-4 h-4" />
                )}
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

/**
 * Shows the strategize items in three collapsible sections:
 * IN PROGRESS, TO DO, and DONE.
 */
const StrategizeRoadmap = () => {
  // Hardcoded items, each with a "status" to group them:
  const strategizeItems = [
    // IN PROGRESS
    { id: 1, code: 'STA-8',  content: 'Add sound effects and basic game state management', status: 'in-progress' },
    { id: 2, code: 'STA-9',  content: 'Test Task In Progress', status: 'in-progress' },

    // TO DO
    { id: 3, code: 'STA-10', content: 'Implement multiplayer networking', status: 'to-do' },
    { id: 4, code: 'STA-11', content: 'Add achievements system', status: 'to-do' },

    // DONE
    { id: 5, code: 'STA-2',  content: 'Build the upgrades and power management system', status: 'done' },
    { id: 6, code: 'STA-3',  content: 'Set up Firebase authentication with custom game profile', status: 'done' },
    { id: 7, code: 'STA-6',  content: 'Implement basic enemy AI and combat mechanics', status: 'done' },
    { id: 8, code: 'STA-1',  content: 'Create a futuristic landing page for StarCommand Elite', status: 'done' },
    { id: 9, code: 'STA-4',  content: 'Create the weapons and combat system interface', status: 'done' },
    { id: 10, code: 'STA-5', content: 'Implement the radar and sonar detection system', status: 'done' },
    { id: 11, code: 'STA-7', content: 'Build the main game HUD with power systems display', status: 'done' },
  ];

  // Separate items by status:
  const inProgressItems = strategizeItems.filter((item) => item.status === 'in-progress');
  const toDoItems       = strategizeItems.filter((item) => item.status === 'to-do');
  const doneItems       = strategizeItems.filter((item) => item.status === 'done');

  return (
    <div>

      <CollapsibleSection
        title={
          <div className="flex items-center gap-2">
            {/* Colored circle for IN PROGRESS */}
            <span className="h-2 w-2 rounded-full bg-[#A78BFA]" />
            <span>IN PROGRESS </span>
          </div>
        }
        items={inProgressItems}
      />

      <div>
        <hr className="show-prompt min-[800px]:max-w-[var(--chat-min-width)]" />
      </div>
      <CollapsibleSection
        title={
          <div className="flex items-center gap-2">
            <span className="h-2 w-2 rounded-full bg-white/50" />
            <span>TO DO </span>
          </div>
        }
        items={toDoItems}
      />

      <div>
        <hr className="show-prompt min-[800px]:max-w-[var(--chat-min-width)]" />
      </div>
      <CollapsibleSection
        title={
          <div className="flex items-center gap-2">
            {/* Colored circle for DONE */}
            <span className="h-2 w-2 rounded-full bg-[#4ADE80]" />
            <span>DONE ({doneItems.length})</span>
          </div>
        }
        items={doneItems}
        defaultOpen={false}
      />
    </div>
  );
};

/**
 * Shows the checkpoint items in a single list (similar to your CombinedHistory style).
 */
const CheckpointRoadmap = () => {
  // Hardcoded checkpoint items with a "status" property.
  const checkpointItems = [
    { id: 1, code: 'CHK-1', content: 'Plan checkpoint review meeting', status: 'to-do' },
    { id: 2, code: 'CHK-2', content: 'Update checkpoint documentation', status: 'to-do' },
    { id: 3, code: 'CHK-3', content: 'Finalize checkpoint deliverables', status: 'done' },
    { id: 4, code: 'CHK-4', content: 'Conduct post-checkpoint analysis', status: 'done' },
  ];

  return (
    <div className="px-4 py-2 space-y-2 min-[800px]:max-w-[var(--chat-min-width)]">
      {checkpointItems.map((item) => (
        <div key={item.id} className="relative  rounded-lg border border-white/10 bg-[#1B1E2A] hover:bg-[#1E2436] px-3 py-2">
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={item.status === 'done'}
              readOnly
              className="h-4 w-4"
            />
            <div className="flex">
              <div className="font-bold text-white">{item.code}</div>
              <div className="text-white">{item.content}</div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

/**
 * Master component that decides whether to show strategize sections
 * or checkpoint items, based on the store's mode.
 */
const CombinedRoadmap = ({ mode }) => {
  if (mode === 'strategize') {
    return <StrategizeRoadmap />;
  } else if (mode === 'checkpoint') {
    return <CheckpointRoadmap />;
  }
  return null;
};

/**
 * Main RoadmapContent that subscribes to your roadmapStore
 * and renders the CombinedRoadmap with the correct mode.
 */
const RoadmapContent = () => {
  const { mode } = useStore(roadmapStore);

  return (
    <ScrollToBottom  className={'flex-1 flex-messages roadmaps-history-tabs w-full'}>
      <CombinedRoadmap mode={mode} />
    </ScrollToBottom>
  );
};

export default RoadmapContent;
