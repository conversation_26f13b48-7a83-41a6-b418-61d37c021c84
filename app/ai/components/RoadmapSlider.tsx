// RoadmapSlider.tsx
import { motion } from 'framer-motion';
import { memo } from 'react';
import { classNames } from '~/utils/classNames';
import { cubicEasingFn } from '~/utils/easings';
import { genericMemo } from '~/utils/react';

// Import your icons (you can choose different ones if needed)

interface RoadmapSliderOption<T> {
  value: T;
  text: string;
}

export interface RoadmapSliderOptions<T> {
  left: RoadmapSliderOption<T>;
  right: RoadmapSliderOption<T>;
}

interface RoadmapSliderProps<T> {
  selected: T;
  options: RoadmapSliderOptions<T>;
  setRoadmapSelected?: (selected: T) => void;
}

export const RoadmapSlider = genericMemo(<T,>({
                                                selected,
                                                options,
                                                setRoadmapSelected,
                                              }: RoadmapSliderProps<T>) => {
  const isLeftSelected = selected === options.left.value;

  return (
    <div className="flex ">
      <div className="flex items-center gap-1.5 show-prompt rounded-md p-1 overflow-hidden">
        <RoadmapSliderButton
          variant="strategize"
          selected={isLeftSelected}
          setRoadmapSelected={() => setRoadmapSelected?.(options.left.value)}
        >
          <span className="flex items-center gap-2">

              <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                    d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L16 4m0 13V4m0 0L10 7" />
            </svg>
            <span className="text-[13px] font-normal tracking-[0.4px]">{options.left.text}</span>
          </span>
        </RoadmapSliderButton>

        <RoadmapSliderButton
          variant="checkpoint"
          selected={!isLeftSelected}
          setRoadmapSelected={() => setRoadmapSelected?.(options.right.value)}
        >
          <span className="flex items-center gap-2">
              <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-[13px] font-normal tracking-[0.4px]">{options.right.text}</span>
          </span>
        </RoadmapSliderButton>
      </div>
    </div>
  );
});

interface RoadmapSliderButtonProps {
  selected: boolean;
  variant: 'strategize' | 'checkpoint';
  children: JSX.Element;
  setRoadmapSelected: () => void;
}

const RoadmapSliderButton = memo(({
                                    selected,
                                    variant,
                                    children,
                                    setRoadmapSelected,
                                  }: RoadmapSliderButtonProps) => {
  const baseClasses = 'text-sm px-2.5 py-2 rounded-md relative hover:bg-white/5';

  // Define colors for each variant
  let selectedClasses = '';
  let bgColor = '';
  switch (variant) {
    case 'strategize':
      selectedClasses = 'bg-[#4ADE80]';
      bgColor = '#4ADE80';
      break;
    case 'checkpoint':
      selectedClasses = 'bg-[#4ADE80]';
      bgColor = '#4ADE80';
      break;
    default:
      selectedClasses = '';
      bgColor = '';
  }

  const notSelectedClasses = 'text-white/70 bg-transparent';

  return (
    <button
      onClick={setRoadmapSelected}
      className={classNames(
        baseClasses,
        selected ? selectedClasses : notSelectedClasses
      )}
    >
      <span className="relative z-10 flex">{children}</span>
      {selected && (
        <motion.span
          layoutId="pill-tab-roadmap"
          transition={{ duration: 0.2, ease: cubicEasingFn }}
          className="absolute inset-0 z-0 rounded-md"
          style={{ backgroundColor: bgColor }}
        ></motion.span>
      )}
    </button>
  );
});
