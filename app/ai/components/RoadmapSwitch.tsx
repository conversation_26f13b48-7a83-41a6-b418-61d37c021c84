import { useStore } from '@nanostores/react';
import { memo } from 'react';
import { roadmapStore, setRoadmapMode } from '~/ai/lib/stores/roadmap';
import { RoadmapSlider, type RoadmapSliderOptions } from '~/ai/components/RoadmapSlider';

const modeOptions: RoadmapSliderOptions<'strategize' | 'checkpoint'> = {
  left: {
    value: 'strategize',
    text: 'Strategize',
  },
  right: {
    value: 'checkpoint',
    text: 'Checkpoint',
  },
};

export const RoadmapSwitch = memo(() => {
  const mode = useStore(roadmapStore).mode;

  return (
    <RoadmapSlider
      selected={mode}
      options={modeOptions}
      setRoadmapSelected={(mode) => setRoadmapMode(mode)}
    />
  );
});
