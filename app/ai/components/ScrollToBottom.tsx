import React, { ReactNode, useEffect, useRef, useImperativeHandle, RefObject, useState } from 'react';

interface ScrollToBottomProps {
  className?: string;
  children: ReactNode;
  ref?: RefObject<ScrollToBottomRef|null>;
}

export interface ScrollToBottomRef {
  scrollToBottom: (options?:{onlyOnHover: boolean }) => void;
}

const ScrollToBottom = (({ className, children, ref }: ScrollToBottomProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isHovered, setIsHovered] = useState(false);
  useImperativeHandle(ref, () => ({ scrollToBottom: (options) => {
    if(options?.onlyOnHover && isHovered)
      return scrollToBottom();
    if(options?.onlyOnHover === false && !isHovered)
      return scrollToBottom();
    if(!options)
      scrollToBottom();
  }
}));

  const scrollToBottom = ()=> {
    const container = containerRef.current;
    if(!container) return;

    container.scrollTo({
      top: container.scrollHeight,
      behavior: 'smooth',
    });
  }

  useEffect(()=>{
    setTimeout(scrollToBottom, 100);
  }, []);

  return (
    <div
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      ref={containerRef} className={className}>
      {children}
    </div>
  );
});

export default ScrollToBottom;
