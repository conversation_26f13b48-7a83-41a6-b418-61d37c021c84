import { AnimatePresence, cubicBezier, motion } from 'framer-motion';
import Space<PERSON>ogo from '~/ai/components/SpaceLogo.tsx';

interface SendButtonProps {
  show: boolean;
  isStreaming?: boolean;
  disabled?: boolean;
  onClick?: (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
  onImagesSelected?: (images: File[]) => void;
  hasContent?:number | boolean
  type: 'send' | 'cancel';
}

const customEasingFn = cubicBezier(0.4, 0, 0.2, 1);

export const SendButton = ({ show,hasContent, isStreaming, disabled, onClick, type }: SendButtonProps) => {
  return (
    <AnimatePresence>
      {show && (
        <motion.button
          className="flex justify-center items-center mr-[20px] max-w-[40px] min-w-[40px] max-h-[40px] border-none  bg-transparent  hover:brightness-94 transition-theme disabled:opacity-50 "
          transition={{ ease: customEasingFn, duration: 0.17 }}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 10 }}
          onClick={(event) => {
            event.preventDefault();
            onClick?.(event);
          }}
        >
          <SpaceLogo hasContent={hasContent} running={isStreaming} variant="cosmic-pulse"/>
        </motion.button>
      )}
    </AnimatePresence>
  );
};
