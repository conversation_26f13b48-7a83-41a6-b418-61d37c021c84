import React, { useEffect, useRef, useState } from 'react';
import '~/components/styles/spacelogo.css';
import { useStore } from '@nanostores/react';
import { chatStore } from '~/ai/lib/stores/chat';
import { AI_MODELS, AI_MODEL_STORAGE_KEY } from '~/utils/constants';

const SpaceLogo = ({
  variant = 'cosmic-pulse',
  size = 'default',
  running = false,
  hasContent = false,
  transparent = false,
}) => {
  const containerRef = useRef(null);
  const logoRef = useRef(null);
  const starsContainerRef = useRef(null);
  const dynamicStyleRef = useRef(null);
  const { mode } = useStore(chatStore);
  const [modelColor, setModelColor] = useState('#4ADE80');

  const updateDynamicStyles = (color: string) => {
    if (!dynamicStyleRef.current) {
      dynamicStyleRef.current = document.createElement('style');
      dynamicStyleRef.current.id = 'dynamic-space-logo-styles';
      document.head.appendChild(dynamicStyleRef.current);
    }

    let r, g, b;

    if (color.startsWith('#')) {
      const hex = color.slice(1);
      r = parseInt(hex.substring(0, 2), 16);
      g = parseInt(hex.substring(2, 4), 16);
      b = parseInt(hex.substring(4, 6), 16);
    } else {
      r = 74;
      g = 222;
      b = 128;
    }

    const css = `
      .cosmic-pulse .throttle-group {
        filter: drop-shadow(0 0 1px rgba(${r}, ${g}, ${b}, 0.2));
      }
      
      .is-running-animation.cosmic-pulse .throttle-group {
        filter: drop-shadow(0 0 6px rgba(${r}, ${g}, ${b}, 0.8));
      }
      
      .is-running-animation.cosmic-pulse .throttle-line-1 {
        filter: drop-shadow(0 0 5px rgba(${r}, ${g}, ${b}, 0.9));
      }
      
      .is-running-animation.cosmic-pulse .throttle-line-2 {
        filter: drop-shadow(0 0 6px rgba(${r}, ${g}, ${b}, 0.9));
      }
      
      .is-running-animation.cosmic-pulse .throttle-line-3 {
        filter: drop-shadow(0 0 7px rgba(${r}, ${g}, ${b}, 0.9));
      }
      
      .is-running-animation.cosmic-pulse .throttle-line-4 {
        filter: drop-shadow(0 0 5px rgba(${r}, ${g}, ${b}, 0.9));
      }
      
      .is-running-animation.cosmic-pulse .star-particle {
        filter: drop-shadow(0 0 3px rgba(${r}, ${g}, ${b}, 0.8));
      }
      
      @keyframes dynamic-cosmic-twinkle {
        0% {
          opacity: 0.7;
          transform: scale(0.95);
          filter: drop-shadow(0 0 1px rgba(${r}, ${g}, ${b}, 0.5));
        }
        50% {
          opacity: 1;
          transform: scale(1.05);
          filter: drop-shadow(0 0 3px rgba(${r}, ${g}, ${b}, 0.8));
        }
        100% {
          opacity: 0.9;
          transform: scale(1);
          filter: drop-shadow(0 0 2px rgba(${r}, ${g}, ${b}, 0.6));
        }
      }
      
      @keyframes dynamic-cosmic-shooting-star {
        0% {
          transform: translateX(40px) translateY(-40px);
          opacity: 0;
          background-color: ${color};
        }
        10% {
          opacity: 0.9;
          transform: translateX(30px) translateY(-30px);
          background-color: ${color};
        }
        50% {
          opacity: 1;
          transform: translateX(0) translateY(0);
          background-color: #ffffff;
        }
        90% {
          opacity: 0.9;
          transform: translateX(-30px) translateY(30px);
          background-color: ${color};
        }
        100% {
          transform: translateX(-40px) translateY(40px);
          opacity: 0;
          background-color: ${color};
        }
      }
      
      .is-running-animation.cosmic-pulse .star-particle {
        animation: dynamic-cosmic-twinkle 2s infinite alternate;
      }
      
      .is-running-animation.cosmic-pulse .star {
        animation: dynamic-cosmic-shooting-star 2s infinite linear;
      }
    `;

    dynamicStyleRef.current.textContent = css;
  };

  useEffect(() => {
    const currentModelId = localStorage.getItem(AI_MODEL_STORAGE_KEY);

    if (currentModelId) {
      const currentModel = AI_MODELS.find((model) => model.value === currentModelId);

      if (currentModel) {
        setModelColor(currentModel.color);
        updateDynamicStyles(currentModel.color);
      }
    }

    return () => {
      if (dynamicStyleRef.current) {
        document.head.removeChild(dynamicStyleRef.current);
      }
    };
  }, []);

  useEffect(() => {
    const handleModelChangeEvent = (event: CustomEvent<{ modelValue: string; source?: string }>) => {
      if (event.detail && event.detail.modelValue) {
        const newModel = AI_MODELS.find((model) => model.value === event.detail.modelValue);

        if (newModel) {
          setModelColor(newModel.color);
          updateDynamicStyles(newModel.color);
        }
      }
    };

    window.addEventListener('biela:changeAIModel', handleModelChangeEvent as EventListener);

    return () => {
      window.removeEventListener('biela:changeAIModel', handleModelChangeEvent as EventListener);
    };
  }, []);

  useEffect(() => {
    updateDynamicStyles(modelColor);
  }, [modelColor]);

  const getColor = () => {
    if (variant === 'cosmic-pulse') {
      return mode === 'chat' ? '#871387' : modelColor;
    } else if (variant === 'error-state') {
      return '#FF4D4D';
    } else if (variant === 'warning-state') {
      return '#FFD700';
    } else if (variant === 'solved-error-state') {
      return '#FF4D4D';
    }

    return modelColor;
  };

  useEffect(() => {
    // Initialize the logo with appropriate animations based on variant
    const container = containerRef.current;
    const logo = logoRef.current;
    const starsContainer = starsContainerRef.current;
    const starParticles = container.querySelectorAll('.star-particle');
    const throttleLines = container.querySelectorAll('.throttle-line');

    // Add random twinkling to stars
    starParticles.forEach(star => {
      const randomDelay = Math.random() * 3;
      star.style.animationDelay = `${randomDelay}s`;
    });

    // Set transform origin for throttle lines
    throttleLines.forEach(line => {
      // Set transform origin to the front of each line
      const pathData = line.getAttribute('d');
      if (pathData) {
        const match = pathData.match(/M\s*([0-9.]+)\s*([0-9.]+)/);
        if (match && match.length >= 3) {
          const x = parseFloat(match[1]);
          const y = parseFloat(match[2]);
          line.style.transformOrigin = `${x}px ${y}px`;
        }
      }
    });

    // Add more dynamic stars based on variant
    if (starsContainer) {
      // Add variant-specific stars
      for (let i = 0; i < 10; i++) {
        const star = document.createElement('div');
        star.classList.add('star', `${variant}-star-${i}`);
        star.style.width = `${Math.random() * 2 + 0.5}px`;
        star.style.height = star.style.width;
        star.style.top = `${Math.random() * 100}%`;
        star.style.left = `${Math.random() * 100}%`;
        star.style.opacity = '0';

        // Set color based on variant and mode
        const color = getColor();
        star.style.backgroundColor = color;

        star.style.animationDelay = `${Math.random() * 2}s`;
        starsContainer.appendChild(star);
      }

      // Add special particles for specific variants
      if (variant === 'error-state' || variant === 'warning-state' || variant === 'solved-error-state') {
        const particlesContainer = container.querySelector(`.${variant}-particles`);
        if (particlesContainer && variant === 'warning-state') {
          // Add caution particles for warning state
          for (let i = 0; i < 5; i++) {
            const particle = document.createElement('div');
            particle.classList.add('caution-particle');
            particle.style.position = 'absolute';
            particle.style.width = '3px';
            particle.style.height = '3px';
            particle.style.backgroundColor = '#ffd700';
            particle.style.borderRadius = '50%';
            particle.style.opacity = '0';
            particle.style.top = `${20 + Math.random() * 60}%`;
            particle.style.left = `${20 + Math.random() * 60}%`;
            particlesContainer.appendChild(particle);
          }
        }
      }
    }
    // Trigger the transition for solved error state
    if (variant === 'solved-error-state') {
      setTimeout(() => {
        container.classList.add('transitioning');
      }, 500);
    }

  }, [variant, mode]);

  // Determine container size and logo size based on the size prop
  let containerSize = {};
  let logoSize = {};

  switch (size) {
    case 'large':
      containerSize = { width: '72px', height: '72px' };
      logoSize = { height: '100px' }; // Proportionally larger logo
      break;
    default:
      containerSize = { width: '50px', height: '50px' };
      logoSize = { height: '72px' };
  }

  // Apply transparent background and border if transparent prop is true
  const containerStyle = {
    ...containerSize,
    ...(transparent ? {
      backgroundColor: 'transparent',
      border: '1px solid #2b2f38'
    } : {})
  };

  const currentColor = getColor();

  return (
    <div
      className={`logo-container transition-all ${running ? 'is-running-animation' : hasContent ? 'opacity-100' : 'opacity-50' } ${variant} ${mode === 'chat' ? 'logo-chat-mode' : ''}`}
      ref={containerRef}
      style={containerStyle}
      data-color={currentColor}
    >
      <div className="stars-container" ref={starsContainerRef}>
        <div className="star star1"></div>
        <div className="star star2"></div>
        <div className="star star3"></div>
        <div className="star star4"></div>
        <div className="star star5"></div>
        <div className="star star6"></div>
        <div className="star star7"></div>
        <div className="star star8"></div>
        <div className="star star9"></div>
        <div className="star star10"></div>
        <div className="star star11"></div>
        <div className="star star12"></div>
      </div>

      {/* Variant-specific particles container */}
      {variant === 'error-state' && <div className="error-particles"></div>}
      {variant === 'warning-state' && <div className="warning-particles"></div>}
      {variant === 'solved-error-state' && <div className="solved-error-particles"></div>}

      <svg
        width="365"
        height="325"
        viewBox="0 0 365 325"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="logo"
        ref={logoRef}
        style={logoSize}
      >
        {/* Throttle effects (back of the B) */}
        <g className="throttle-group">
          <path
            d="M262.45 166.65L78.9802 275.62C76.2002 277.27 72.6002 276.36 70.9502 273.57C69.2502 270.72 70.2802 266.99 73.2002 265.42L260.98 164.07C261.7 163.68 262.6 163.95 262.98 164.67C263.36 165.37 263.11 166.24 262.43 166.64L262.45 166.65Z"
            fill={currentColor}
            className="throttle-line throttle-line-1"
          />
          <path
            d="M165.429 146.58L48.3495 218.03C45.5895 219.72 41.9795 218.84 40.2895 216.08C38.5995 213.32 39.4795 209.71 42.2395 208.02C42.2595 207.95 163.909 144.23 164.049 144.13C165.569 143.36 166.999 145.53 165.439 146.57L165.429 146.58Z"
            fill={variant === 'cosmic-pulse' ? currentColor :
              variant === 'error-state' ? "#FF6B6B" :
                variant === 'warning-state' ? "#FFC107" : "#FF6B6B"}
            className="throttle-line throttle-line-2"
          />
          <path
            d="M216.139 156.92L8.83863 279.33C6.04863 280.98 2.45863 280.05 0.818627 277.26C-0.871373 274.41 0.158627 270.71 3.06863 269.13L214.639 154.25C215.379 153.85 216.309 154.12 216.709 154.86C217.099 155.59 216.849 156.49 216.139 156.91V156.92Z"
            fill={variant === 'cosmic-pulse' ? currentColor :
              variant === 'error-state' ? "#FF3333" :
                variant === 'warning-state' ? "#FFB300" : "#FF3333"}
            className="throttle-line throttle-line-3"
          />
          <path
            d="M223.639 236.33L123.219 288.75C121.009 289.85 119.149 286.74 121.279 285.33L217.859 226.14C220.619 224.45 224.229 225.31 225.919 228.07C227.699 230.94 226.629 234.78 223.629 236.33H223.639Z"
            fill={variant === 'cosmic-pulse' ? currentColor :
              variant === 'error-state' ? "#FF5252" :
                variant === 'warning-state' ? "#FFA000" : "#FF5252"}
            className="throttle-line throttle-line-4"
          />
        </g>

        {/* Front of the B (spaceship part) */}
        <g className="spaceship-group">
          <path
            d="M302.827 37.04C315.017 42.13 322.967 55.04 323.197 67.8C323.527 74.75 320.227 80.61 316.887 86.9C315.807 90.74 319.177 95.66 323.417 94.73C333.867 92.61 344.737 93.76 352.847 100.95C362.697 109.78 367.167 125.29 363.257 138.57C356.257 164.47 328.657 178.04 305.547 190.9C305.207 191.19 304.827 191.46 304.417 191.7L238.977 228.77C235.697 230.63 231.527 229.48 229.667 226.19C227.807 222.91 228.957 218.74 232.247 216.87L289.637 184.35C306.237 174.06 331.437 163.98 341.947 147.58C351.417 134.34 345.267 114.52 330.767 108.18C318.177 102.68 304.317 108.93 292.157 115.96C286.647 118.36 276.297 128.44 271.377 119.93C266.807 111.65 279.397 108.12 284.277 104.8C296.077 97.83 302.337 93.17 305.817 82.47C308.967 70.93 302.287 57.27 291.747 51.92C279.527 45.66 265.267 52.88 253.327 59.72C253.327 59.72 253.307 59.73 253.297 59.74C252.977 59.92 252.657 60.11 252.337 60.29L249.027 62.17L218.057 79.72C214.767 81.58 210.597 80.42 208.737 77.14C206.877 73.85 208.027 69.68 211.317 67.82L246.287 48C246.517 47.87 246.747 47.76 246.977 47.66C265.187 37.24 283.877 28.96 302.837 37.01L302.827 37.04Z"
            fill={variant === 'cosmic-pulse' ? currentColor :
              variant === 'error-state' ? "#FF8080" :
                variant === 'warning-state' ? "#FFD54F" : "#FF8080"}
            className="spaceship-body"
          />
          <path
            d="M252.318 60.3101C251.198 60.9601 250.098 61.6001 249.028 62.2201L249.008 62.1901L252.318 60.3101Z"
            fill={variant === 'cosmic-pulse' ? currentColor :
              variant === 'error-state' ? "#FF8080" :
                variant === 'warning-state' ? "#FFD54F" : "#FF8080"}
            className="spaceship-detail"
          />
          <path
            d="M253.28 59.7598C253.19 59.8198 253.1 59.8698 253.01 59.9198L252.32 60.3098C252.64 60.1298 252.96 59.9398 253.28 59.7598Z"
            fill={variant === 'cosmic-pulse' ? currentColor :
              variant === 'error-state' ? "#FF8080" :
                variant === 'warning-state' ? "#FFD54F" : "#FF8080"}
            className="spaceship-detail"
          />
          <path
            d="M201.42 87.6002L132.06 122.42C131.09 122.91 129.91 122.52 129.42 121.55C128.96 120.64 129.29 119.54 130.12 119.01L195.64 77.4202C198.37 75.6802 202 76.4902 203.74 79.2302C205.48 81.9602 204.67 85.5902 201.93 87.3302C201.76 87.4302 201.59 87.5302 201.42 87.6202V87.6002Z"
            fill={variant === 'cosmic-pulse' ? currentColor :
              variant === 'error-state' ? "#FF8080" :
                variant === 'warning-state' ? "#FFD54F" : "#FF8080"}
            className="spaceship-detail"
          />
        </g>

        {/* Stars/particles */}
        <g className="stars-group">
          <path
            d="M85.31 171.22C82.19 170.35 80.36 167.12 81.22 164C80.36 167.12 77.12 168.95 74 168.09C77.12 168.95 78.95 172.19 78.09 175.31C78.95 172.19 82.19 170.36 85.31 171.22Z"
            fill={variant === 'cosmic-pulse' ? currentColor :
              variant === 'error-state' ? "#FF8080" :
                variant === 'warning-state' ? "#FFD54F" : "#FF8080"}
            className="star-particle star-particle-1"
          />
          <path
            d="M165.349 57.3502C161.059 56.1602 158.549 51.7202 159.729 47.4302C158.539 51.7202 154.099 54.2302 149.809 53.0502C154.099 54.2402 156.609 58.6802 155.429 62.9702C156.619 58.6802 161.059 56.1702 165.349 57.3502Z"
            fill={variant === 'cosmic-pulse' ? currentColor :
              variant === 'error-state' ? "#FF8080" :
                variant === 'warning-state' ? "#FFD54F" : "#FF8080"}
            className="star-particle star-particle-2"
          />
          <path
            d="M93.3603 119.92C89.0703 118.73 86.5603 114.29 87.7403 110C86.5503 114.29 82.1103 116.8 77.8203 115.62C82.1103 116.81 84.6203 121.25 83.4403 125.54C84.6303 121.25 89.0703 118.74 93.3603 119.92Z"
            fill={variant === 'cosmic-pulse' ? currentColor :
              variant === 'error-state' ? "#FF8080" :
                variant === 'warning-state' ? "#FFD54F" : "#FF8080"}
            className="star-particle star-particle-3"
          />
          <path
            d="M174.088 288.71C170.998 287.86 169.188 284.66 170.038 281.57C169.188 284.66 165.988 286.47 162.898 285.62C165.988 286.47 167.798 289.67 166.948 292.76C167.798 289.67 170.998 287.86 174.088 288.71Z"
            fill={variant === 'cosmic-pulse' ? currentColor :
              variant === 'error-state' ? "#FF8080" :
                variant === 'warning-state' ? "#FFD54F" : "#FF8080"}
            className="star-particle star-particle-4"
          />
          <path
            d="M241.787 277.21C236.337 275.7 233.147 270.06 234.647 264.61C233.137 270.06 227.497 273.25 222.047 271.75C227.497 273.26 230.687 278.9 229.187 284.35C230.697 278.9 236.337 275.71 241.787 277.21Z"
            fill={variant === 'cosmic-pulse' ? currentColor :
              variant === 'error-state' ? "#FF8080" :
                variant === 'warning-state' ? "#FFD54F" : "#FF8080"}
            className="star-particle star-particle-5"
          />
          <path
            d="M303.268 225.45C298.888 224.24 296.328 219.71 297.538 215.33C296.328 219.71 291.798 222.27 287.418 221.06C291.798 222.27 294.358 226.8 293.148 231.18C294.358 226.8 298.888 224.24 303.268 225.45Z"
            fill={variant === 'cosmic-pulse' ? currentColor :
              variant === 'error-state' ? "#FF8080" :
                variant === 'warning-state' ? "#FFD54F" : "#FF8080"}
            className="star-particle star-particle-6"
          />
          <path
            d="M280.62 83.25C275.75 81.9 272.9 76.87 274.25 72C272.9 76.87 267.87 79.72 263 78.37C267.87 79.72 270.72 84.75 269.37 89.62C270.72 84.75 275.75 81.9 280.62 83.25Z"
            fill={variant === 'cosmic-pulse' ? currentColor :
              variant === 'error-state' ? "#FF8080" :
                variant === 'warning-state' ? "#FFD54F" : "#FF8080"}
            className="star-particle star-particle-7"
          />
          <path
            d="M85.5097 251.25C83.4197 250.67 82.1897 248.5 82.7697 246.41C82.1897 248.5 80.0197 249.73 77.9297 249.15C80.0197 249.73 81.2497 251.9 80.6697 253.99C81.2497 251.9 83.4197 250.67 85.5097 251.25Z"
            fill={variant === 'cosmic-pulse' ? currentColor :
              variant === 'error-state' ? "#FF8080" :
                variant === 'warning-state' ? "#FFD54F" : "#FF8080"}
            className="star-particle star-particle-8"
          />
          <path
            d="M41.3086 236.27C39.3086 235.72 38.1286 233.64 38.6886 231.64C38.1386 233.64 36.0586 234.82 34.0586 234.26C36.0586 234.81 37.2386 236.89 36.6786 238.89C37.2286 236.89 39.3086 235.71 41.3086 236.27Z"
            fill={variant === 'cosmic-pulse' ? currentColor :
              variant === 'error-state' ? "#FF8080" :
                variant === 'warning-state' ? "#FFD54F" : "#FF8080"}
            className="star-particle star-particle-9"
          />
        </g>
      </svg>
    </div>
  );
};
export default SpaceLogo;
