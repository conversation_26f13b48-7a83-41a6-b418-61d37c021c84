import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Plan } from '~/types/billing';
import { fetchPlans, createCheckoutSession } from '~/lib/stores/billing';
import { PlanCard } from '~/backOffice/components/billings/components/PlanCard';
import { PlanCardSkeleton } from '~/backOffice/components/billings/components/PlanCardSkeleton';
import { NoPlan } from '~/backOffice/components/billings/components/NoPlan';
import {FaCcAmex, FaCcDinersClub, FaCcDiscover, FaCcJcb, FaCcMastercard, FaCcVisa} from "react-icons/fa";

interface UpgradePlansViewProps {
  onBack: () => void;
}

const cardVariants = {
  hidden: (i: number) => ({
    opacity: 0,
    y: 20,
    transition: { delay: i * 0.1, duration: 0.3 },
  }),
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: { delay: i * 0.1, duration: 0.3 },
  }),
};

const handleSubscribe = async (planId: string, priceId: string): Promise<void> => {
  console.log('Subscribing to plan:', planId, priceId);
  await createCheckoutSession({
    mode: 'subscription',
    planId,
    priceId,
  });
};

const UpgradePlansView: React.FC<UpgradePlansViewProps> = ({ onBack }) => {
  const { t } = useTranslation();
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('yearly');
  const [plansLoading, setPlansLoading] = useState<boolean>(true);
  const [stripePlans, setStripePlans] = useState<Plan[]>([]);

  useEffect(() => {
    const loadPlans = async (): Promise<void> => {
      setPlansLoading(true);
      try {
        const plans = await fetchPlans(billingCycle);
        setStripePlans(plans);
      } catch (error) {
        console.error('Error loading plans:', error);
      } finally {
        setPlansLoading(false);
      }
    };
    void loadPlans();
  }, [billingCycle]);

  return (
    <div className="w-full">
      {/* header */}
      <motion.div
        className="flex justify-between items-center mb-6"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <h2 className="text-2xl text-white font-light">{t('choosePlan', 'Choose a Plan')}</h2>
        <button
          onClick={onBack}
          className="text-gray-400 hover:text-white flex items-center transition-colors text-sm font-light"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path
              fillRule="evenodd"
              d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z"
              clipRule="evenodd"
            />
          </svg>
          {t('back', 'Back')}
        </button>
      </motion.div>

      {stripePlans?.length > 0 && (
        <motion.div
          className="flex justify-center mb-8"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <div className="bg-gray-800/50 rounded-full p-1 ">
            {(['monthly', 'yearly'] as const).map((cycle) => (
              <button
                key={cycle}
                onClick={() => setBillingCycle(cycle)}
                className={`capitalize px-6 py-2 rounded-full transition-all text-sm font-light ${
                  billingCycle === cycle
                    ? 'bg-gradient-to-r from-green-500 to-green-600 shadow-lg shadow-green-500/20 text-white'
                    : 'bg-transparent text-gray-400 hover:text-white'
                }`}
              >
                {t(cycle)} {cycle === 'yearly' ? '(save 10%)' : ''}
              </button>
            ))}
          </div>
        </motion.div>
      )}

      <div
        className={`${
          stripePlans?.length === 0 ? 'flex' : 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8'
        } items-stretch justify-center`}
      >
        {plansLoading ? (
          [0, 1, 2].map((idx) => <PlanCardSkeleton key={idx} index={idx} />)
        ) : stripePlans?.length === 0 ? (
          <NoPlan />
        ) : (
          stripePlans.map((plan, idx) => (
            <motion.div key={plan.id} custom={idx} initial="hidden" animate="visible" variants={cardVariants}>
              <PlanCard plan={plan} index={idx} billingCycle={billingCycle} onSubscribe={handleSubscribe} />
            </motion.div>
          ))
        )}
      </div>

      <div className="grid grid-cols-6 gap-4 my-8 w-max mx-auto">
        <FaCcVisa className="text-2xl" />
        <FaCcMastercard className="text-2xl " />
        <FaCcAmex className="text-2xl" />
        <FaCcDiscover className={'text-2xl'} />
        <FaCcDinersClub className={'text-2xl'} />
        <FaCcJcb className={'text-2xl'} />
      </div>
    </div>
  );
};

export default UpgradePlansView;
