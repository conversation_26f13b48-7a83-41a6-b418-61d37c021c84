import { MODEL_REGEX, PROVIDER_REGEX } from '~/utils/constants';
import { Markdown } from './Markdown';
import { Attachment } from 'ai';

interface UserMessageProps {
  content: string;
  experimental_attachments?: Attachment[];
}

export function UserMessage({ content, experimental_attachments }: UserMessageProps) {
  // ✅ Handle string-based content
  if (typeof content === 'string') {
    // If there are any images in attachments, we'll render them
    const images = experimental_attachments?.filter((attachment) => attachment.contentType === 'image' && attachment.url) || [];

    return (
      <div className="overflow-hidden">
        <div className="flex flex-col gap-4">
          {/* ✅ Render the text content */}
          <Markdown userMessage={true} html>{stripMetadata(content)}</Markdown>

          {/* ✅ Render ALL valid images */}
          {images.map((item, index) => (
            <img
              key={index}
              src={item.url}
              alt={`Image ${index + 1}`}
              className="max-w-full h-auto rounded-lg"
              style={{ maxHeight: '512px', objectFit: 'contain' }}
            />
          ))}
        </div>
      </div>
    );
  }

  return null;
}

// ✅ Clean metadata (like model/provider details) from text
function stripMetadata(content: string) {
  return content.replace(MODEL_REGEX, '').replace(PROVIDER_REGEX, '');
}
