import { create } from 'zustand';
import Cookies from 'js-cookie';

export const ShortEvents = create((set, get) => ({
  // Starea inițială
  contestModalClosed: false,

  // Funcție pentru a seta contestModalClosed la true și a salva în cookie
  closeContestModal: () => {
    set({ contestModalClosed: true });
    Cookies.set("contest-modal", "true");
  },

  // Funcție generică pentru a seta contestModalClosed cu valoarea primită (true/false)
  setContestModal: (value) => {
    set({ contestModalClosed: value });
    Cookies.set("contest-modal", value ? "true" : "false");
  },

  // Funcție care face get pe cookie-ul 'contest-modal'
  // Dacă cookie-ul nu există, îl setează la false și actualizează store-ul.
  // Dacă există, folosește valoarea acestuia pentru a actualiza store-ul.
  initializeContestModal: () => {
    const cookieValue = Cookies.get("contest-modal");

    if (cookieValue === undefined) {
      // Cookie-ul nu există: îl setăm la "false"
      Cookies.set("contest-modal", "false");
      set({ contestModalClosed: false });
      return false;
    } else {
      // Convertim valoarea din string în boolean
      const parsedValue = cookieValue === "true";
      set({ contestModalClosed: parsedValue });
      return parsedValue;
    }
  },
}));
