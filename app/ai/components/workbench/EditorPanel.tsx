import { useStore } from '@nanostores/react';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { LoadingBiela } from '~/components/LoadingBiela';
import SessionStore from '~/ai/lib/stores/session/sessionStore';
import { motion } from 'framer-motion';

export const EditorPanel = () => {

  const [error, setError] = useState<string | null>(null);
  const [isFullyLoaded, setIsFullyLoaded] = useState(false);

  const sessionStore = SessionStore.getInstance();
  const isAuthenticated = useStore(sessionStore.hasWebcontainerSession)

  if (error) {
    return <div style={{ color: 'red' }}>Authentication Error: {error}</div>;
  }

  return (
    <div className="w-full h-full relative">
        {isAuthenticated && (
        <iframe
          onLoad={()=> setTimeout(()=>setIsFullyLoaded(true), 4300)}
          title="Code Server"
          src={`${sessionStore.sessionData.host}${sessionStore.sessionData.editorPath}?folder=/config/workspace/${sessionStore.getSlug()}`}
          className="w-full h-full"
        />
      )}
      {!isFullyLoaded && (
        <motion.div
          initial={{ opacity: 1 }}
          animate={{ opacity: 0 }}
        
          className='bg-[#0a0f1c] absolute inset-0 z-10'
          transition={{ delay:2000, duration: 1 }}
        >
          <LoadingBiela className="w-full h-full" />
        </motion.div>
      )}
    </div>
  );
};