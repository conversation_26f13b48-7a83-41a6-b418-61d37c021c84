import { useStore } from '@nanostores/react';
import { memo, useEffect, useRef, useState } from 'react';
import { workbenchStore } from '~/ai/lib/stores/workbench';
import { PortDropdown } from './PortDropdown';
import { ScreenshotSelector } from './ScreenshotSelector';
import { getAppState } from '~/ai/lib/stores/cloud/cloud';
import { IconButton } from '~/components/common/IconButton';
import { PanelHeaderButton } from '~/ai/components/PanelHeaderButton';
import { FaRocket } from 'react-icons/fa';
import WithTooltip from '~/ai/components/Tooltip';
import SessionStore from '~/ai/lib/stores/session/sessionStore';
import LoadingAnimation from '~/ai/components/LoadingAnimation';

export const Preview = memo(
  ({
    isStreaming,
    mode,
    isDeploying,
    deviceMode,
    DEVICES,
    setDeviceMode,
    chatStarted,
    handleDeploy,
    openApp,
  }: {
    isStreaming: boolean | undefined;
    mode: string;
    isDeploying: boolean;
    chatStarted?: boolean;
    DEVICES: { label: string; value: number; id: string; Icon: JSX.Element }[];
    setDeviceMode: (device: { label: string; value: number; id: string; Icon: JSX.Element }) => void;
    handleDeploy: () => void;
    openApp: () => void;
    deviceMode: {
      label: string;
      value: number;
      id: string;
    };
  }) => {
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    const selectedView = useStore(workbenchStore.currentView);
    const [activePreviewIndex, setActivePreviewIndex] = useState(0);
    const [isPortDropdownOpen, setIsPortDropdownOpen] = useState(false);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const sessionStore = SessionStore.getInstance();
    const ports = useStore(sessionStore.availablePorts);

    const [iframeUrl, setIframeUrl] = useState<string | undefined>('');
    const [displayUrl, setDisplayUrl] = useState<string | undefined>('');
    const [hardCodedUrl,setHardCodedUrl] = useState<string | undefined>('')
    const [isSelectionMode, setIsSelectionMode] = useState(false);
    const [appState, setAppState] = useState<any>(undefined);
    const [isLoaded, setIsLoaded] = useState(false);
    const hasRefreshedRef = useRef(false);

    function normalizeUrlPreview(url: string): string {
      try {
        const parsedUrl = new URL(url);
        return `${parsedUrl.protocol}//${parsedUrl.host}`;
      } catch (e) {
        return url; // fallback dacă nu e un URL valid
      }
    }


    function normalizeUrl(url) {
      if (/^https?:\/\//i.test(url)) {
        return url; // Already has http or https
      }

      const currentProtocol = window.location.protocol;

      // 'http:' or 'https:'
      return `${currentProtocol}//${url}`;
    }

    useEffect(() => {
      if (displayUrl || iframeUrl || ports?.length === 0) {
        return;
      }

      const url = sessionStore.previewUrl(ports[0]);
      setDisplayUrl(normalizeUrlPreview(url)); // shown to the user
      setHardCodedUrl(normalizeUrlPreview(url)); // shown to the user
      setIframeUrl(url); // used in iframe
    }, [ports]);

    useEffect(() => {
      const port = ports[activePreviewIndex ?? 0];

      if (!port) {
        return;
      }

      const newUrl = sessionStore.previewUrl(port.toString());

      if (displayUrl?.includes(newUrl)) {
        return; // already set
      }

      setDisplayUrl(normalizeUrlPreview(newUrl)); // shown to the user

      const noCacheUrl = `${newUrl}?t=${Date.now()}`;
      setIframeUrl(noCacheUrl); // used in iframe
    }, [activePreviewIndex]);

    useEffect(() => {
      sessionStore.fetchPreviewPorts();
    }, [selectedView]);

    useEffect(() => {
      if (chatStarted) {
        getAppState().then(setAppState);
      }
    }, [isDeploying]);

    // Listen for iframe-ready postMessage
    useEffect(() => {
      const handleMessage = (event: MessageEvent) => {
        if (event.data?.type === 'iframe-ready') {
          setIsLoaded(true);
          hasRefreshedRef.current = true;
          console.log('Iframe has confirmed it is ready');
        }
      };

      window.addEventListener('message', handleMessage);
      return () => {
        window.removeEventListener('message', handleMessage);
      };
    }, []);

    // Handle manual reload
    const reloadPreview = () => {
      sessionStore.fetchPreviewPorts();

      const port = ports[activePreviewIndex ?? 0];

      if (!port) {
        return;
      }

      const url = sessionStore.previewUrl(port);
      setDisplayUrl(normalizeUrlPreview(url)); // shown to the user

      const noCacheUrl = `${url}?t=${Date.now()}`;
      setIframeUrl(noCacheUrl); // used in iframe
    };

    const formatDeployedAt = (isoString?: string) => {
      if (!isoString) {
        return 'Not deployed';
      }

      const date = new Date(isoString);

      return new Intl.DateTimeFormat('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        day: '2-digit',
        month: 'long',
        year: 'numeric',
      }).format(date);
    };

    const toggleFullscreen = async () => {
      if (!isFullscreen && containerRef.current) {
        await containerRef.current.requestFullscreen();
      } else if (document.fullscreenElement) {
        await document.exitFullscreen();
      }
    };

    useEffect(() => {
      const onChange = () => setIsFullscreen(!!document.fullscreenElement);
      document.addEventListener('fullscreenchange', onChange);

      return () => document.removeEventListener('fullscreenchange', onChange);
    }, []);

    const onIframeLoad = () => {
      setIsLoaded(true);

      const iframe = iframeRef.current;
      if (iframe && iframe.contentWindow && iframe.contentDocument) {
        setTimeout(() => {
          try {
            const body = iframe.contentDocument?.body;
            const html = iframe.contentDocument?.documentElement;

            const isBlank = body && body.innerHTML.trim() === '';
            const isWhite = body && getComputedStyle(body).backgroundColor === 'rgb(255, 255, 255)';
            const hasOnlyWhitespace = html && html.textContent?.trim() === '';

            if ((isBlank || isWhite || hasOnlyWhitespace) && !hasRefreshedRef.current) {
              console.log('Iframe appears blank or white, reloading...');
              hasRefreshedRef.current = true;
              reloadPreview();
            }
            else if (body?.innerText?.includes('No preview available')) {
              console.log('Iframe shows "No preview available", reloading...');
              reloadPreview();
            }
          } catch (e) {
            console.warn('Cannot access iframe contents. Possibly cross-origin.', e);
          }
        }, 4000);
      }
    };

    useEffect(() => {
      if (!isStreaming && !isLoaded) {
        setTimeout(()=>{
          reloadPreview();
        },5000)
      }
    }, [isStreaming]);

    useEffect(() => {
      if (ports.length === 0 && !isStreaming) {
        const interval = setInterval(() => {
          reloadPreview();
        }, 5000);
        return () => clearInterval(interval);
      }
    }, [ports.length, isStreaming]);

    return (
      <div ref={containerRef} className="w-full h-full flex flex-col relative">
        {isPortDropdownOpen && (
          <div className="z-iframe-overlay w-full h-full absolute" onClick={() => setIsPortDropdownOpen(false)} />
        )}
        <div className="flex items-center justify-between px-4 py-2 border-b border-white/10 gap-4">
          <div className="flex items-center gap-4 flex-1">
            <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
              {DEVICES.map((device) => (
                <button
                  key={device.id}
                  title={device.label}
                  onClick={() => setDeviceMode(device)}
                  className={`p-1.5 rounded transition-colors ${
                    deviceMode.id === device.id ? 'bg-[#4ADE80]/10 text-[#4ADE80]' : 'text-white/50 hover:bg-white/10'
                  }`}
                >
                  {device.Icon}
                </button>
              ))}
            </div>
            <div className="flex-1 flex items-center gap-2 bg-white/5 rounded-lg px-3 py-1.5 min-w-[300px]">
              <WithTooltip
                tooltip={
                  <div className="flex flex-col text-left text-xs leading-tight">
                    <span className="font-medium text-white/80">Last deployment time</span>
                    <span className="text-white/60">{formatDeployedAt(appState?.cloudApp?.deployed_at)}</span>
                  </div>
                }
                position="bottom"
                className="bg-black/70 p-2 rounded-md mt-2"
              >
                <img src="/icons/globe.svg" className="w-[14px] h-[14px] cursor-pointer" alt="URL" />
              </WithTooltip>
              <div className="link-copy">
                <input
                  title="URL"
                  className="flex-1 bg-transparent w-full text-sm text-white/70 outline-none"
                  type="text"
                  value={displayUrl}
                  onChange={(e) => {
                    const newValue = e.target.value;

                    if (!newValue.startsWith(hardCodedUrl)) return;

                    setDisplayUrl(newValue);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      setIframeUrl(displayUrl);
                    }
                  }}
                  style={{ width: '100%' }}
                />
              </div>
              {ports.length > 0 && (
                <PortDropdown
                  activePreviewIndex={activePreviewIndex}
                  setActivePreviewIndex={setActivePreviewIndex}
                  isDropdownOpen={isPortDropdownOpen}
                  setHasSelectedPreview={(v) => {} /* not used */}
                  setIsDropdownOpen={setIsPortDropdownOpen}
                  previews={ports}
                />
              )}
              <IconButton icon="refresh" onClick={reloadPreview} title="Reload Preview" className="w-6 !max-h-6" />
              <IconButton
                icon="expand"
                onClick={toggleFullscreen}
                size={'sm'}
                title={isFullscreen ? 'Exit Full Screen' : 'Full Screen'}
                className="w-6 !max-h-6"
              />
            </div>
          </div>
          <div className="flex gap-2 overflow-auto">
            <PanelHeaderButton
              disabled={isDeploying || isStreaming}
              onClick={handleDeploy}
              className="flex items-center gap-2 px-3 py-1.5 rounded-md text-sm bg-[#4ADE80]/10 text-[#4ADE80] hover:bg-[#4ADE80]/30"
              disabledClassName="opacity-50 cursor-wait"
            >
              {isDeploying ? (
                'Deploying...'
              ) : (
                <>
                  <FaRocket /> Deploy
                </>
              )}
            </PanelHeaderButton>
            {appState?.cloudApp?.url && (
              <PanelHeaderButton
                disabled={isDeploying}
                onClick={openApp}
                className="flex items-center gap-2 px-3 py-1.5 rounded-md text-sm bg-[#4ADE80]/10 text-[#4ADE80] hover:bg-[#4ADE80]/30"
                disabledClassName="opacity-50 cursor-wait"
              >
                Live App
              </PanelHeaderButton>
            )}
          </div>
        </div>
        <div className="flex-1 border-t border-biela-elements-borderColor flex justify-center items-center overflow-auto">
          <div
            className="transition-width duration-300 ease-in-out"
            style={{ width: `${deviceMode?.value ?? 100}%`, height: '100%' }}
          >
            {ports.length > 0 ? (
              <>
                {isStreaming && mode === 'code' ? (
                  <div className="flex w-full h-full justify-center items-center bg-[#0A0F1C] text-gray-500 text-lg font-semibold">
                    {/*AI is coding...*/}
                    <LoadingAnimation></LoadingAnimation>
                  </div>
                ) : (
                  <iframe
                    key={iframeUrl}
                    ref={iframeRef}
                    src={normalizeUrl(iframeUrl)}
                    title="preview"
                    className="border-none w-full h-full bg-white"
                    allowFullScreen
                    onLoad={onIframeLoad}
                  />
                )}
                <ScreenshotSelector
                  isSelectionMode={isSelectionMode}
                  setIsSelectionMode={setIsSelectionMode}
                  containerRef={iframeRef}
                />
              </>
            ) : isStreaming ? (
              <div className="flex w-full h-full justify-center items-center bg-[#0A0F1C]">
                <LoadingAnimation />
              </div>
            ) : (
              <div className="flex w-full h-full justify-center items-center bg-[#0A0F1C] text-gray-500 text-lg font-semibold">
                <LoadingAnimation />
              </div>
            )}
          </div>
        </div>
      </div>
    );
  },
);

Preview.displayName = 'Preview';
