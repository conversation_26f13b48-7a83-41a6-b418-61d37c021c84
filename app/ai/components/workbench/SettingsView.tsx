import React, { useState } from 'react';

import SettingsHeader from './settings/SettingsHeader';
import ProjectInfoSection from './settings/ProjectInfoSection';
import DomainSection from './settings/DomainSection';
import DatabaseSection from '~/ai/components/workbench/settings/DatabaseSection';
import type { DropdownItems } from '~/components/common/Dropdown';
import type { ProjectCommits } from '~/ai/lib/stores/projects/code';

interface SettingsViewProps {
  hasCommited: boolean;
  isForkingApp: boolean;
  handleForkClick: (event: React.UIEvent) => void;
  commitToRestore?: DropdownItems;
  commits: ProjectCommits[] | null;
  handleRestoreAppClick: (event: React.UIEvent) => void;
  isStreaming?: boolean;
  showSaveTooltip: boolean;
  setShowSaveTooltip: (value: boolean) => void;
  handleSaveClick: (event: React.MouseEvent<HTMLButtonElement>) => void;
  isSavingApp: boolean;
}
const SettingsView: React.FC<SettingsViewProps> = ({hasCommited,isForkingApp,handleForkClick,commitToRestore,commits,handleRestoreAppClick,isStreaming,showSaveTooltip,setShowSaveTooltip,handleSaveClick,isSavingApp}) => {
  const [activeSection, setActiveSection] = useState<'project' | 'domain' | 'database'>('project');

  const renderSection = () => {
    switch (activeSection) {
      case 'project':
        return <ProjectInfoSection hasCommited={hasCommited} isForkingApp={isForkingApp} handleForkClick={handleForkClick} commitToRestore={commitToRestore} commits={commits} handleRestoreAppClick={handleRestoreAppClick} isStreaming={isStreaming} showSaveTooltip={showSaveTooltip} setShowSaveTooltip={setShowSaveTooltip} handleSaveClick={handleSaveClick} isSavingApp={isSavingApp} />;
      case 'domain':
        return <DomainSection />;
      case 'database':
        return <DatabaseSection />;
      default:
        return <ProjectInfoSection  hasCommited={hasCommited} isForkingApp={isForkingApp} handleForkClick={handleForkClick} commitToRestore={commitToRestore} commits={commits} handleRestoreAppClick={handleRestoreAppClick} isStreaming={isStreaming} showSaveTooltip={showSaveTooltip} setShowSaveTooltip={setShowSaveTooltip} handleSaveClick={handleSaveClick} isSavingApp={isSavingApp} />;
    }
  };

  return (
    <div className="h-full bg-[#0A0F1C] flex flex-col">
      <SettingsHeader activeSection={activeSection} setActiveSection={setActiveSection} />
      <div className="flex-1 overflow-y-auto p-6">
        {renderSection()}
      </div>
    </div>
  );
};

export default SettingsView;
