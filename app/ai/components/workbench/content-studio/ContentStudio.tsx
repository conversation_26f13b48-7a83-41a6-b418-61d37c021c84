import React, { useState, useEffect, useRef } from 'react';
import { ArrowPathIcon } from '@heroicons/react/24/outline';
import FolderNavigation from './components/folder/FolderNavigation';
import ContentView from './components/content/ContentView';
import ImageDetailsPage from './components/details/ImageDetailsPage';
import UploadModal from './components/upload/UploadModal';
import FilterPanel from './components/filters/FilterPanel';
import MoveToFolderDialog from './components/batch/MoveToFolderDialog';
import BatchTagEditor from './components/batch/BatchTagEditor';
import ConfirmationDialog from './components/common/ConfirmationDialog';
import SelectedImagesBar from './components/batch/SelectedImagesBar';
import ContentToolbar from './components/content/ContentToolbar';
import FolderCreationModal from './components/folder/FolderCreationModal';
import FolderRenameModal from './components/folder/FolderRenameModal';
import { createFolder, deleteFolder, getFoldersV2, renameFolder, getTagsV2, createResource, deleteResource, getResourcesV2, updateResource, createTag } from '../../../lib/stores/contentStudio';
import { toast } from "react-toastify";
import { useTranslation } from 'react-i18next';
import { Image, Folder, Tag, Filters, UploadFile } from '../../../../types/contentStudio';

interface ContentStudioProps {
  onInsertImages?: (images: { url: string; title: string; id: string; type: string; thumbnailUrl: string }[]) => Promise<void>;
}

const ContentStudio = ({ onInsertImages = undefined }: ContentStudioProps) => {
  const { t } = useTranslation('contentStudio');
  const [selectedFolderId, setSelectedFolderId] = useState<string>('all');
  const [images, setImages] = useState<Image[]>([]);
  const [totalImages, setTotalImages] = useState<number>(0);
  const [page, setPage] = useState<number>(0);
  const [totalPages, setTotalPages] = useState<number>(2);
  const [itemsPerPage, setItemsPerPage] = useState<number>(20);
  const [foldersPage, setFoldersPage] = useState<number>(0);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [selectedImageForDetails, setSelectedImageForDetails] = useState<Image | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isUploadModalOpen, setIsUploadModalOpen] = useState<boolean>(false);
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState<boolean>(false);
  const [isMoveToFolderOpen, setIsMoveToFolderOpen] = useState<boolean>(false);
  const [isTagEditorOpen, setIsTagEditorOpen] = useState<boolean>(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [debouncedTerm, setDebouncedTerm] = useState<string>(searchTerm);
  const [sortOption, setSortOption] = useState<string>('newest');
  const [isSortDropdownOpen, setIsSortDropdownOpen] = useState<boolean>(false);
  const [filters, setFilters] = useState<Filters>({
    dateFrom: '',
    dateTo: '',
    tags: [],
    searchTerm: ''
  });
  const [folders, setFolders] = useState<Folder[]>([]);
  const [allFolders, setAllFolders] = useState<Folder[]>([]);
  const [isFolderCreationOpen, setIsFolderCreationOpen] = useState<boolean>(false);
  const [folderCreationParentId, setFolderCreationParentId] = useState<string | null>(null);
  const [isFolderRenameOpen, setIsFolderRenameOpen] = useState<boolean>(false);
  const [folderToRename, setFolderToRename] = useState<Folder | null>(null);
  const [isFolderDeleteConfirmOpen, setIsFolderDeleteConfirmOpen] = useState<boolean>(false);
  const [folderToDelete, setFolderToDelete] = useState<Folder | null>(null);
  const [tags, setTags] = useState<Tag[]>([]);
  const [showImageDetailsPage, setShowImageDetailsPage] = useState<boolean>(false);

  const contentAreaRef = useRef<HTMLDivElement | null>(null);
  const contentStudioRef = useRef<HTMLDivElement | null>(null);
  const resourceAreaRef = useRef<HTMLDivElement | null>(null);

  const pageRef = useRef<number>(page);
  const isLoadingMoreImagesRef = useRef<boolean>(false);
  const isLoadingMoreFoldersRef = useRef<boolean>(false);

  useEffect(() => {
    setSelectedImages([]);
    getImages();
  }, [sortOption, filters, selectedFolderId, debouncedTerm])

  // Waiting 200ms after the user last typed something, so we don't call the api for each keystroke.
  useEffect(() => {
    const timeout = setTimeout(() => {
      setDebouncedTerm(searchTerm);
    }, 200);

    return () => clearTimeout(timeout);
  }, [searchTerm]);

  // Check if any filters are active
  const isFilterActive = !(filters.dateFrom == '' && filters.dateTo == '' && filters.tags.length == 0);

  const allSelectedImageObjects = images.filter(img => selectedImages.includes(img.id));

  const getFolderPath = (folderId: string): Folder[] => {
    const result: Folder[] = [];
    let counter = 1;
    let currentFolder = allFolders.find(folder => folder.id === folderId);

    if (!currentFolder || currentFolder.isRoot) return result;

    result.unshift(currentFolder);

    while (currentFolder && currentFolder.parentId) {
      currentFolder = allFolders.find(folder => folder.id === currentFolder!.parentId);
      if (currentFolder && !currentFolder.isRoot) {
        counter++;
        if(counter > 4){
          result.unshift({...currentFolder, name: '...'});
        } else {
          result.unshift(currentFolder);
        }
      }
    }

    return result;
  };

  // Get folder path for breadcrumb
  const folderPath = getFolderPath(selectedFolderId);

  // Function to close details page - now with priority
  const closeDetailsPage = () => {
    if (showImageDetailsPage) {
      // Immediately set state to ensure it closes
      setShowImageDetailsPage(false);
      setSelectedImageForDetails(null);
    }
  };

  // Add effect to ensure details page closes when dropdown is open
  useEffect(() => {
    if (isSortDropdownOpen && showImageDetailsPage) {
      closeDetailsPage();
    }
  }, [isSortDropdownOpen, showImageDetailsPage]);

  const handleSelectFolder = (folderId: string) => {
    setPage(0);
    // Close the image details page when selecting a different folder
    closeDetailsPage();
    // Change folder
    setSelectedFolderId(folderId);
    // Unselect images (because we only load the visible (in the selected folder) images, we can't show the preview of images outside of it)
    setSelectedImages([]);
  };

  const handleSelectImage = (imageId: string) => {
    // Simple toggle selection
    setSelectedImages(prev => {
      if (prev.includes(imageId)) {
        return prev.filter(id => id !== imageId);
      } else {
        return [...prev, imageId];
      }
    });
  };

  const handleViewImageDetails = (imageId: string) => {
    // Find the image regardless of current folder
    const image = images.find(img => img.id === imageId);

    if (image) {
      setSelectedImageForDetails(image);
      setShowImageDetailsPage(true);
    }
  };

  const handleInsertImages = () => {
    if (selectedImages.length > 0 && onInsertImages) {
      // Close details page if open
      closeDetailsPage();

      // Pass the selected image objects to the parent component
      if (onInsertImages) {
        onInsertImages(allSelectedImageObjects as { url: string; title: string; id: string; type: string, thumbnailUrl: string }[]);
      }
      // Clear selection after insert
      setSelectedImages([]);
    }
  };

  const handleUpload = () => {
    // Close details page if open
    closeDetailsPage();

    setIsUploadModalOpen(true);
  };

  const handleUploadComplete = async (uploadedFiles: UploadFile[]) => {
    let uniqueTags: string[] = [];
    uploadedFiles.map(file => uniqueTags = [...new Set([...uniqueTags, ...(file.tags || [])])]);

    await Promise.all(
      uniqueTags.map(tag => {
        createTag(tag);
      })
    )

    await Promise.all(
      uploadedFiles.map(file => {
        return createResource(file);
      })
    )

    setIsUploadModalOpen(false);
    await getImages();
  };

  const handleSaveImageDetails = async (updatedImage: Image) => {
    await Promise.all(updatedImage.tags?.map(tag => {
      if(!selectedImageForDetails?.tags?.includes(tag)){
        return createTag(tag);
      } else {
        return Promise.resolve();
      }
    }) || []);

    await updateResource(updatedImage.id, updatedImage);
    await getImages();

    setSelectedImageForDetails(null);
    setShowImageDetailsPage(false);
  };

  const handleMoveImage = async (imageId: string, targetFolderId: string) => {
    // Update the image's folder in our local state

    await updateResource(imageId, {folderId: targetFolderId})
    await getImages();
  };

  const handleDeleteImage = async () => {
    if (selectedImageForDetails) {
      await deleteResource(selectedImageForDetails.id)
      await getImages();

      setSelectedImageForDetails(null);
      setShowImageDetailsPage(false);
    }
  };

  const handleToggleFilterPanel = () => {
    // Close details page if open
    closeDetailsPage();

    setIsFilterPanelOpen(!isFilterPanelOpen);
  };

  const handleUpdateFilters = (newFilters: typeof filters) => {
    setPage(0);
    setFilters(newFilters);
    setIsFilterPanelOpen(false);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Close details page if open when typing in search
    if (showImageDetailsPage && e.target.value !== searchTerm) {
      closeDetailsPage();
    }

    setSearchTerm(e.target.value);
  };

  // Handler for changing view mode (grid/list)
  const handleChangeViewMode = (mode: 'grid' | 'list') => {
    // Close details page if open when changing view mode
    closeDetailsPage();

    setViewMode(mode);
  };

  // Handler for sort dropdown toggle - Improved version
  const handleToggleSortDropdown = (isOpen: boolean) => {
    setIsSortDropdownOpen(isOpen);

    // Force close details page when dropdown is opened
    if (isOpen) {
      closeDetailsPage();
    }
  };

  const handleChangeSortBy = (option: string) => {
    // Close details page if open when changing sort
    closeDetailsPage();
    setSortOption(option);
  };

  const handleMoveToFolder = () => {
    if (selectedImages.length > 0) {
      setIsMoveToFolderOpen(true);
    }
  };

  const handleBatchAddTags = () => {
    if (selectedImages.length > 0) {
      setIsTagEditorOpen(true);
    }
  };

  const handleBatchDelete = () => {
    if (selectedImages.length > 0) {
      setIsDeleteConfirmOpen(true);
    }
  };

  const handleConfirmMove = async (targetFolderId: string) => {
    // Move all selected images to the target folder
    await Promise.all(
      selectedImages.map((image) => { 
        return updateResource(image, {folderId: targetFolderId})
      })
    )
    setIsMoveToFolderOpen(false);

    await getImages();
  };

  const handleConfirmAddTags = async (tagsToAdd: string[]) => {
    // Add tags to all selected images
    await Promise.all(
      selectedImages.map((imgId) => {
        const img = images.find(img => img.id === imgId)
        const previousTags = img?.tags || [];
        const newTags = [...new Set([...previousTags, ...tagsToAdd])];

        return updateResource(imgId, { tags: newTags });
      })
    );

    setShowImageDetailsPage(false);
    setSelectedImageForDetails(null);
    setIsTagEditorOpen(false);
    await getImages();
  };

  const handleConfirmDelete = async () => {
    // Delete all selected images
    await Promise.all(
      selectedImages.map(async (image) => {
        await deleteResource(image)
      })
    )

    setIsDeleteConfirmOpen(false);
    setSelectedImages([]);
    await getImages();
  };

  const handleClearSelection = () => {
    setSelectedImages([]);
  };

  // getImages will call with argument 0 so we can reset the page and show the beginning of images. if it's called from loadMoreImages, it should take the current page number
  const fetchImages = async (pageNumber: number | null = null) => {
    if(pageNumber != null){
      setPage(0);
    } else {
      pageNumber = page;
    }

    let sortField, sortDirection;

    switch(sortOption){
      case 'newest':
        sortField = 'createdAt';
        sortDirection = 'DESC';
        break;
      case 'oldest':
        sortField = 'createdAt';
        sortDirection = 'ASC';
        break;
      case 'name-desc':
        sortField = 'title';
        sortDirection = 'DESC';
        break;
      case 'name-asc':
        sortField = 'title';
        sortDirection = 'ASC';
        break;
      case 'size-desc':
        sortField = 'size';
        sortDirection = 'DESC';
        break;
      case 'size-asc':
        sortField = 'size';
        sortDirection = 'ASC';
        break;
    }

    const resultImages = await getResourcesV2(pageNumber, itemsPerPage, sortField, sortDirection, filters, selectedFolderId, debouncedTerm, true);

    return resultImages;
  }

  // This is used when folders / filters are changed
  const getImages = async () => {
    setIsLoading(true);

    const resultImages = await fetchImages(0);

    setImages(resultImages?.items || []);
    setTotalPages(resultImages?.totalPages || 0)
    setTotalImages(resultImages?.total || 0);
    setIsLoading(false);
  }

  // This is used for infinite scrolling, triggering the below useEffect. Using pageRef because setPage is async and caused troubles.
  const loadMoreImages = async () => {
    if (isLoadingMoreImagesRef.current || pageRef.current + 1 >= totalPages) return;

    isLoadingMoreImagesRef.current = true;
    setPage(prev => prev + 1);
  };

  useEffect(() => {
    pageRef.current = page;

    const updateImages = async () => {
      const resultImages = await fetchImages();
      setImages(prev => [...(prev || []), ...(resultImages?.items || [])]);

      isLoadingMoreImagesRef.current = false;
    };
    // When page is 0, we don't need to add them again.
    if(page){
      updateImages();
    }
  }, [page]);

  const fetchFolders = async (foldersSearchTerm = "") => {
    return await getFoldersV2(foldersPage, 50, "updatedAt", "DESC", foldersSearchTerm);
  }

  const getFolders = async (foldersSearchTerm = "") => {
    const resultFolders = await fetchFolders(foldersSearchTerm);

    if(resultFolders){
      setFolders(resultFolders.items);

      // this is used to set allFolders, when there is no searchTerm
      if(foldersSearchTerm === ""){
        setAllFolders(resultFolders.items);
      }
    }
  }

  const getTags = async (searchTerm = "") => {
    const resultTags = await getTagsV2(0, 10, "updatedAt", "DESC", searchTerm);

    if(resultTags){
      setTags(resultTags.items);
    }
  }

  // Folder creation handling
  const handleOpenFolderCreation = (parentId: string | null = null) => {
    // Close details page if open
    closeDetailsPage();

    setFolderCreationParentId(parentId);
    setIsFolderCreationOpen(true);
  };

  const handleCreateFolder = async (folderData: { name: string; parentId: string | null }): Promise<void> => {
    const newFolder = await createFolder(folderData.name, folderData.parentId);
    await getFolders();
    if(newFolder?.id){
      setSelectedFolderId(newFolder.id);
      localStorage.setItem('latestCreatedFolder', newFolder.id);
    }

    // Close the modal
    setIsFolderCreationOpen(false);
  };

  // Folder rename handling
  const handleOpenFolderRename = (folder: Folder) => {
    setFolderToRename(folder);
    setIsFolderRenameOpen(true);
  };

  const handleRenameFolder = async (updatedFolder: Folder) => {
    await renameFolder(updatedFolder.id, updatedFolder.name);
    await getFolders();

    setIsFolderRenameOpen(false);
  };

  // Folder delete handling
  const handleOpenFolderDelete = (folder: Folder) => {
    setFolderToDelete(folder);
    setIsFolderDeleteConfirmOpen(true);
  };

  const handleDeleteFolder = async () => {
    if (!folderToDelete) return;

    // Check if folder has images
    const folderHasImages = images.some(img => img.folderId === folderToDelete.id);

    // Check if folder has subfolders
    const folderHasSubfolders = folders.some(folder => folder.parentId === folderToDelete.id);

    if (folderHasImages || folderHasSubfolders) {
      // Show warning that folder is not empty
      toast.error(t('cannotDeleteNonEmptyFolder', 'Cannot delete non-empty folder. Please move or delete its contents first.'));
      setIsFolderDeleteConfirmOpen(false);
      return;
    }

    // Delete the folder
    await deleteFolder(folderToDelete.id);
    await getFolders();

    // If the deleted folder was selected, select 'all' folder
    if (selectedFolderId === folderToDelete.id) {
      setSelectedFolderId('all');
    }

    setIsFolderDeleteConfirmOpen(false);
    setFolderToDelete(null);
  };

  // Reset filter panel state when component unmounts
  useEffect(() => {
    const setup = async () => {
      try {
        await Promise.all([
          getFolders(),
          getTags(),
        ]);
      } catch (error) {
        console.error("Error during setup: ", error);
      }
    }

    setup();

    return () => {
      setIsFilterPanelOpen(false);
    };
  }, []);

  return (
    <div
    className="h-full bg-[#0A0F1C] flex flex-col relative"
    ref={contentStudioRef}
    onDragEnter={() => {setIsUploadModalOpen(true)}}
    >
      <div className="flex-1 flex overflow-hidden">
        {/* Left sidebar - Folder Navigation */}
        <div className="w-64 border-r border-white/10 flex flex-col bg-[#1A1F2E]/50">
          <div className="h-[61px] p-4 border-b border-white/10">
            <h2 className="text-lg font-medium text-white">{t('title', 'Content Studio')}</h2>
          </div>
          <div className="flex-1 overflow-y-auto">
            <FolderNavigation
              folders={folders}
              getFolders={getFolders}
              selectedFolderId={selectedFolderId}
              onSelectFolder={handleSelectFolder}
              onCreateFolder={handleOpenFolderCreation}
              onRenameFolder={handleOpenFolderRename}
              onDeleteFolder={handleOpenFolderDelete}
            />
          </div>
        </div>

        {/* Main content area */}
        <div className="flex-1 flex flex-col relative" ref={contentAreaRef}>
          
          {/* Main Content Container */}
          <div className="h-full flex-1 flex flex-col relative">
            {/* This is the toolbar */}
            {!showImageDetailsPage && (
              <ContentToolbar
                currentFolder={selectedFolderId}
                folderPath={folderPath}
                viewMode={viewMode}
                sortBy={sortOption}
                onChangeViewMode={handleChangeViewMode}
                onChangeSortBy={handleChangeSortBy}
                onToggleFilterPanel={handleToggleFilterPanel}
                onNavigateToFolder={handleSelectFolder}
                isFilterActive={isFilterActive}
                onSortDropdownToggle={handleToggleSortDropdown}
              />
            )}

            {/* Search Bar */}
            { !((totalImages == 0 && searchTerm == "") || showImageDetailsPage) && (
              <div className="h-[61px] p-4 border-b border-white/10 flex items-center">
                <div className="relative w-full">
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={handleSearchChange}
                    placeholder={t('searchPlaceholder', 'Search images...')}
                    className="w-full bg-white/5 border border-white/10 rounded-lg pl-10 pr-4 py-2 text-white focus:outline-none focus:border-[#22D3EE]/30 placeholder-white/30"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="w-5 h-5 text-white/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                </div>
              </div>
            )}
            
            {/* Content Area - This is the resource area */}
            <div className="h-full flex-1 relative" ref={resourceAreaRef}>
            { isLoading ? (
                <div className="flex-1 flex items-center justify-center p-6 h-full">
                  <div className="w-full h-full flex flex-col items-center justify-center transition-all rounded-xl p-10">
                    <div className="mb-6 w-20 h-20 rounded-full bg-white/5 flex items-center justify-center">
                      <ArrowPathIcon className="w-10 h-10 text-white/40 animate-spin" />
                    </div>
                    <h3 className="text-xl font-light text-white/90 mb-2">{t('loading', 'Loading...')}</h3>
                  </div>
                </div>
              ) : (
                !showImageDetailsPage && (
                  <ContentView
                    currentFolder={selectedFolderId}
                    images={images}
                    loadMoreImages={loadMoreImages}
                    selectedImages={selectedImages}
                    onSelectImage={handleSelectImage}
                    onNavigateToFolder={handleSelectFolder}
                    onViewDetails={handleViewImageDetails}
                    viewMode={viewMode}
                    folders={folders}
                    handleUpload={handleUpload}
                  />
                )
              )}

              {/* Image Details Page - overlay in the resource area */}
              {selectedImageForDetails && showImageDetailsPage && (
                <ImageDetailsPage
                  image={selectedImageForDetails}
                  isOpen={showImageDetailsPage}
                  onClose={() => {
                    setShowImageDetailsPage(false);
                    setSelectedImageForDetails(null);
                  }}
                  onSave={handleSaveImageDetails}
                  onDelete={handleDeleteImage}
                  onMove={handleMoveImage}
                  contentStudioRef={contentStudioRef}
                  folders={folders}
                />
              )}

              {/* Filter Panel - positioned relative to the content area */}
              <FilterPanel
                isOpen={isFilterPanelOpen}
                onClose={() => setIsFilterPanelOpen(false)}
                imagesLen={images.length}
                filters={filters}
                onUpdateFilters={handleUpdateFilters}
                getTags={getTags}
                allTags={tags}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Selected Images Bar - Always present with fixed height */}
      <SelectedImagesBar
        selectedImages={allSelectedImageObjects}
        onViewDetails={handleViewImageDetails}
        onClearSelection={handleClearSelection}
        onMoveToFolder={handleMoveToFolder}
        onAddTags={handleBatchAddTags}
        onDelete={handleBatchDelete}
        onInsert={handleInsertImages}
        onUpload={handleUpload}
      />

      {/* ALL MODALS - Positioned relative to Content Studio container with higher z-index */}

      {/* Upload Modal */}
      <UploadModal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        onUploadComplete={handleUploadComplete}
        contentAreaRef={contentAreaRef}
        contentStudioRef={contentStudioRef}
        allFolders={folders}
        parentSelectedFolder={selectedFolderId}
      />

      {/* Move to Folder Dialog */}
      <MoveToFolderDialog
        folders={folders}
        isOpen={isMoveToFolderOpen}
        onClose={() => setIsMoveToFolderOpen(false)}
        onMove={handleConfirmMove}
        selectedCount={selectedImages.length}
        currentFolder={selectedFolderId}
        contentStudioRef={contentStudioRef}
      />

      {/* Batch Tag Editor */}
      <BatchTagEditor
        isOpen={isTagEditorOpen}
        onClose={() => setIsTagEditorOpen(false)}
        onAddTags={handleConfirmAddTags}
        selectedCount={selectedImages.length}
        contentStudioRef={contentStudioRef}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isDeleteConfirmOpen}
        onClose={() => setIsDeleteConfirmOpen(false)}
        title={t('confirmDeleteTitle', 'Confirm Delete')}
        message={t(
          'confirmDeleteMessage',
          'Are you sure you want to delete the selected images? This action cannot be undone.'
        )}
        confirmLabel={t('delete', 'Delete')}
        onConfirm={handleConfirmDelete}
        confirmVariant="danger"
        itemName={selectedImages.length === 1 ? allSelectedImageObjects[0]?.title : null}
        itemImageUrl={selectedImages.length === 1 ? allSelectedImageObjects[0]?.url : null}
        contentStudioRef={contentStudioRef}
      />

      {/* Folder Creation Modal */}
      <FolderCreationModal
        folders={folders}
        isOpen={isFolderCreationOpen}
        onClose={() => setIsFolderCreationOpen(false)}
        onCreateFolder={handleCreateFolder}
        parentFolderId={folderCreationParentId}
        contentStudioRef={contentStudioRef}
      />

      {/* Folder Rename Modal */}
      <FolderRenameModal
        isOpen={isFolderRenameOpen}
        onClose={() => setIsFolderRenameOpen(false)}
        onRenameFolder={handleRenameFolder}
        folder={folderToRename}
        allFolders={allFolders}
        contentStudioRef={contentStudioRef}
      />

      {/* Folder Delete Confirmation */}
      <ConfirmationDialog
        isOpen={isFolderDeleteConfirmOpen}
        onClose={() => setIsFolderDeleteConfirmOpen(false)}
        title={t('confirmDeleteFolderTitle', 'Delete Folder')}
        message={t('confirmDeleteFolderMessage', { folderName: folderToDelete?.name ?? '', defaultValue: 'Are you sure you want to delete the folder "{{folderName}}"? This action cannot be undone.' })}
        confirmLabel={t('deleteFolder', 'Delete Folder')}
        onConfirm={handleDeleteFolder}
        confirmVariant="danger"
        itemName={folderToDelete?.name}
        contentStudioRef={contentStudioRef}
        hideItem={true}
      />
    </div>
  );
}

export default ContentStudio;