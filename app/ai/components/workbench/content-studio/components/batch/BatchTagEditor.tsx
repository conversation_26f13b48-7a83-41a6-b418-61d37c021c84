import React, { useState, KeyboardEvent } from 'react';
import { motion } from 'framer-motion';
import { XMarkIcon, TagIcon, PlusIcon } from '@heroicons/react/24/outline';
import { createTag } from '../../../../../lib/stores/contentStudio';
import { useTranslation } from 'react-i18next';

interface BatchTagEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onAddTags: (tags: string[]) => void;
  selectedCount: number;
  contentStudioRef?: React.RefObject<HTMLDivElement | null>;
}

const BatchTagEditor: React.FC<BatchTagEditorProps> = ({ isOpen, onClose, onAddTags, selectedCount, contentStudioRef }) => {
  const { t } = useTranslation('contentStudio');
  const [tags, setTags] = useState<string[]>([]);
  const [inputValue, setInputValue] = useState<string>('');
  
  // Get all unique tags from images
  const allTags: string[] = [
    t('suggestedTags.app', 'app'),
    t('suggestedTags.code', 'code'),
    t('suggestedTags.coding', 'coding'),
    t('suggestedTags.coffee', 'coffee'),
    t('suggestedTags.css', 'css'),
    t('suggestedTags.design', 'design'),
    t('suggestedTags.development', 'development'),
    t('suggestedTags.html', 'html'),
    t('suggestedTags.javascript', 'javascript'),
    t('suggestedTags.laptop', 'laptop'),
    t('suggestedTags.mobile', 'mobile'),
    t('suggestedTags.programming', 'programming'),
    t('suggestedTags.react', 'react'),
    t('suggestedTags.screen', 'screen'),
    t('suggestedTags.web', 'web'),
    t('suggestedTags.webdev', 'webdev'),
    t('suggestedTags.workspace', 'workspace')
  ];
  
  // Filter suggestions based on input
  const filteredSuggestions = inputValue
    ? allTags.filter(tag => 
        tag.toLowerCase().includes(inputValue.toLowerCase()) && 
        !tags.includes(tag)
      ).slice(0, 5) // Limit to 5 suggestions
    : [];
  
  const handleAddTag = (tag: string) => {
    if (tag && !tags.includes(tag)) {
      setTags([...tags, tag]);
      setInputValue('');
    }
  };
  
  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };
  
  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && inputValue.trim()) {
      e.preventDefault();
      handleAddTag(inputValue.trim().toLowerCase());
    }
  };

  const createTags = async (tags: string[]) => {
    await Promise.all(tags.map(async tag => await createTag(tag)));
  }
  
  const handleSubmit = () => {
    // Add input as a tag
    if (tags.length > 0) {
      const updatedTags = [
        ...(tags || []),
        ...(inputValue ? [inputValue] : [])
      ]

      setInputValue('');

      createTags(updatedTags);
      onAddTags(updatedTags);
      setTags([]);
      onClose();
    }
  };
  
  if (!isOpen) return null;
  
  return (
    <div 
      className="absolute inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
      onClick={() => {
        onClose();
        setTags([]);
      }}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%'
      }}
    >
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        className="w-full max-w-md bg-[#0F1526] rounded-xl overflow-hidden shadow-xl"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-5">
          <h2 className="text-xl font-light text-[#22D3EE] flex items-center gap-2">
            <TagIcon className="w-5 h-5" />
            {t('addTags', 'Add Tags')}
          </h2>
          <button 
            onClick={() => {
              onClose();
              setTags([]);
            }}
            className="p-1.5 text-white hover:text-white/80 transition-colors"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>
        
        {/* Divider */}
        <div className="h-px bg-[#1A2035]"></div>
        
        <div className="p-6">
          <div className="mb-6">
            <label className="block text-white/70 text-sm mb-2">
            {selectedCount === 1
              ? t('enterTagsForSingle', 'Enter tags for 1 item')
              : t('enterTagsForMultiple', 'Enter tags for multiple items')}
            </label>
            
            <div className="flex flex-wrap gap-2 p-3 bg-[#1A2035] border border-white/10 rounded-lg mb-3">
              {tags.map(tag => (
                <div 
                  key={tag}
                  className="flex items-center gap-1 px-2 py-1 bg-[#22D3EE]/10 text-[#22D3EE] rounded-lg text-sm"
                >
                  <span>{tag}</span>
                  <button
                    onClick={() => handleRemoveTag(tag)}
                    className="hover:bg-white/10 rounded transition-colors"
                  >
                    <XMarkIcon className="w-3.5 h-3.5" />
                  </button>
                </div>
              ))}
              
              <div className="relative flex-1 min-w-[120px]">
                <input
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder={tags.length > 0 ? t('addAnotherTagPlaceholder', 'Add another tag...') : t('addTagPlaceholder', 'Add a tag...')}
                  className="w-full bg-transparent border-none text-white text-sm focus:outline-none placeholder-white/30"
                />
                
                {filteredSuggestions.length > 0 && (
                  <div className="absolute top-full left-0 right-0 mt-1 bg-[#1A2035] border border-white/10 rounded-lg shadow-lg z-10">
                    {filteredSuggestions.map(suggestion => (
                      <button
                        key={suggestion}
                        onClick={() => handleAddTag(suggestion)}
                        className="w-full text-left px-3 py-2 text-sm text-white/80 hover:bg-white/5 transition-colors"
                      >
                        {suggestion}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
            
            <p className="text-white/40 text-xs">
              {t('pressEnterToAddTag', 'Press Enter to add each tag. These tags will be added to all selected items.')}
            </p>
          </div>
          
          {allTags.length > 0 && (
            <div className="mb-6">
              <h3 className="text-white/70 text-sm mb-2">{t('commonlyUsedTags', 'Commonly used tags:')}</h3>
              <div className="flex flex-wrap gap-2">
                {allTags.slice(0, 8).map(tag => (
                  <button
                    key={tag}
                    onClick={() => handleAddTag(tag)}
                    disabled={tags.includes(tag)}
                    className={`flex items-center gap-1 px-2 py-1 rounded-lg text-xs ${
                      tags.includes(tag)
                        ? 'bg-[#1A2035] text-white/30 cursor-not-allowed'
                        : 'bg-[#1A2035] text-white/70 hover:bg-[#232940] transition-colors'
                    }`}
                  >
                    <PlusIcon className="w-3 h-3" />
                    {tag}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
        
        {/* Divider */}
        <div className="h-px bg-[#1A2035]"></div>
        
        {/* Footer */}
        <div className="p-5 flex items-center justify-end gap-3">
          <button
            onClick={() => {
              onClose();
              setTags([]);
            }}
            className="px-4 py-2 bg-white/5 text-white rounded-lg hover:bg-white/10 transition-colors"
          >
            {t('cancel', 'Cancel')}
          </button>
          <button
            onClick={handleSubmit}
            disabled={tags.length === 0}
            className={`px-4 py-2 rounded-lg transition-colors ${
              tags.length > 0
                ? 'bg-[#22D3EE] text-black hover:bg-[#22D3EE]/90'
                : 'bg-white/5 text-white/40 cursor-not-allowed'
            }`}
          >
            {t('addTags', 'Add Tags')}
          </button>
        </div>
      </motion.div>
    </div>
  );
}

export default BatchTagEditor;
