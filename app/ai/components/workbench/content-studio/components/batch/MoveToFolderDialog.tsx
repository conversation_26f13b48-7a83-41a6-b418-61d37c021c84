import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { XMarkIcon, FolderArrowDownIcon, FolderIcon, ChevronRightIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import { useTranslation } from 'react-i18next';
import { Folder } from '../../../../../../types/contentStudio';

interface MoveToFolderDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onMove: (folderId: string) => void;
  selectedCount: number;
  currentFolder: string;
  contentStudioRef?: React.RefObject<HTMLDivElement | null>;
  folders: Folder[];
}

const MoveToFolderDialog: React.FC<MoveToFolderDialogProps> = ({ isOpen, onClose, onMove, selectedCount, currentFolder, contentStudioRef, folders }) => {
  const { t } = useTranslation('contentStudio');
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>({});

  const handleToggleExpand = (folderId: string) => {
    setExpandedFolders(prev => ({
      ...prev,
      [folderId]: !prev[folderId]
    }));
  };
  
  // Filter out special folders, and organize by hierarchy
  const availableFolders = folders?.filter(folder => !folder.isRoot) || [];

  const renderFolderTree = (parentId: string | null = null, level: number = 0): React.ReactNode | null => {
    const folderItems = availableFolders.filter(folder => folder.parentId === parentId);
    
    if (folderItems.length === 0) return null;
    
    return (
      <div className="ml-4">
        {folderItems.map(folder => {
          const hasChildren = availableFolders.some(f => f.parentId === folder.id);
          const isExpanded = expandedFolders[folder.id];
          
          return (
            <div key={folder.id}>
              <div 
                className={`flex items-center gap-2 px-3 py-2 rounded-lg text-left my-1 ${
                  selectedFolderId === folder.id
                    ? 'bg-[#22D3EE]/10 text-[#22D3EE]'
                    : 'text-white/80 hover:bg-white/5'
                } transition-colors cursor-pointer`}
                style={{ paddingLeft: `${(level * 8) + 12}px` }}
                onClick={() => setSelectedFolderId(folder.id)}
              >
                {hasChildren && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleToggleExpand(folder.id);
                    }}
                    className="p-0.5 rounded bg-transparent hover:bg-white/10"
                  >
                    {isExpanded ? (
                      <ChevronDownIcon className="w-3.5 h-3.5 text-white/50" />
                    ) : (
                      <ChevronRightIcon className="w-3.5 h-3.5 text-white/50" />
                    )}
                  </button>
                )}
                {!hasChildren && <div className="w-4"></div>}
                <FolderIcon className="w-5 h-5" />
                <span>{folder.name}</span>
              </div>
              
              {hasChildren && isExpanded && renderFolderTree(folder.id, level + 1)}
            </div>
          );
        })}
      </div>
    );
  };
  
  const handleMove = () => {
    if (selectedFolderId) {
      onMove(selectedFolderId);
      onClose();
    }
  };
  
  if (!isOpen) return null;

  return (
    <div 
      className="absolute inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
      onClick={onClose}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%'
      }}
    >
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        className="w-[90%] max-w-md bg-[#1A1F2E] rounded-xl border border-white/10 overflow-hidden shadow-xl"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between p-4 border-b border-white/10">
          <h2 className="text-xl font-light text-white flex items-center gap-2" >
            <FolderArrowDownIcon className="w-5 h-5 text-[#22D3EE]" />
            {t('moveDialog.title', 'Move Items')}
          </h2>
          <button 
            onClick={onClose}
            className="p-2 rounded-full bg-transparent hover:bg-white/10 transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-white/70" />
          </button>
        </div>
        
        <div className="p-6">
          <div className="mb-6">
            <h3 className="text-white/70 text-sm mb-3">{t('moveDialog.selectDestination', 'Select destination folder:')}</h3>
            <div className="bg-white/5 rounded-lg border border-white/10 max-h-60 overflow-y-auto p-2">
              {/* Root folder option */}
              <div 
                className={`flex items-center gap-2 px-3 py-2 rounded-lg text-left ${
                  selectedFolderId === 'all'
                    ? 'bg-[#22D3EE]/10 text-[#22D3EE]'
                    : 'text-white/80 hover:bg-white/5'
                } transition-colors cursor-pointer mb-1`}
                onClick={() => setSelectedFolderId('all')}
              >
                <FolderIcon className="w-5 h-5" />
                <span>{t('moveDialog.rootOption', 'Root (All Files)')}</span>
              </div>
              
              {/* Top level folders */}
              {availableFolders.filter(folder => !folder.parentId).map(folder => {
                const hasChildren = availableFolders.some(f => f.parentId === folder.id);
                const isExpanded = expandedFolders[folder.id];
                
                return (
                  <div key={folder.id}>
                    <div 
                      className={`flex items-center gap-2 px-3 py-2 rounded-lg text-left ${
                        selectedFolderId === folder.id
                          ? 'bg-[#22D3EE]/10 text-[#22D3EE]'
                          : 'text-white/80 hover:bg-white/5'
                      } transition-colors cursor-pointer mb-1`}
                      onClick={() => setSelectedFolderId(folder.id)}
                    >
                      {hasChildren && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleToggleExpand(folder.id);
                          }}
                          className="p-0.5 rounded bg-transparent hover:bg-white/10"
                        >
                          {isExpanded ? (
                            <ChevronDownIcon className="w-3.5 h-3.5 text-white/50" />
                          ) : (
                            <ChevronRightIcon className="w-3.5 h-3.5 text-white/50" />
                          )}
                        </button>
                      )}
                      {!hasChildren && <div className="w-4"></div>}
                      <FolderIcon className="w-5 h-5" />
                      <span>{folder.name}</span>
                    </div>
                    
                    {/* Show children if expanded */}
                    {hasChildren && isExpanded && renderFolderTree(folder.id, 1)}
                  </div>
                );
              })}
              
              {availableFolders.length === 0 && (
                <div className="p-4 text-center text-white/50">
                  {t('moveDialog.noFolders', 'No other folders available')}
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-center justify-end gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-white/5 text-white rounded-lg hover:bg-white/10 transition-colors"
            >
              {t('cancel', 'Cancel')}
            </button>
            <button
              onClick={handleMove}
              disabled={!selectedFolderId}
              className={`px-4 py-2 rounded-lg transition-colors ${
                selectedFolderId
                  ? 'bg-[#22D3EE] text-black hover:bg-[#22D3EE]/90'
                  : 'bg-white/5 text-white/40 cursor-not-allowed'
              }`}
            >
              {t('moveDialog.moveButton', 'Move Items')}
            </button>
          </div>
        </div>
      </motion.div>
    </div>
  );
}

export default MoveToFolderDialog;
