import React from 'react';
import { XMarkIcon, FolderArrowDownIcon, TagIcon, TrashIcon, ArrowUpTrayIcon } from '@heroicons/react/24/outline';
import { useTranslation } from 'react-i18next';
import { Image } from '../../../../../../types/contentStudio';

interface SelectedImagesBarProps {
  selectedImages: Image[];
  onViewDetails: (imageId: string) => void;
  onClearSelection: () => void;
  onMoveToFolder: () => void;
  onAddTags: () => void;
  onDelete: () => void;
  onInsert: () => void;
  onUpload: () => void;
}

const SelectedImagesBar: React.FC<SelectedImagesBarProps> = ({
  selectedImages,
  onViewDetails,
  onClearSelection,
  onMoveToFolder,
  onAddTags,
  onDelete,
  onInsert,
  onUpload
}) => {
  const { t } = useTranslation('contentStudio');
  // Calculate the count of selected images
  const selectedCount = selectedImages.length;
  
  return (
    <div className="border-t border-white/10 p-4 bg-[#1A1F2E]/80 backdrop-blur-sm h-[72px] flex items-center">
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center gap-4">
          {/* Selected count pill - always visible */}
          <div className={`flex items-center gap-2 rounded-lg px-3 py-1.5 transition-colors duration-300 ${
            selectedCount > 0 
              ? "bg-[#22D3EE]/10 text-[#22D3EE]" 
              : "bg-white/5 text-white/50"
          }`}>
            <span className="text-sm">{selectedCount} {t('selected', 'selected')}</span>
            {selectedCount > 0 && (
              <button 
                onClick={onClearSelection}
                className="p-1 bg-transparent hover:bg-white/10 rounded-full transition-colors"
              >
                <XMarkIcon className="w-4 h-4" />
              </button>
            )}
          </div>
          
          {/* Show action buttons when images are selected, otherwise show upload button */}
          {selectedCount > 0 ? (
            <div className="flex items-center gap-2">
              <button
                onClick={onMoveToFolder}
                className="flex items-center gap-1 px-3 py-1.5 rounded-lg text-sm transition-colors bg-white/5 hover:bg-white/10 text-white/80"
              >
                <FolderArrowDownIcon className="w-4 h-4" />
                <span>{t('move', 'Move')}</span>
              </button>
              
              <button
                onClick={onAddTags}
                className="flex items-center gap-1 px-3 py-1.5 rounded-lg text-sm transition-colors bg-white/5 hover:bg-white/10 text-white/80"
              >
                <TagIcon className="w-4 h-4" />
                <span>{t('addTags', 'Add Tags')}</span>
              </button>
              
              <button
                onClick={onDelete}
                className="flex items-center gap-1 px-3 py-1.5 rounded-lg text-sm transition-colors bg-red-500/10 hover:bg-red-500/20 text-red-400"
              >
                <TrashIcon className="w-4 h-4" />
                <span>{t('delete', 'Delete')}</span>
              </button>
            </div>
          ) : (
            <button
              onClick={onUpload}
              className="flex items-center gap-1 px-3 py-1.5 bg-[#4ADE80] hover:bg-[#4ADE80]/90 rounded-lg text-sm text-black transition-colors"
            >
              <ArrowUpTrayIcon className="w-4 h-4" />
              <span>{t('uploadFiles', 'Upload Files')}</span>
            </button>
          )}
        </div>
        
        {/* Right side with fixed dimensions for thumbnails and insert button */}
        <div className="flex items-center gap-4">
          {/* Thumbnails container with fixed width/height */}
          <div className="h-12 flex items-center relative overflow-hidden">
            {selectedCount > 0 && (
              <div className="flex items-center gap-2 max-w-[300px] overflow-x-auto pr-2 no-scrollbar">
                {selectedImages.map(image => (
                  <div 
                    key={image.id}
                    className="relative flex-shrink-0 w-12 h-12 rounded-md overflow-hidden border border-[#22D3EE]/30 cursor-pointer hover:border-[#22D3EE] transition-colors"
                    onClick={() => onViewDetails(image.id)}
                  >
                      <img src={image.type.startsWith('video/') ? image.thumbnailUrl : image.url} alt={image.title || "Selected image"} className="w-full h-full object-cover" />
                  </div>
                ))}
              </div>
            )}
            {selectedCount > 5 && (<div className="pointer-events-none absolute top-0 right-0 h-full w-24 bg-gradient-to-l from-[#0f172a] to-transparent"></div>)}
          </div>
          
          {/* Insert button - conditionally enabled */}
          <button
            onClick={onInsert}
            disabled={selectedCount === 0}
            className={`min-w-[140px] h-10 px-4 rounded-lg transition-colors ${
              selectedCount > 0
                ? "bg-[#22D3EE] text-black hover:bg-[#22D3EE]/90" 
                : "bg-white/5 text-white/30 cursor-not-allowed"
            }`}
          >
            {t('useInProject', 'Use in Project')}
          </button>
        </div>
      </div>
    </div>
  );
}

export default SelectedImagesBar;
