import React from 'react';
import { motion } from 'framer-motion';
import { XMarkIcon, ExclamationTriangleIcon, TrashIcon } from '@heroicons/react/24/outline';
import { useTranslation } from 'react-i18next';

interface ConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmLabel?: string;
  cancelLabel?: string;
  confirmVariant?: 'danger' | 'default'; // Can be expanded for more styles
  icon?: 'warning' | 'info' | 'none'; // For future flexibility
  itemName?: string | null;
  itemImageUrl?: string | null;
  contentStudioRef?: React.RefObject<HTMLDivElement | null>;
  hideItem?: boolean;
}

const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({ 
  isOpen, 
  onClose, 
  title, 
  message, 
  confirmLabel, 
  cancelLabel,
  confirmVariant = 'danger',
  icon = 'warning',
  onConfirm,
  itemName = null,
  itemImageUrl = null,
  contentStudioRef,
  hideItem
}) => {
  const { t } = useTranslation('contentStudio');
  if (!isOpen) return null;
  
  return (
    <div 
      className="absolute inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
      onClick={onClose}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%'
      }}
    >
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        className="w-full max-w-md bg-[#0F1526] rounded-xl overflow-hidden shadow-xl"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-5">
          <h2 className="text-xl font-light text-red-500">
            {title}
          </h2>
          <button 
            onClick={onClose}
            className="p-1.5 text-white hover:text-white/80 transition-colors"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>
        
        {/* Divider */}
        <div className="h-px bg-[#1A2035]"></div>
        
        {/* Content */}
        <div className="p-6 space-y-5">
          {/* Warning message */}
          <div className="bg-[#1A1525] border border-red-500/30 rounded-lg p-5">
            <div className="flex gap-3">
              <ExclamationTriangleIcon className="w-6 h-6 text-red-500 flex-shrink-0" />
              <p className="text-white/90">
                {message}
              </p>
            </div>
          </div>
          
          {/* Item being deleted (optional) */}
          {!hideItem && itemName && (
            <div className="bg-[#1A2035] rounded-lg p-4">
              <div className="flex items-center gap-4">
                <img src={itemImageUrl || ''} className="w-12 h-12 bg-[#232940] object-cover rounded flex-shrink-0" />
                <div className="font-medium text-white text-lg">{itemName}</div>
              </div>
            </div>
          )}
        </div>
        
        {/* Divider */}
        <div className="h-px bg-[#1A2035]"></div>
        
        {/* Footer */}
        <div className="p-5 flex items-center justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-[#232940] hover:bg-[#232940]/70 rounded-lg transition-colors text-white"
          >
            {cancelLabel ?? t('cancel', 'Cancel')}
          </button>
          
          <button
            onClick={() => {
              onConfirm();
              onClose();
            }}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors text-white flex items-center gap-2"
          >
            <TrashIcon className="w-5 h-5" />
            {confirmLabel ?? t('delete', 'Delete')}
          </button>
        </div>
      </motion.div>
    </div>
  );
}

export default ConfirmationDialog;
