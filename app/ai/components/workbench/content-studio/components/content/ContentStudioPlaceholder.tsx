import React from "react";
import { motion } from "framer-motion";
import { ArrowUpTrayIcon } from '@heroicons/react/24/outline/index.js';
import { useTranslation } from 'react-i18next';

interface ContentStudioPlaceholderProps {
  handleUpload: () => void;
}

const ContentStudioPlaceholder: React.FC<ContentStudioPlaceholderProps> = ({ handleUpload }) => {
  const { t } = useTranslation('contentStudio');

  return (
      <div className="flex flex-col items-center justify-start sm:justify-center p-8 rounded-xl w-full max-h-[90vh] sm:h-screen overflow-y-auto">
      <div className="text-center max-w-md">
        {/* Added mt-12 to push the robot down within the container */}
        <div className="mb-6 flex flex-col items-center mt-12">
          {/* Robot head - KEEPING EXACTLY THE SAME AS ORIGINAL */}
          <motion.div
            className="w-24 h-24 mx-auto bg-green-500 rounded-t-3xl relative"
            animate={{
              y: [0, -10, 0],
            }}
            transition={{
              repeat: Infinity,
              duration: 2,
            }}
          >
            {/* Eyes */}
            <div className="absolute top-8.5 left-6.5 w-4.5 h-4.5 bg-white rounded-full">
              <motion.div
                className="w-2 h-2 bg-black rounded-full relative top-1.2 left-1.2"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ repeat: Infinity, duration: 2 }}
              />
            </div>
            <div className="absolute top-8.5 right-6.5 w-4.5 h-4.5 bg-white rounded-full">
              <motion.div
                className="w-2 h-2 bg-black rounded-full relative top-1.2 left-1.2"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ repeat: Infinity, duration: 2, delay: 0.1 }}
              />
            </div>

            {/* Mouth */}
            <motion.div
              className="absolute bottom-6 left-1/2 transform -translate-x-1/2 w-12 h-2 bg-white rounded-lg"
              animate={{
                width: [48, 32, 48],
                height: [8, 5, 8]
              }}
              transition={{ repeat: Infinity, duration: 1.5 }}
            />

            {/* Antenna */}
            <motion.div
              className="absolute -top-5 left-1/2 transform -translate-x-1/2 w-2 h-5 bg-green-600"
              animate={{ rotateZ: [-5, 5, -5] }}
              transition={{ repeat: Infinity, duration: 1.5 }}
            >
              <motion.div
                className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-green-400 rounded-full"
                animate={{
                  boxShadow: [
                    "0 0 5px #4ade80",
                    "0 0 15px #4ade80",
                    "0 0 5px #4ade80"
                  ]
                }}
                transition={{ repeat: Infinity, duration: 1.5 }}
              />
            </motion.div>
          </motion.div>

          {/* Robot body - KEEPING EXACTLY THE SAME AS ORIGINAL */}
          <div className="w-42 h-12 mx-auto bg-green-600 rounded-b-xl relative">
            {/* Buttons */}
            <motion.div
              className="absolute top-4.5 left-4.5 w-3 h-3 bg-green-400 rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ repeat: Infinity, duration: 1, delay: 0.2 }}
            />
            <motion.div
              className="absolute top-4.5 left-12.5 w-3 h-3 bg-green-400 rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ repeat: Infinity, duration: 1, delay: 0.4 }}
            />
            <motion.div
              className="absolute top-4.5 right-12.5 w-3 h-3 bg-green-400 rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ repeat: Infinity, duration: 1, delay: 0.6 }}
            />
            <motion.div
              className="absolute top-4.5 right-4.5 w-3 h-3 bg-green-400 rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ repeat: Infinity, duration: 1, delay: 0.8 }}
            />

            {/* Left arm and hand */}
            <motion.div
              className="absolute -left-12 top-2 w-12 h-3 bg-green-400 rounded-full origin-right"
              animate={{ rotate: [-5, 5, -5] }}
              transition={{ repeat: Infinity, duration: 2, delay: 0.2 }}
            >
              {/* Left hand */}
              <motion.div
                className="absolute -left-6 -top-4 w-8 h-8 bg-green-400 rounded-full flex items-center justify-center"
                animate={{ rotate: [0, -5, 0, 5, 0] }}
                transition={{ repeat: Infinity, duration: 3 }}
              >
                {/* Left picture */}
                <motion.div
                  className="w-12 h-12 bg-green-600 rounded border-1 border-green-900 overflow-hidden flex items-center justify-center shadow-md transform -rotate-12"
                  whileHover={{ scale: 1.1 }}
                  animate={{ rotate: [-12, -8, -12] }}
                  transition={{ repeat: Infinity, duration: 2 }}
                >
                  <svg className="w-6 h-6 text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                </motion.div>
              </motion.div>
            </motion.div>

            {/* Right arm and hand */}
            <motion.div
              className="absolute -right-12 top-2 w-12 h-3 bg-green-400 rounded-full origin-left"
              animate={{ rotate: [5, -5, 5] }}
              transition={{ repeat: Infinity, duration: 2, delay: 0.2 }}
            >
              {/* Right hand */}
              <motion.div
                className="absolute -right-6 -top-4 w-8 h-8 bg-green-400 rounded-full flex items-center justify-center"
                animate={{ rotate: [0, 5, 0, -5, 0] }}
                transition={{ repeat: Infinity, duration: 3 }}
              >
                {/* Right picture */}
                <motion.div
                  className="w-12 h-12 bg-green-600 rounded border-1 border-green-900 overflow-hidden flex items-center justify-center shadow-md transform rotate-12"
                  whileHover={{ scale: 1.1 }}
                  animate={{ rotate: [12, 8, 12] }}
                  transition={{ repeat: Infinity, duration: 2 }}
                >
                  <svg className="w-6 h-6 text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                </motion.div>
              </motion.div>
            </motion.div>
          </div>
        </div>

        {/* Updated text colors for better contrast against dark background */}
        <div className="mt-8">
          <h3 className="text-title font-medium text-white mb-2">
            {t('noImagesFound', 'Upload Images')}
          </h3>
          <p className="text-normal text-gray-300">
            {t('uploadSomeImages', 'Upload some images or select a different folder')}
          </p>
        </div>

        {/* Action buttons - Updated as requested */}
        <div className="mt-6 flex justify-center">
          <button
            onClick={handleUpload}
            className="flex items-center gap-2 px-6 py-2 bg-[#4ADE80] hover:bg-[#4ADE80]/90 text-black rounded-md hover:bg-ai-green-dark transition-colors">
            <ArrowUpTrayIcon className="w-4 h-4" />
           <span>
             {t('chooseAnImage', 'Choose an Image')}
           </span>
          </button>
        </div>
      </div>


    </div>
  );
};

export default ContentStudioPlaceholder;
