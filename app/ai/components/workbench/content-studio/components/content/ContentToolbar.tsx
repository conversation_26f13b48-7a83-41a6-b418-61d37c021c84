import React, { useState, useRef, useEffect } from 'react';
import { Squares2X2Icon, ListBulletIcon, ChevronRightIcon, FunnelIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';
import { useTranslation } from 'react-i18next';
import { Folder } from '../../../../../../types/contentStudio'

interface ContentToolbarProps {
  currentFolder: string;
  folderPath: Folder[];
  viewMode: 'grid' | 'list';
  sortBy: string;
  onChangeViewMode: (mode: 'grid' | 'list') => void;
  onChangeSortBy: (sort: string) => void;
  onToggleFilterPanel: () => void;
  onNavigateToFolder: (folderId: string) => void;
  isFilterActive: boolean;
  onSortDropdownToggle?: (open: boolean) => void;
}

const ContentToolbar: React.FC<ContentToolbarProps> = ({ 
  currentFolder, 
  folderPath, 
  viewMode,
  sortBy, 
  onChangeViewMode,
  onChangeSortBy,
  onToggleFilterPanel,
  onNavigateToFolder,
  isFilterActive,
  onSortDropdownToggle
}) => {
  const { t } = useTranslation('contentStudio');
  const [isSortDropdownOpen, setIsSortDropdownOpen] = useState(false);
  const sortRef = useRef<HTMLDivElement>(null);

  // Handle outside clicks for sort dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (sortRef.current && !sortRef.current.contains(event.target as Node)) {
        setIsSortDropdownOpen(false);
        onSortDropdownToggle && onSortDropdownToggle(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onSortDropdownToggle]);

  // Toggle sort dropdown and notify parent
  const handleSortDropdownToggle = () => {
    const newState = !isSortDropdownOpen;
    setIsSortDropdownOpen(newState);
    onSortDropdownToggle && onSortDropdownToggle(newState);
  };

  // Handle sort option selection
  const handleSortSelect = (option: string) => {
    onChangeSortBy(option);
    setIsSortDropdownOpen(false);
    onSortDropdownToggle && onSortDropdownToggle(false);
  };

  return (
    <div className="h-[61px] flex items-center justify-between p-4 border-b border-white/10">
      <div className="flex items-center gap-1 text-sm text-white/50 max-w-[600px]">
        <button 
          onClick={() => onNavigateToFolder('all')}
          className="hover:text-white/80 transition-colors"
        >
          {t('allFiles', 'All Files')}

        </button>
        
        {folderPath.map((folder, index) => (
          <React.Fragment key={folder.id}>
            <ChevronRightIcon className="w-3 h-3" />
            <button 
              onClick={() => onNavigateToFolder(folder.id)}
              className={clsx(
                "hover:text-white/80 transition-colors",
                index === folderPath.length - 1 && "text-white/70"
              )}
            >
              {folder.name}
            </button>
          </React.Fragment>
        ))}
      </div>
      
      <div className="flex items-center space-x-2">
        {/* Sort Dropdown */}
        <div className="relative" ref={sortRef}>
          <button 
            onClick={handleSortDropdownToggle}
            className="flex items-center gap-2 px-3 py-1.5 bg-white/5 rounded-lg text-white/70 hover:bg-white/10 transition-colors text-sm"
          >
            <span>
              {t('sort.label', 'Sort')}: {
                sortBy === 'newest' ? t('sort.newest', 'Newest First') :
                sortBy === 'oldest' ? t('sort.oldest', 'Oldest First') :
                sortBy === 'name-asc' ? t('sort.nameAsc', 'Name (A-Z)') :
                sortBy === 'name-desc' ? t('sort.nameDesc', 'Name (Z-A)') :
                sortBy === 'size-desc' ? t('sort.sizeDesc', 'Size (Largest)') :
                sortBy === 'size-asc' ? t('sort.sizeAsc', 'Size (Smallest)') :
                t('sort.newest', 'Newest First')
              }
            </span>
            <ChevronDownIcon className={`w-4 h-4 transition-transform ${isSortDropdownOpen ? 'rotate-180' : ''}`} />
          </button>
          
          {isSortDropdownOpen && (
            <div className="absolute top-full right-0 mt-1 w-48 bg-[#1A1F2E] border border-white/10 rounded-lg shadow-xl z-50">
              <div className="py-1">
                {[
                  { id: 'newest', label: t('sort.newest', 'Newest First') },
                  { id: 'oldest', label: t('sort.oldest', 'Oldest First') },
                  { id: 'name-asc', label: t('sort.nameAsc', 'Name (A-Z)') },
                  { id: 'name-desc', label: t('sort.nameDesc', 'Name (Z-A)') },
                  { id: 'size-desc', label: t('sort.sizeDesc', 'Size (Largest)') },
                  { id: 'size-asc', label: t('sort.sizeAsc', 'Size (Smallest)') }
                ].map(option => (
                  <button
                    key={option.id}
                    onClick={() => handleSortSelect(option.id)}
                    className={clsx(
                      'w-full text-left px-4 py-2 text-sm transition-colors',
                      option.id === sortBy
                        ? 'bg-[#22D3EE]/10 text-[#22D3EE]'
                        : 'text-white/70 hover:bg-white/5'
                    )}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
        
        {/* Filter Button */}
        <button 
          onClick={onToggleFilterPanel}
          className={clsx(
            'flex items-center gap-2 px-3 py-1.5 rounded-lg transition-colors filter-toggle-button text-sm',
            isFilterActive
              ? 'bg-[#22D3EE]/10 text-[#22D3EE]' 
              : 'bg-white/5 text-white/70 hover:bg-white/10'
          )}
        >
          <FunnelIcon className="w-4 h-4" />
          <span>{t('filters', 'Filters')}</span>
        </button>
        
        {/* View Mode Toggles */}
        <div className="flex bg-white/5 rounded-md overflow-hidden">
          <button
            onClick={() => onChangeViewMode('grid')}
            className={clsx(
              'p-1.5',
              viewMode === 'grid' ? 'bg-white/10 text-white' : 'text-white/50 hover:bg-white/5'
            )}
          >
            <Squares2X2Icon className="w-5 h-5" />
          </button>
          <button
            onClick={() => onChangeViewMode('list')}
            className={clsx(
              'p-1.5',
              viewMode === 'list' ? 'bg-white/10 text-white' : 'text-white/50 hover:bg-white/5'
            )}
          >
            <ListBulletIcon className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
}

export default ContentToolbar;
