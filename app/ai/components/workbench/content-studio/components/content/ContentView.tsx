import React, { useRef, useState, useEffect } from 'react';
import ImageGrid from './ImageGrid';
import ListView from './ListView';
import { Image, Folder } from '../../../../../../types/contentStudio';

interface ContentViewProps {
  currentFolder: string;
  images: Image[];
  loadMoreImages: () => Promise<void>;
  selectedImages: string[];
  onSelectImage: (id: string) => void;
  onNavigateToFolder: (id: string) => void;
  onViewDetails: (id: string) => void;
  viewMode: 'grid' | 'list';
  folders: Folder[];
  handleUpload: () => void;
}

const ContentView: React.FC<ContentViewProps> = ({
  currentFolder,
  images,
  loadMoreImages,
  selectedImages,
  onSelectImage,
  onNavigateToFolder,
  onViewDetails,
  viewMode = 'grid',
  folders,
  handleUpload
}) => {
  const scrollRef = useRef<HTMLDivElement | null>(null);

  const handleScroll = async () => {
    await loadMoreImages();
  };

  useEffect(() => {
    const el = scrollRef.current;
    if(!el) return;

    const onScroll = () => {
      const scrollBottom = el.scrollHeight - el.scrollTop - el.clientHeight;
      if(scrollBottom < 100){
        handleScroll();
      }
    }

    el.addEventListener('scroll', onScroll);
    return () => el.removeEventListener('scroll', onScroll);
  }, []);

  return (
    <div ref={scrollRef} className="h-full flex flex-col bg-[#0A0F1C]/50 overflow-y-auto">
      {viewMode === 'grid' ? (
        <ImageGrid
          images={images}
          selectedImages={selectedImages}
          onSelectImage={onSelectImage}
          onViewDetails={onViewDetails}
          folders={folders}
          handleUpload={handleUpload}
        />
      ) : (
        <ListView
          images={images}
          selectedImages={selectedImages}
          onSelectImage={onSelectImage}
          onViewDetails={onViewDetails}
          handleUpload={handleUpload}
        />
      )}
    </div>
  );
}

export default ContentView;
