import React from 'react';
import ImageItem from './ImageItem';
import { formatDate, formatFileSize } from '../../utils/fileUtils';
import { useTranslation } from 'react-i18next';
import ContentStudioPlaceholder from './ContentStudioPlaceholder';
import { Image, Folder } from '../../../../../../types/contentStudio';

interface ImageGridProps {
  images: Image[];
  selectedImages: string[];
  onSelectImage: (id: string) => void;
  onViewDetails: (id: string) => void;
  folders: Folder[];
  handleUpload: () => void;
}

const ImageGrid: React.FC<ImageGridProps> = ({ images, selectedImages, onSelectImage, onViewDetails, folders, handleUpload }) => {
  const { t } = useTranslation('contentStudio');
  if (images.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <ContentStudioPlaceholder handleUpload={handleUpload}/>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4 p-4 pb-38">
      {images.map(image => (
        <div key={image.id} className="flex flex-col">
          {/* ImageItem is completely self-contained for selection handling */}
          <ImageItem
            image={image}
            isSelected={selectedImages.includes(image.id)}
            onSelect={onSelectImage}
            onViewDetails={onViewDetails}
          />

          {/* Metadata below the image - static, doesn't change on selection */}
          <div className="mt-2">
            <div className="font-medium text-white text-sm truncate">{image.title}</div>
            <div className="flex items-center justify-between text-xs text-white/50 mt-1">
              <div className="flex items-center gap-1">
                <svg className="w-3.5 h-3.5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                </svg>
                <span>{formatDate(image.createdAt, 'short')}</span>
              </div>
              <span>{formatFileSize(image.size)}</span>
            </div>
            <div className="mt-1 text-xs">
              <span className="px-2 py-0.5 bg-white/5 rounded text-white/60 text-[10px]">
                {folders?.find(folder => folder.id === image.folderId)?.name || t('allFiles', 'All Files')}
              </span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

export default ImageGrid;
