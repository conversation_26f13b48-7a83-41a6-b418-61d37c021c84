import React from 'react';
import { CheckCircleIcon, EyeIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';
import { useTranslation } from 'react-i18next';
import { Image } from '../../../../../../types/contentStudio';

interface ImageItemProps {
  image: Image;
  isSelected: boolean;
  onSelect: (id: string) => void;
  onViewDetails: (id: string) => void;
}

const ImageItem: React.FC<ImageItemProps> = ({ image, isSelected, onSelect, onViewDetails }) => {
  const { t } = useTranslation('contentStudio');
  // Handle selection toggle - simple click to select/deselect
  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    // Prevent any parent elements from handling this event
    e.stopPropagation();
    
    // Toggle selection
    onSelect(image.id);
  };
  
  // Handle double click to view details
  const handleDoubleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    if (onViewDetails) {
      onViewDetails(image.id);
    }
  };
  
  // Handle view details button click
  const handleViewDetails = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation(); // Prevent selection toggle
    if (onViewDetails) {
      onViewDetails(image.id);
    }
  };

  return (
    <div 
      className={clsx(
        "group relative rounded-lg overflow-hidden cursor-pointer",
        isSelected ? "ring-2 ring-[#22D3EE]" : "hover:ring-1 hover:ring-white/20"
      )}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
    >
      {/* Image container - fixed size to prevent movement */}
      <div className="aspect-square overflow-hidden bg-white/5">
        <img 
          src={image.type.startsWith('video/') ? image.thumbnailUrl : image.url}
          alt={image.title}
          className="w-full h-full object-cover"
        />
      </div>
      
      {/* Hover overlay - static position */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
        <div className="absolute bottom-0 left-0 right-0 p-2">
          <div className="text-sm text-white font-medium truncate">
            {image.title}
          </div>
          <div className="text-xs text-white/70">
            {image.dimensions}
          </div>
        </div>
      </div>
      
      {/* View Details Button - appears on hover */}
      <button
        onClick={handleViewDetails}
        className="absolute top-2 right-2 bg-black/40 text-white p-1.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-black/60"
        title={t('viewDetails', 'View Details')}
      >
        <EyeIcon className="w-4 h-4" />
      </button>
      
      {/* Selection indicator - static position in corner */}
      {isSelected && (
        <div className="absolute top-2 left-2 bg-[#22D3EE] text-black rounded-full p-0.5">
          <CheckCircleIcon className="w-5 h-5" />
        </div>
      )}
    </div>
  );
}

export default ImageItem;
