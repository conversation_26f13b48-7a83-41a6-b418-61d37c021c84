import React from 'react';
import { CheckCircleIcon, ChevronRightIcon, EyeIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';
import { formatFileSize } from '../../utils/fileUtils';
import { useTranslation } from 'react-i18next';
import ContentStudioPlaceholder from './ContentStudioPlaceholder';
import { Image } from '../../../../../../types/contentStudio';

interface ListViewProps {
  images: Image[];
  selectedImages: string[];
  onSelectImage: (imageId: string) => void;
  onViewDetails: (imageId: string) => void;
  handleUpload: () => void;
}

const ListView: React.FC<ListViewProps> = ({ images, selectedImages, onSelectImage, onViewDetails, handleUpload }) => {
  const { t } = useTranslation('contentStudio');
  if (images.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <ContentStudioPlaceholder handleUpload={handleUpload} />
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Simple click handler for row selection
  const handleRowClick = (e: React.MouseEvent, imageId: string) => {
    e.stopPropagation();
    // Just toggle selection, nothing else
    onSelectImage(imageId);
  };

  // Separate handler for the details button
  const handleDetailsClick = (e: React.MouseEvent, imageId: string) => {
    e.stopPropagation();
    onViewDetails(imageId);
  };

  return (
    <div className="w-full">
      {/* Header Row */}
      <div className="grid grid-cols-12 gap-4 px-4 py-2 text-xs text-white/50 border-b border-white/5 sticky top-0 bg-[#0A0F1C]">
        <div className="col-span-4">{t('tableHeader.name', 'Name')}</div>
        <div className="col-span-2">{t('tableHeader.size', 'Size')}</div>
        <div className="col-span-2">{t('tableHeader.type', 'Type')}</div>
        <div className="col-span-3">{t('tableHeader.date', 'Date')}</div>
        <div className="col-span-1"></div>
      </div>

      {/* List Items */}
      <div className="divide-y divide-white/5 pb-38">
        {images.map(image => (
          <div
            key={image.id}
            className={clsx(
              "grid grid-cols-12 gap-4 px-4 py-3 cursor-pointer",
              selectedImages.includes(image.id)
                ? "bg-[#22D3EE]/5"
                : "hover:bg-white/5"
            )}
            onClick={(e) => handleRowClick(e, image.id)}
          >
            {/* Name + Preview */}
            <div className="col-span-4 flex items-center gap-3">
              <div className="flex-shrink-0">
                <div className={clsx(
                  "w-10 h-10 rounded overflow-hidden border",
                  selectedImages.includes(image.id)
                    ? "border-[#22D3EE]"
                    : "border-white/10"
                )}>
                      <img src={image.type.startsWith('video/') ? image.thumbnailUrl : image.url} alt={image.title} className="w-full h-full object-cover" />
                </div>
              </div>
              <span className="text-white truncate">{image.title}</span>
            </div>

            {/* Size */}
            <div className="col-span-2 flex items-center text-white/70">
              {formatFileSize(image.size)}
            </div>

            {/* Type */}
            <div className="col-span-2 flex items-center text-white/70">
              {image.type.split('/')[1]?.toUpperCase() || 'UNKNOWN'}
            </div>

            {/* Date */}
            <div className="col-span-3 flex items-center text-white/70">
              {formatDate(image.createdAt)}
            </div>

            {/* Actions */}
            <div className="col-span-1 flex items-center justify-end">
              <button
                onClick={(e) => handleDetailsClick(e, image.id)}
                className="p-1.5 bg-transparent rounded hover:bg-white/10 text-white/70 hover:text-white/80 transition-colors text-white group flex gap-1 items-center"
                title={t('viewDetails', 'View Details')}
              >
                <EyeIcon className="w-4 h-4" />
                <span className="text-xs opacity-0 group-hover:opacity-100 transition-opacity">{t('view', 'View')}</span>
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default ListView;
