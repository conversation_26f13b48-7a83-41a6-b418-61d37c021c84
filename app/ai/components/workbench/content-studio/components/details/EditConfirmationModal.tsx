import React, { RefObject } from 'react';
import { motion } from 'framer-motion';
import { XMarkIcon, PencilIcon } from '@heroicons/react/24/outline';
import { useTranslation } from 'react-i18next';

interface EditConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

const EditConfirmationModal: React.FC<EditConfirmationModalProps> = ({
  isOpen, 
  onClose, 
  onConfirm,
}) => {
  const { t } = useTranslation('contentStudio');
  if (!isOpen) return null;
  
  return (
    <div 
      className="absolute inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
      onClick={onClose}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%'
      }}
    >
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        className="w-full max-w-md bg-[#0F1526] rounded-xl overflow-hidden shadow-xl"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-5">
          <h2 className="text-xl font-light text-[#22D3EE] flex items-center gap-2">
            <PencilIcon className="w-5 h-5" />
            {t('editConfirm.title', 'Save Changes')}
          </h2>
          <button 
            onClick={onClose}
            className="p-1.5 text-white hover:text-white/80 transition-colors"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>
        
        {/* Divider */}
        <div className="h-px bg-[#1A2035]"></div>
        
        {/* Content */}
        <div className="p-6 space-y-5">
          {/* Message */}
          <div className="bg-[#1A2035] border border-[#22D3EE]/30 rounded-lg p-5">
            <div className="flex gap-3">
            <p className="text-white/90">
              {t('editConfirm.message', 'Are you sure you want to save the changes to this item? This will update the metadata in your library.')}
            </p>
            </div>
          </div>
          
          {/* Optional item preview here if needed */}
        </div>
        
        {/* Divider */}
        <div className="h-px bg-[#1A2035]"></div>
        
        {/* Footer */}
        <div className="p-5 flex items-center justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-white/5 text-white rounded-lg hover:bg-white/10 transition-colors"
          >
            {t('cancel', 'Cancel')}
          </button>
          
          <button
            onClick={() => {
              onConfirm();
              onClose();
            }}
            className="px-4 py-2 bg-[#22D3EE] text-black rounded-lg hover:bg-[#22D3EE]/90 transition-colors flex items-center gap-2"
          >
            <PencilIcon className="w-5 h-5" />
            {t('editConfirm.confirm', 'Save Changes')}
          </button>
        </div>
      </motion.div>
    </div>
  );
}

export default EditConfirmationModal;
