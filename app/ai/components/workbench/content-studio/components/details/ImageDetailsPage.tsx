import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  XMarkIcon, 
  PencilIcon, 
  TrashIcon, 
  FolderArrowDownIcon,
  DocumentTextIcon,
  TagIcon,
  LinkIcon,
  ArrowLeftIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import MoveToFolderDialog from '../batch/MoveToFolderDialog';
import EditConfirmationModal from './EditConfirmationModal';
import ConfirmationDialog from '../common/ConfirmationDialog';
import { formatFileSize } from '../../utils/fileUtils';
import { useTranslation } from 'react-i18next';
import { Image, Folder } from '../../../../../../types/contentStudio';

interface ImageDetailsPageProps {
  image: Image;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedImage: Image) => void;
  onDelete: () => void;
  onMove: (imageId: string, targetFolderId: string) => void;
  contentStudioRef: React.RefObject<HTMLDivElement | null>;
  folders: Folder[];
}

const ImageDetailsPage: React.FC<ImageDetailsPageProps> = ({ image, isOpen, onClose, onSave, onDelete, onMove, contentStudioRef, folders }) => {
  const { t } = useTranslation('contentStudio');
  const [editMode, setEditMode] = useState<boolean>(false);
  const [title, setTitle] = useState<string>(image?.title || '');
  const [tags, setTags] = useState<string[]>(image?.tags || []);
  const [tagInput, setTagInput] = useState<string>('');
  const [isCopied, setIsCopied] = useState<boolean>(false);
  const [isMoveDialogOpen, setIsMoveDialogOpen] = useState<boolean>(false);
  const [isEditConfirmOpen, setIsEditConfirmOpen] = useState<boolean>(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const titleRef = useRef<HTMLHeadingElement>(null);
  const animationRef = useRef<number | null>(null);
  
  if (!isOpen || !image) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  // Copy URL function
  const handleCopyUrl = () => {
    try {
      const tempInput = document.createElement('input');
      tempInput.value = image.url;
      document.body.appendChild(tempInput);
      tempInput.select();
      document.execCommand('copy');
      document.body.removeChild(tempInput);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy URL', err);
    }
  };
  
  const handleEdit = () => {
    setEditMode(true);
  };
  
  const handleSaveClick = () => {
    // Validate file name
    if(!title.trim()){
      setError('errorFileMustHaveName');
      return;
    }

    setIsEditConfirmOpen(true);
  };
  
  const handleConfirmSave = () => {
    // Add input as a tag

    const updatedTags = [
      ...(tags || []), 
      ...(tagInput ? [tagInput] : [])
    ]
    
    onSave({ 
      ...image, 
      title, 
      tags: updatedTags 
    });
    setError('');
    setEditMode(false);
    setIsEditConfirmOpen(false);
  };
  
  const handleMoveButtonClick = () => {
    setIsMoveDialogOpen(true);
  };
  
  const handleMoveConfirm = (targetFolderId: string) => {
    if (onMove && targetFolderId) {
      onMove(image.id, targetFolderId);
    }
    setIsMoveDialogOpen(false);
    setError('');
    onClose();
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && tagInput.trim()) {
      e.preventDefault();
      handleAddTag();
    }
  };
  
  const handleDeleteClick = () => {
    setIsDeleteConfirmOpen(true);
  };
  
  const handleConfirmDelete = () => {
    onDelete();
    setIsDeleteConfirmOpen(false);
  };

  const animateScroll = (element: HTMLElement, to: number, duration = 1200) => {
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
  
    const start = element.scrollLeft;
    const change = to - start;
    const startTime = performance.now();
  
    function animate(time: number) {
      const elapsed = time - startTime;
      const progress = Math.min(elapsed / duration, 1);
      element.scrollLeft = start + change * progress;
  
      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      } else {
        animationRef.current = null;
      }
    }
    animationRef.current = requestAnimationFrame(animate);
  };
  
  const handleTitleMouseEnter = () => {
    const el = titleRef.current;
    if (el && el.scrollWidth > el.clientWidth) {
      animateScroll(el, el.scrollWidth, 4000);
    }
  };
  
  const handleTitleMouseLeave = () => {
    const el = titleRef.current;
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
      animationRef.current = null;
    }
  
    if (el) {
      animateScroll(el, 0, 800);
    }
  };
  

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="absolute inset-0 bg-[#0A0F1C] z-50 flex flex-col"
    >
      {/* Header Bar */}
      <div className="flex items-center justify-between px-4 py-2 border-b border-white/10 bg-[#1A1F2E] h-[61px]">
        <div className="flex items-center gap-3">
          <button
            onClick={() => {
              setError('');
              onClose();
            }}
            className="p-1.5 bg-transparent hover:bg-white/10 rounded-lg "
          >
            <ArrowLeftIcon className="w-5 h-5 text-white/70" />
          </button>
          <h2 className="text-lg font-medium text-white">{image.type.startsWith('video/') ? t('imageDetails.videoTitle', 'Video Details') : t('imageDetails.title', 'Image Details')}</h2>
        </div>
        
        <div className="flex items-center gap-2">
          {!editMode && (
            <button
              onClick={handleEdit}
              className="flex items-center gap-2 px-4 py-2 bg-white/5 hover:bg-white/10 rounded-lg transition-colors text-white/80"
            >
              <PencilIcon className="w-4 h-4" />
              <span className="font-medium">{t('edit', 'Edit')}</span>

            </button>
          )}
          {editMode && (
            <>
              <button
                onClick={() => {
                  setError('');
                  setEditMode(false);
                }}
                className="flex items-center gap-2 px-3 py-1.5 bg-white/5 hover:bg-white/10 rounded-lg transition-colors text-white/80"
              >
                <XMarkIcon className="w-4 h-4" />
                <span>{t('cancel', 'Cancel')}</span>
              </button>
              <button
                onClick={handleSaveClick}
                className="flex items-center gap-2 px-3 py-1.5 bg-[#4ADE80] text-black rounded-lg hover:bg-[#4ADE80]/90 transition-colors"
              >
                <CheckIcon className="w-4 h-4" />
                <span>{t('save', 'Save')}</span>
              </button>
            </>
          )}
        </div>
      </div>
      
      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left side - Image Preview */}
        <div className="w-3/5 border-r border-white/10 flex items-center justify-center p-5 bg-[#0A0F1C]">
          <div className="relative max-h-full max-w-full overflow-hidden rounded-lg border border-white/10">
            {image.type.startsWith('video/') ? (
              <video 
                src={image.url} 
                className="max-h-[65vh] max-w-full object-contain"  
                autoPlay
                loop
                muted
                controls
                playsInline
                preload="metadata"
              />
            ) : (
              <img src={image.url} alt={image.title} className="max-h-[65vh] max-w-full object-contain" />
            )}
          </div>
        </div>
        
        {/* Right side - Details */}
        <div className="w-2/5 bg-[#1A1F2E] flex flex-col">
          {editMode ? (
            <div className="h-full flex flex-col">
              <div className="flex-1 p-4 overflow-y-auto">
                <div className="mb-3">
                  <h3 className="text-white/70 text-sm mb-1.5">{t('fileTitle', 'Title')}</h3>
                  <input
                    type="text"
                    value={title}
                    onChange={(e) => {
                      setTitle(e.target.value);
                      setError(''); // Clear error when typing
                    }}
                    className={`w-full bg-white/5 border rounded-lg px-3 py-1.5 text-white focus:outline-none focus:border-[#22D3EE]/30  ${
                      error ? 'border-red-500' : 'border-white/10'
                    } `}
                  />
                    {error && <p className="text-red-500 text-xs mt-1">{t(error, 'Error')}</p>}
                </div>
                
                <div className="mb-3">
                  <h3 className="text-white/70 text-sm mb-1.5">{t('tags', 'Tags')}</h3>
                  <div className="flex flex-wrap gap-1.5 mb-3">
                    {tags.map(tag => (
                      <div 
                        key={tag}
                        className="flex items-center gap-1 px-2 py-1 bg-[#22D3EE]/10 text-[#22D3EE] rounded-full text-xs"
                      >
                        <span>{tag}</span>
                        <button
                          onClick={() => setTags(tags.filter(t => t !== tag))}
                          className="bg-transparent hover:bg-white/10 rounded-full"
                        >
                          <XMarkIcon className="w-3 h-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <input
                      type="text"
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder={t('addTagPlaceholder', 'Add a tag...')}
                      className="flex-1 bg-white/5 border border-white/10 rounded-lg px-3 py-1.5 text-white text-sm focus:outline-none focus:border-[#22D3EE]/30"
                    />
                    <button
                      onClick={handleAddTag}
                      className="px-3 py-1.5 bg-[#22D3EE]/10 text-[#22D3EE] rounded-lg hover:bg-[#22D3EE]/20 transition-colors text-sm"
                    >
                      {t('add', 'Add')}
                    </button>
                  </div>
                </div>
                
                {/* Move button in edit mode */}
                <button
                  onClick={handleMoveButtonClick}
                  className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-white/5 hover:bg-white/10 transition-colors rounded-lg text-white mt-3"
                >
                  <FolderArrowDownIcon className="w-4 h-4" />
                  <span>{t('moveToFolder', 'Move to Folder')}</span>
                </button>
              </div>
            </div>
          ) : (
            <div className="h-full flex flex-col">
              <div className="p-4 border-b border-white/5">
                <h2
                  ref={titleRef}
                  onMouseEnter={handleTitleMouseEnter}
                  onMouseLeave={handleTitleMouseLeave}
                  className="text-lg font-medium text-white mb-1 overflow-x-auto whitespace-nowrap no-scrollbar"
                  style={{ display: 'block', maxWidth: '100%' }}
                >
                  {image.title}
                </h2>


                <div className="flex items-center text-white/50 text-xs">
                  <span className="mr-1">{t('uploaded', 'Uploaded:')}</span>
                  <span>{formatDate(image.createdAt)}</span>
                </div>
              </div>
              
              {/* Details content in a scrollable container */}
              <div className="flex-1 p-4 space-y-4 overflow-y-auto">
                {/* File Information */}
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <DocumentTextIcon className="w-4 h-4 text-white/70" />
                    <h3 className="text-white text-sm font-medium">{t('fileInfo', 'File Information')}</h3>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <div className="bg-white/5 p-2 rounded-lg">
                      <div className="text-white/50 text-xs mb-0.5">{t('type', 'Type')}</div>
                      <div className="text-white text-sm">{image.type.split('/')[1]?.toUpperCase() || 'PNG'}</div>
                    </div>
                    
                    <div className="bg-white/5 p-2 rounded-lg">
                      <div className="text-white/50 text-xs mb-0.5">{t('size', 'Size')}</div>
                      <div className="text-white text-sm">{formatFileSize(image.size)}</div>
                    </div>
                    
                    <div className="bg-white/5 p-2 rounded-lg">
                      <div className="text-white/50 text-xs mb-0.5">{t('dimensions', 'Dimensions')}</div>
                      <div className="text-white text-sm">{image.dimensions}</div>
                    </div>
                    
                    <div className="bg-white/5 p-2 rounded-lg">
                      <div className="text-white/50 text-xs mb-0.5">{t('location', 'Location')}</div>
                      <div className="text-white text-sm">{folders.find(folder => folder.id === image.folderId)?.name || 'All Files'}</div>
                    </div>
                  </div>
                </div>
                
                {/* Tags */}
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <TagIcon className="w-4 h-4 text-white/70" />
                    <h3 className="text-white text-sm font-medium">{t('tags', 'Tags')}</h3>
                  </div>
                  
                  <div className="p-2 rounded-lg">
                    <div className="flex flex-wrap gap-1.5">
                      {image.tags && image.tags.length > 0 ? (
                        image.tags.map(tag => (
                          <span 
                            key={tag} 
                            className="px-2 py-1 bg-[#22D3EE]/10 text-[#22D3EE] rounded-full text-xs"
                          >
                            {tag}
                          </span>
                        ))
                      ) : (
                        <span className="text-white/50 text-xs">{t('noTags', 'No tags')}</span>
                      )}
                    </div>
                  </div>
                </div>
                
                {/* URL */}
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <LinkIcon className="w-4 h-4 text-white/70" />
                    <h3 className="text-white text-sm font-medium">{image.type.startsWith('video/') ? t('videoUrl', 'Video URL') : t('imageUrl', 'Image URL')}</h3>
                  </div>
                  
                  <div className="p-2 rounded-lg">
                    <div className="flex items-center gap-2">
                      <div className="flex-1 bg-[#161B2A] border border-white/10 rounded-lg px-2 py-1.5 text-white/70 text-xs overflow-hidden whitespace-nowrap text-ellipsis">
                        {image.url}
                      </div>
                      <button
                        onClick={handleCopyUrl}
                        className="p-1.5 bg-[#161B2A] border border-white/10 rounded-lg hover:bg-white/10 transition-colors"
                      >
                        <svg className="w-4 h-4 text-white/70" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                        </svg>
                      </button>
                    </div>
                    {isCopied && (
                      <div className="text-[#4ADE80] text-xs mt-1">{t('urlCopied', 'URL copied!')}</div>
                    )}
                  </div>
                </div>
              </div>
              
              {/* Action buttons at the bottom */}
              <div className="p-4 border-t border-white/5">
                <div className="flex items-center gap-3">
                  <button
                    onClick={handleMoveButtonClick}
                    className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-white/5 hover:bg-white/10 transition-colors rounded-lg text-white"
                  >
                    <FolderArrowDownIcon className="w-4 h-4" />
                    <span>{t('move', 'Move')}</span>
                  </button>
                  
                  <button
                    onClick={handleDeleteClick}
                    className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-red-500/10 hover:bg-red-500/20 transition-colors rounded-lg text-red-400"
                  >
                    <TrashIcon className="w-4 h-4" />
                    <span>{t('delete', 'Delete')}</span>
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Move to Folder Dialog */}
      <MoveToFolderDialog
        isOpen={isMoveDialogOpen}
        onClose={() => setIsMoveDialogOpen(false)}
        onMove={handleMoveConfirm}
        selectedCount={1}
        currentFolder={image.folderId || 'all'}
        contentStudioRef={contentStudioRef}
        folders={folders}
      />
      
      {/* Edit Confirmation Modal */}
      <EditConfirmationModal
        isOpen={isEditConfirmOpen}
        onClose={() => setIsEditConfirmOpen(false)}
        onConfirm={handleConfirmSave}
      />
      
      {/* Delete Confirmation Modal */}
      <ConfirmationDialog
        isOpen={isDeleteConfirmOpen}
        onClose={() => setIsDeleteConfirmOpen(false)}
        title={t('deleteImageTitle', 'Delete Image')}
        message={t('deleteImageMessage', 'Are you sure you want to delete this image? This action cannot be undone.')}
        confirmLabel={t('deleteImageConfirm', 'Delete Image')}
        onConfirm={handleConfirmDelete}
        confirmVariant="danger"
        icon="warning"
        itemName={image.title}
        itemImageUrl={image.url}
        contentStudioRef={contentStudioRef}
      />
    </motion.div>
  );
}

export default ImageDetailsPage;
