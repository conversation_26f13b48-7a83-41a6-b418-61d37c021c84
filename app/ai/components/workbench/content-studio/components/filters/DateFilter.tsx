import React from 'react';
import { useTranslation } from 'react-i18next';

interface DateFilterProps {
  dateFrom: string;
  dateTo: string;
  onDateFromChange: (value: string) => void;
  onDateToChange: (value: string) => void;
}

const DateFilter: React.FC<DateFilterProps> = ({ dateFrom, dateTo, onDateFromChange, onDateToChange }) => {
  const { t } = useTranslation('contentStudio');
  return (
    <div>
      <h3 className="text-white font-medium mb-3">{t('dateRange', 'Date Range')}</h3>
      
      <div className="space-y-3">
        <div>
          <label className="block text-white/70 text-sm mb-2">{t('dateFrom', 'From')}</label>
          <input
            type="date"
            value={dateFrom}
            onChange={(e) => onDateFromChange(e.target.value)}
            className="w-full bg-white/5 border border-white/10 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:border-[#22D3EE]/30"
          />
        </div>
        
        <div>
          <label className="block text-white/70 text-sm mb-2">{t('dateTo', 'To')}</label>
          <input
            type="date"
            value={dateTo}
            onChange={(e) => onDateToChange(e.target.value)}
            min={dateFrom} // Ensure "to" date isn't before "from" date
            className="w-full bg-white/5 border border-white/10 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:border-[#22D3EE]/30"
          />
        </div>
      </div>
    </div>
  );
}

export default DateFilter;
