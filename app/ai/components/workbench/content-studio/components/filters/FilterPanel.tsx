import React, { useState, useRef, useEffect } from 'react';
import { ChevronLeftIcon } from '@heroicons/react/24/outline';
import DateFilter from './DateFilter';
import TagFilter from './TagFilter';
import { useTranslation } from 'react-i18next';
import { Tag, Filters } from '../../../../../../types/contentStudio';

interface FilterPanelProps {
  isOpen: boolean;
  onClose: () => void;
  imagesLen: number;
  filters: Filters;
  onUpdateFilters: (updated: Filters) => void;
  getTags: (searchTerm?: string) => Promise<void>;
  allTags: Tag[];
}

const FilterPanel: React.FC<FilterPanelProps> = ({ 
  isOpen, 
  onClose, 
  imagesLen,
  filters, 
  onUpdateFilters,
  getTags,
  allTags
}) => {
  const { t } = useTranslation('contentStudio');
  const [dateFrom, setDateFrom] = useState<string>(filters.dateFrom || '');
  const [dateTo, setDateTo] = useState<string>(filters.dateTo || '');
  const [selectedTags, setSelectedTags] = useState<string[]>(filters.tags || []);
  const panelRef = useRef<HTMLDivElement>(null);
  
  // Handle clicks outside the filter panel
  useEffect(() => {
    if (!isOpen) return;
    
    function handleClickOutside(event: MouseEvent) {
      const target = event.target as HTMLElement | null;
      // Check if panel is open and if the click is outside the panel and not on the toggle button
      if (
        panelRef.current && 
        target &&
        !panelRef.current.contains(target) &&
        !target.closest('.filter-toggle-button')
      ) {
        onClose();
      }
    }
    
    // Add event listener
    document.addEventListener('mousedown', handleClickOutside);
    
    // Clean up
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);
  
  // Add class name for additional selector
  useEffect(() => {
    if (panelRef.current) {
      panelRef.current.classList.add("filter-panel");
    }
    getTags();
  }, []);
  
  const handleToggleTag = (tag: string) => {
    setSelectedTags(prev => {
      if (prev.includes(tag)) {
        return prev.filter(t => t !== tag);
      } else {
        return [...prev, tag];
      }
    });
  };
  
  const handleClearFilters = () => {
    setDateFrom('');
    setDateTo('');
    setSelectedTags([]);
    onUpdateFilters({
      dateFrom: '',
      dateTo: '',
      tags: [],
      searchTerm: filters.searchTerm
    });
  };
  
  const handleApplyFilters = () => {
    onUpdateFilters({
      dateFrom,
      dateTo,
      tags: selectedTags,
      searchTerm: filters.searchTerm
    });
  };
  
  return (
    <div 
      ref={panelRef}
      className={`absolute top-0 bottom-0 right-0 w-80 bg-[#1A1F2E] border-l border-white/10 shadow-xl transform transition-transform duration-300 z-50 flex flex-col ${
        isOpen ? 'translate-x-0' : 'translate-x-full'
      } ${
        imagesLen > 8 ? 'pb-38' : ''
      }
      `}
    >
      <div className="flex items-center justify-between p-4 border-b border-white/10">
        <h2 className="text-xl font-light text-white">{t('filters', 'Filters')}</h2>
        <button 
          onClick={onClose}
          className="p-2 rounded-full hover:bg-white/10 transition-colors filter-toggle-button"
        >
          <ChevronLeftIcon className="w-5 h-5 text-white/70" />
        </button>
      </div>
      
      {/* Filter Content - Scrollable area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Date Filter */}
        <div>
          <DateFilter 
            dateFrom={dateFrom}
            dateTo={dateTo}
            onDateFromChange={setDateFrom}
            onDateToChange={setDateTo}
          />
        </div>
        
        {/* Tag Filter - takes all available space */}
        <div>
          <h3 className="text-white font-medium mb-3">{t('tags', 'Tags')}</h3>
          <div className="max-h-[400px] overflow-y-auto pr-1">
            <TagFilter 
              getTags={getTags}
              allTags={allTags}
              selectedTags={selectedTags}
              onToggleTag={handleToggleTag}
            />
          </div>
        </div>
      </div>
      
      {/* Footer Actions - Fixed at bottom */}
      <div className={`${imagesLen > 8 ? 'px-4 pt-4' : 'p-4'} border-t border-white/10`}>
        <div className="space-y-3">
          <button
            onClick={handleClearFilters}
            className="w-full py-2 bg-white/5 text-white/80 rounded-lg hover:bg-white/10 transition-colors"
          >
             {t('clearFilters', 'Clear Filters')}
          </button>
          <button
            onClick={handleApplyFilters}
            className="w-full py-2 bg-[#22D3EE] text-black rounded-lg hover:bg-[#22D3EE]/90 transition-colors"
          >
            {t('applyFilters', 'Apply Filters')}
          </button>
        </div>
      </div>
    </div>
  );
}

export default FilterPanel;
