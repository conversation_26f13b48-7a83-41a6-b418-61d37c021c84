import React, { useEffect, useState } from 'react';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { useTranslation } from 'react-i18next';
import { Tag } from '../../../../../../types/contentStudio';

interface TagFilterProps {
  getTags: (searchTerm?: string) => void;
  allTags: Tag[];
  selectedTags: string[];
  onToggleTag: (tagId: string) => void;
}

const TagFilter: React.FC<TagFilterProps> = ({ getTags, allTags, selectedTags, onToggleTag }) => {
  const { t } = useTranslation('contentStudio');
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedTerm, setDebouncedTerm] = useState(searchTerm);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setDebouncedTerm(searchTerm);
    }, 200)

    return () => { clearTimeout(timeout) };
  }, [searchTerm]);

  useEffect(() => {
    getTags(debouncedTerm);
  }, [debouncedTerm]);
  
  return (
    <div className="h-full">
      <div className="relative mb-3">
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder={t('searchTagsPlaceholder', 'Search tags...')}
          className="w-full bg-white/5 text-white/80 rounded-lg pl-9 pr-3 py-2 text-sm border border-white/10 focus:outline-none focus:border-[#22D3EE]/30 placeholder-white/30"
        />
        <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/30" />
      </div>
      
      <div className="flex flex-wrap gap-2">
        {allTags?.length > 0 ? (
          allTags.map(tag => (
            <button
              key={tag.id}
              onClick={() => onToggleTag(tag.name)}
              className={`px-3 py-1.5 rounded-lg text-xs transition-colors ${
                selectedTags.includes(tag.name)
                  ? 'bg-[#22D3EE]/20 text-[#22D3EE] hover:bg-[#22D3EE]/30'
                  : 'bg-white/5 text-white/70 hover:bg-white/10'
              }`}
            >
              {tag.name}
            </button>
          ))
        ) : (
          <div className="w-full text-center text-white/40 text-sm py-2">
            {searchTerm ? t('noTagsMatchSearch', 'No tags match your search') : t('noTagsAvailable', 'No tags available')}
          </div>
        )}
      </div>
    </div>
  );
}

export default TagFilter;
