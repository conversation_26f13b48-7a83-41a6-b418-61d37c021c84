import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { XMarkIcon, FolderPlusIcon, FolderIcon, ChevronRightIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import { useTranslation } from 'react-i18next';
import { Folder } from '../../../../../../types/contentStudio'

interface FolderCreationModalProps {
  folders: Folder[];
  isOpen: boolean;
  onClose: () => void;
  onCreateFolder: (folderData: { name: string; parentId: string | null }) => Promise<void>;
  parentFolderId?: string | null;
  contentStudioRef?: React.RefObject<HTMLDivElement | null>;
}

const FolderCreationModal: React.FC<FolderCreationModalProps> = ({ folders, isOpen, onClose, onCreateFolder, parentFolderId = null, contentStudioRef }) => {
  const { t } = useTranslation('contentStudio');
  const [folderName, setFolderName] = useState('');
  const [selectedFolderId, setSelectedFolderId] = useState(parentFolderId || null);
  const [error, setError] = useState('');
  const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>({});
  
  // Update selected parent when parentFolderId prop changes
  useEffect(() => {
    setSelectedFolderId(parentFolderId);
  }, [parentFolderId]);
  
  // Filter out special folders, current folder, and organize by hierarchy
  const availableFolders = folders?.filter(folder => 
    !folder.isRoot
  );
  
  const handleToggleExpand = (folderId: string) => {
    setExpandedFolders(prev => ({
      ...prev,
      [folderId]: !prev[folderId]
    }));
  };
  
  const renderFolderTree = (parentId: string | null = null, level = 0): React.ReactNode => {
    const folderItems = availableFolders.filter(folder => folder.parentId === parentId);
    
    if (folderItems.length === 0) return null;
    
    return (
      <div className="ml-4">
        {folderItems.map(folder => {
          const hasChildren = availableFolders.some(f => f.parentId === folder.id);
          const isExpanded = expandedFolders[folder.id];
          
          return (
            <div key={folder.id}>
              <div 
                className={`flex items-center gap-2 px-3 py-2 rounded-lg text-left my-1 ${
                  selectedFolderId === folder.id
                    ? 'bg-[#22D3EE]/10 text-[#22D3EE]'
                    : 'text-white/80 hover:bg-white/5'
                } transition-colors cursor-pointer`}
                style={{ paddingLeft: `${(level * 8) + 12}px` }}
                onClick={() => setSelectedFolderId(folder.id)}
              >
                {hasChildren && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleToggleExpand(folder.id);
                    }}
                    className="p-0.5 rounded bg-transparent hover:bg-white/10"
                    type="button"
                  >
                    {isExpanded ? (
                      <ChevronDownIcon className="w-3.5 h-3.5 text-white/50" />
                    ) : (
                      <ChevronRightIcon className="w-3.5 h-3.5 text-white/50" />
                    )}
                  </button>
                )}
                {!hasChildren && <div className="w-4"></div>}
                <FolderIcon className="w-5 h-5" />
                <span>{folder.name}</span>
              </div>
              
              {hasChildren && isExpanded && renderFolderTree(folder.id, level + 1)}
            </div>
          );
        })}
      </div>
    );
  };
  
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    // Validate folder name
    if (!folderName.trim()) {
      setError('errorFolderNameRequired');
      return;
    }
    
    // Check if folder name already exists at this level
    const existingFolder = folders.find(f => 
      f.name.toLowerCase() === folderName.trim().toLowerCase() && 
      f.parentId === selectedFolderId
    );
    
    if (existingFolder) {
      setError('errorFolderAlreadyExists');
      return;
    }
    
    // Create folder
    onCreateFolder({
      name: folderName.trim(),
      parentId: selectedFolderId
    });
    
    // Reset and close
    setFolderName('');
    setError('');
    onClose();
  };
  
  if (!isOpen) return null;
  
  return (
    <div 
      className="absolute inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
      onClick={() => {
        setError('');
        onClose();
      }}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%'
      }}
    >
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        className="w-full max-w-md bg-[#1A1F2E] rounded-xl border border-white/10 overflow-hidden shadow-xl"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between p-4 border-b border-white/10">
          <h2 className="text-xl font-light text-white flex items-center gap-2">
            <FolderPlusIcon className="w-5 h-5 text-[#22D3EE]" />
            {t('createNewFolder', 'Create New Folder')}
          </h2>
          <button 
             onClick={() => {
              setError('');
              onClose();
            }}
            className="p-2 rounded-full bg-transparent hover:bg-white/10 transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-white/70" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6">
          <div className="mb-4">
            <label className="block text-white/70 text-sm mb-2">{t('folderName', 'Folder Name')}</label>
            <input
              type="text"
              value={folderName}
              onChange={(e) => {
                if(e.target.value.length <= 20){
                  setFolderName(e.target.value);
                }
                setError(''); // Clear error when typing
              }}
              className={`w-full bg-white/5 border ${
                error ? 'border-red-500' : 'border-white/10'
              } rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#22D3EE]/30`}
              placeholder={t('placeholderFolderName', 'Enter folder name')}
              autoFocus
            />
            {error && <p className="text-red-500 text-xs mt-1">{t(error, 'Error')}</p>}
          </div>
          
          <div className="mb-6">
            <label className="block text-white/70 text-sm mb-2">{t('parentFolder', 'Parent Folder')}</label>
            
            <div className="bg-white/5 rounded-lg border border-white/10 max-h-60 overflow-y-auto p-2">
              {/* Root folder option */}
              <div 
                className={`flex items-center gap-2 px-3 py-2 rounded-lg text-left ${
                  selectedFolderId === null
                    ? 'bg-[#22D3EE]/10 text-[#22D3EE]'
                    : 'text-white/80 hover:bg-white/5'
                } transition-colors cursor-pointer mb-1`}
                onClick={() => setSelectedFolderId(null)}
              >
                <FolderIcon className="w-5 h-5" />
                <span>{t('rootAllFiles', 'Root (All Files)')}</span>
              </div>
              
              {/* Folder Tree */}
              {renderFolderTree(null, 0)}
            </div>
          </div>
          
          <div className="flex items-center justify-end gap-3">
            <button
              type="button"
              onClick={() => {
                setError('');
                onClose();
              }}
              className="px-4 py-2 bg-white/5 text-white rounded-lg hover:bg-white/10 transition-colors"
            >
              {t('cancel', 'Cancel')}
            </button>
            <button
              type="submit"
              className="px-4 py-2 rounded-lg transition-colors bg-[#22D3EE] text-black hover:bg-[#22D3EE]/90"
            >
              {t('createFolder', 'Create Folder')}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
}

export default FolderCreationModal;
