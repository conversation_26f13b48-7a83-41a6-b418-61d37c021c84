import React, { useState } from 'react';
import { ChevronDownIcon, FolderIcon, PencilIcon, TrashIcon, FolderPlusIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';
import { useTranslation } from 'react-i18next';
import { Folder } from '../../../../../../types/contentStudio';

interface FolderItemProps {
  folder: Folder;
  level?: number;
  isActive: boolean;
  isExpanded: boolean;
  hasChildren: boolean;
  onToggleExpand: (folderId: string) => void;
  onSelectFolder: (folderId: string) => void;
  onCreateFolder: (parentId: string | null) => void;
  onRenameFolder: (folder: Folder) => void;
  onDeleteFolder: (folder: Folder) => void;
}

const FolderItem: React.FC<FolderItemProps> = ({ folder, level = 0, isActive, isExpanded, onToggleExpand, onSelectFolder, has<PERSON><PERSON>dren, onCreate<PERSON>older, onRenameFolder, onDeleteFolder }) => {
  const { t } = useTranslation('contentStudio');
  const [isHovering, setIsHovering] = useState(false);
  
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelectFolder(folder.id);
  };

  const handleToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleExpand(folder.id);
  };

  // Check if this is a special folder that shouldn't be modified
  const isSpecialFolder = folder.isRoot || ['all', 'recent', 'favorites'].includes(folder.id);

  return (
    <div 
      className="select-none group"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <div 
        className={clsx(
          "flex items-center gap-1.5 px-2 py-1.5 rounded cursor-pointer relative min-h-[34px]",
          isActive ? "bg-[#22D3EE]/10 text-[#22D3EE]" : "text-white/70 hover:bg-white/5"
        )}
        style={{ paddingLeft: `${(level * 12) + 8}px` }}
        onClick={handleClick}
      >
        {hasChildren ? (
          <button 
            onClick={handleToggle}
            className="p-0.5 rounded hover:bg-white/10"
          >
            <ChevronDownIcon 
              className={clsx(
                "w-3.5 h-3.5 transition-transform",
                !isExpanded && "-rotate-90"
              )}
            />
          </button>
        ) : (
          <div className="w-4"></div>
        )}
        <FolderIcon className={clsx(
          "w-4 h-4",
          isActive ? "text-[#22D3EE]" : "text-white/70"
        )} />
        <span className="flex-1 text-sm truncate">{folder.name}</span>
        
        {/* Action buttons that appear on hover */}
        {isHovering && (
          <div className="flex items-center gap-1">
            {/* Add subfolder button */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                onCreateFolder(folder.id);
              }}
              className="p-1 text-white/50 hover:text-white hover:bg-white/10 rounded transition-colors"
              title={t('createSubfolder', 'Create Subfolder')}
            >
              <FolderPlusIcon className="w-3.5 h-3.5" />
            </button>
            
            {/* Edit button - only for non-special folders */}
            {!isSpecialFolder && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onRenameFolder(folder);
                }}
                className="p-1 text-white/50 hover:text-white hover:bg-white/10 rounded transition-colors"
                title={t('renameFolder', 'Rename Folder')}
              >
                <PencilIcon className="w-3.5 h-3.5" />
              </button>
            )}
            
            {/* Delete button - only for non-special folders */}
            {!isSpecialFolder && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteFolder(folder);
                }}
                className="p-1 text-white/50 hover:text-red-400 hover:bg-red-500/10 rounded transition-colors"
                title={t('deleteFolder', 'Delete Folder')}
              >
                <TrashIcon className="w-3.5 h-3.5" />
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default FolderItem;
