import React from 'react';
import FolderItem from './FolderItem';
import { Folder } from '../../../../../../types/contentStudio';

interface FolderListProps {
  folders: Folder[];
  selectedFolderId: string;
  expandedFolders: Record<string, boolean>;
  onToggleExpand: (folderId: string) => void;
  onSelectFolder: (folderId: string) => void;
  onCreateFolder: (parentId: string | null) => void;
  onRenameFolder: (folder: Folder) => void;
  onDeleteFolder: (folder: Folder) => void;
  level?: number;
  parentId?: string | null;
}

const FolderList: React.FC<FolderListProps> = ({ 
  folders, 
  selectedFolderId, 
  expandedFolders, 
  onToggleExpand, 
  onSelectFolder, 
  onCreateFolder,
  onRenameFolder,
  onDeleteFolder,
  level = 0, 
  parentId = null 
}) => {
  // Filter the folders for the current level
  const currentLevelFolders = folders?.filter(folder => 
    !folder.isRoot && folder.parentId === parentId
  ) || [];
  
  if (currentLevelFolders.length === 0) {
    return null;
  }
  
  return (
    <div className="space-y-1">
      {currentLevelFolders.map(folder => {
        // Check if this folder has children
        const hasChildren = folders.some(f => f.parentId === folder.id);
        const isExpanded = expandedFolders[folder.id];

        return (
          <div key={folder.id} className="relative group">
            <FolderItem
              folder={folder}
              level={level}
              isActive={selectedFolderId === folder.id}
              isExpanded={isExpanded}
              onToggleExpand={onToggleExpand}
              onSelectFolder={onSelectFolder}
              hasChildren={hasChildren}
              onCreateFolder={onCreateFolder}
              onRenameFolder={onRenameFolder}
              onDeleteFolder={onDeleteFolder}
            />
            
            {/* Render children if folder is expanded and has children */}
            {hasChildren && isExpanded && (
              <FolderList
                folders={folders}
                selectedFolderId={selectedFolderId}
                expandedFolders={expandedFolders}
                onToggleExpand={onToggleExpand}
                onSelectFolder={onSelectFolder}
                onCreateFolder={onCreateFolder}
                onRenameFolder={onRenameFolder}
                onDeleteFolder={onDeleteFolder}
                level={level + 1}
                parentId={folder.id}
              />
            )}
          </div>
        );
      })}
    </div>
  );
}

export default FolderList;
