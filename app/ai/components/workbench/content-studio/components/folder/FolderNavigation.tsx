import React, { useState, useEffect, useRef } from 'react';
import { FolderPlusIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { useTranslation } from 'react-i18next';
import { Folder } from '../../../../../../types/contentStudio';
import FolderList from './FolderList';

interface FolderNavigationProps {
  folders: Folder[];
  getFolders: (searchTerm?: string) => Promise<void>;
  selectedFolderId: string;
  onSelectFolder: (folderId: string) => void;
  onCreateFolder: (parentId: string | null) => void;
  onRenameFolder: (folder: Folder) => void;
  onDeleteFolder: (folder: Folder) => void;
}


const FolderNavigation: React.FC<FolderNavigationProps> = ({ folders, getFolders, selectedFolderId, onSelectFolder, onCreateFolder, onRenameFolder, onDeleteFolder }) => {
  const { t } = useTranslation('contentStudio');
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedTerm, setDebouncedTerm] = useState(searchTerm);
  const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>({});
  const scrollRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setDebouncedTerm(searchTerm);
    }, 200);

    return () => {clearTimeout(timeout)};
  }, [searchTerm]);

  useEffect(() => {
    getFolders(debouncedTerm);
  }, [debouncedTerm]);

  // Function to handle folder expansion toggling
  const handleToggleExpand = (folderId: string) => {
    setExpandedFolders(prev => ({
      ...prev,
      [folderId]: !prev[folderId]
    }));
  };


  // Function to create a new folder
  const handleCreateFolder = () => {
    // Call parent component's function to open folder creation modal
    onCreateFolder(null);
  };

    // Function to handle renaming a folder
    const handleRenameFolder = (folder: Folder) => {
      if (onRenameFolder) {
        onRenameFolder(folder);
      }
    };
    
    // Function to handle deleting a folder
    const handleDeleteFolder = (folder: Folder) => {
      if (onDeleteFolder) {
        onDeleteFolder(folder);
      }
    };

  return (
    <div className="h-full flex flex-col">
      <div className="p-3">
        <div className="relative">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder={t('folderSearchPlaceholder', 'Search folders...')}
            className="w-full bg-white/5 text-white/80 rounded-lg pl-9 pr-3 py-2 text-sm border border-white/10 focus:outline-none focus:border-[#22D3EE]/30 placeholder-white/30"
          />
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/30" />
        </div>
      </div>
      
      <div className="flex items-center justify-between px-3 py-2">
        <h3 className="text-white/60 text-xs uppercase tracking-wider">{t('foldersLabel', 'Folders')}</h3>
        <button 
          onClick={handleCreateFolder}
          className="p-1 bg-transparent hover:bg-white/10 rounded text-white/50 hover:text-white/80 transition-colors"
        >
          <FolderPlusIcon className="w-4 h-4" />
        </button>
      </div>
      
      <div className="flex-1 overflow-y-auto px-2 py-1" ref={scrollRef}>
        {searchTerm ? (
          <div className="mb-3">
            <div className="text-xs uppercase text-white/40 mb-2 px-2">{t('searchResults', 'Search Results')}</div>
            {folders.length > 0 ? (
              folders.map(folder => (
                <div 
                  key={folder.id}
                  className={`px-3 py-1.5 hover:bg-white/5 rounded cursor-pointer text-sm ${
                    selectedFolderId === folder.id ? 'bg-[#22D3EE]/10 text-[#22D3EE]' : 'text-white/70'
                  }`}
                  onClick={() => onSelectFolder(folder.id)}
                >
                  {folder.name}
                </div>
              ))
            ) : (
              <div className="text-white/40 text-sm px-2">{t('noFoldersFound', 'No folders found')}</div>
            )}
          </div>
        ) : (
          <>
            {/* Root level "All Files" folder only */}
            <div className="mb-3">
              <div 
                className={`px-3 py-1.5 hover:bg-white/5 rounded cursor-pointer text-sm ${
                  selectedFolderId === 'all' ? 'bg-[#22D3EE]/10 text-[#22D3EE]' : 'text-white/70'
                }`}
                onClick={() => onSelectFolder('all')}
              >
                {t('allFiles', 'All Files')}
              </div>
            </div>
            
            {/* Regular folder structure */}
            <div>
              <div className="text-xs uppercase text-white/40 mb-2 px-2">{t('myFolders', 'My Folders')}</div>
              <FolderList 
                folders={folders}
                selectedFolderId={selectedFolderId}
                expandedFolders={expandedFolders}
                onToggleExpand={handleToggleExpand}
                onSelectFolder={onSelectFolder}
                onCreateFolder={onCreateFolder}
                onRenameFolder={handleRenameFolder}
                onDeleteFolder={handleDeleteFolder}
              />
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export default FolderNavigation;
