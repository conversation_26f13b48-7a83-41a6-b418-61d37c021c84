import React, { useState, useEffect, RefObject, FormEvent } from 'react';
import { motion } from 'framer-motion';
import { XMarkIcon, PencilIcon } from '@heroicons/react/24/outline';
import { useTranslation } from 'react-i18next';
import { Folder } from '../../../../../../types/contentStudio';

interface FolderRenameModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRenameFolder: (updatedFolder: Folder) => void;
  folder: Folder | null;
  allFolders: Folder[];
  contentStudioRef: RefObject<HTMLDivElement | null>;
}

const FolderRenameModal: React.FC<FolderRenameModalProps> = ({ isOpen, onClose, onRenameFolder, folder, allFolders, contentStudioRef }) => {
  const { t } = useTranslation('contentStudio');
  const [folderName, setFolderName] = useState<string>('');
  const [error, setError] = useState<string | null>(null);  

  // Set initial folder name when folder changes
  useEffect(() => {
    if (folder) {
      setFolderName(folder.name || '');
    }
  }, [folder]);
  
  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    // Validate folder name
    if (!folderName.trim()) {
      setError('errorFolderNameRequired');
      return;
    }
    
    // No change
    if (folderName.trim() === folder?.name) {
      setError('');
      onClose();
      return;
    }
    
    // Check if folder name already exists at this level
    const existingFolder = allFolders.find(f => 
      f.name.toLowerCase() === folderName.trim().toLowerCase() && 
      f.parentId === folder?.parentId &&
      f.id !== folder?.id
    );
    
    if (existingFolder) {
      setError('errorFolderAlreadyExists');
      return;
    }
    
    // Rename folder
    onRenameFolder({
      ...folder!,
      name: folderName.trim()
    });
    
    // Reset and close
    setError('');
    onClose();
  };
  
  if (!isOpen || !folder) return null;
  
  return (
    <div 
      className="absolute inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
      onClick={() => {
        setError('');
        onClose();
      }}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%'
      }}
    >
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        className="w-full max-w-md bg-[#1A1F2E] rounded-xl border border-white/10 overflow-hidden shadow-xl"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between p-4 border-b border-white/10">
          <h2 className="text-xl font-light text-white flex items-center gap-2">
            <PencilIcon className="w-5 h-5 text-[#22D3EE]" />
            {t('renameFolder', 'Rename Folder')}
          </h2>
          <button 
            onClick={() => {
              setError('');
              onClose();
            }}
            className="p-2 rounded-full bg-transparent hover:bg-white/10 transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-white/70" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6">
          <div className="mb-6">
            <label className="block text-white/70 text-sm mb-2">{t('newFolderName', 'New Folder Name')}</label>
            <input
              type="text"
              value={folderName}
              onChange={(e) => {
                if(e.target.value.length <= 20){
                  setFolderName(e.target.value);
                }
                setError(''); // Clear error when typing
              }}
              className={`w-full bg-white/5 border ${
                error ? 'border-red-500' : 'border-white/10'
              } rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#22D3EE]/30`}
              placeholder={t('placeholderNewFolderName', 'Enter new folder name')}
              autoFocus
            />
            {error && <p className="text-red-500 text-xs mt-1">{t(error, 'Error')}</p>}
          </div>
          
          <div className="flex items-center justify-end gap-3">
            <button
              type="button"
              onClick={() => {
                setError('');
                onClose();
              }}
              className="px-4 py-2 bg-white/5 text-white rounded-lg hover:bg-white/10 transition-colors"
            >
              {t('cancel', 'Cancel')}
            </button>
            <button
              type="submit"
              className="px-4 py-2 rounded-lg transition-colors bg-[#22D3EE] text-black hover:bg-[#22D3EE]/90"
            >
              {t('renameFolder', 'Rename Folder')}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
}

export default FolderRenameModal;
