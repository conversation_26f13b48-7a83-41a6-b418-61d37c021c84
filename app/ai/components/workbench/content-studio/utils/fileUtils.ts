// Utility functions for file operations

/**
 * Formats a file size in bytes to a human-readable string
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size (e.g., "3.45 MB")
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Formats a date string into a readable format
 * @param {string} dateString - Date string to format
 * @param {string} format - Format type ('full', 'short', 'year')
 * @returns {string} Formatted date string
 */
export const formatDate = (dateString: string, format: 'full' | 'year' | 'short' = 'full'): string => {
  const date = new Date(dateString);
  
  switch(format) {
    case 'short':
      return date.toLocaleDateString('en-US', {
        year: '2-digit',
        month: '2-digit',
        day: '2-digit'
      }).replace(/\//g, '-');
      
    case 'year':
      return date.getFullYear().toString();
      
    default: // full
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
  }
};

/**
 * Extracts the dimensions (width and height) of an image file
 * @param {File} file - The image file to inspect
 * @returns {Promise<{ width: number, height: number }>} Promise resolving to image dimensions
 */
export const getImageDimensions = (file: File): Promise<{ width: number; height: number }>  => {
  return new Promise((resolve, reject) => {
    const url = URL.createObjectURL(file);
    const img = new Image();

    img.onload = () => {
      URL.revokeObjectURL(url); // cleanup
      resolve({ width: img.width, height: img.height });
    };

    img.onerror = reject;
    img.src = url;
  });
};

/**
 * Extracts a thumbnail image from a video File object.
 * 
 * @param {File} file - The video file to extract a thumbnail from
 * @param {string} [filename='thumbnail.jpg'] - Optional name for the resulting file
 * @returns {Promise<File>} A promise that resolves to a File (image/jpeg) of the video thumbnail
 * 
 * @example
 * const file = input.files[0];
 * const thumbnail = await extractVideoThumbnail(file);
 * upload(thumbnail);
 * 
 * @throws Will reject if the video cannot be loaded or the thumbnail cannot be generated
 * 
 * @note
 * - Only supports video formats playable by the browser (e.g., .mp4, .webm)
 * - Thumbnail is taken from the 1-second mark
 * - Cleans up memory using URL.revokeObjectURL
 */
export async function extractVideoThumbnail(file: File, filename = 'thumbnail.jpg'): Promise<File> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    const url = URL.createObjectURL(file);

    video.src = url;
    video.crossOrigin = 'anonymous';
    video.preload = 'metadata';
    video.muted = true;
    video.playsInline = true;

    video.currentTime = 1;

    video.onloadeddata = () => {
      try {
        const canvas = document.createElement('canvas');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        const ctx = canvas.getContext('2d');
        if (!ctx) throw new Error('Failed to get canvas context');

        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        canvas.toBlob((blob) => {
          URL.revokeObjectURL(url);
          if (!blob) return reject(new Error('Could not convert canvas to blob'));
          
          const thumbnailFile = new File([blob], filename, { type: 'image/jpeg' });
          resolve(thumbnailFile);
        }, 'image/jpeg', 0.8);
      } catch (error) {
        URL.revokeObjectURL(url);
        reject(error);
      }
    };

    video.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load video for thumbnail extraction'));
    };
  });
}
