import React, { useEffect, useState } from 'react';
import { formatDistanceToNow } from 'date-fns';
import { CircleStackIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';
import { backendApiFetch } from '~/ai/lib/backend-api';
import { workbenchStore } from '~/ai/lib/stores/workbench';
import { useTranslation } from 'react-i18next';  // Use consistent translation hook

const STATUS_MAP = {
  ACTIVE_HEALTHY:        { label: 'Online',                connected: true,  color: '#4ADE80' },
  ACTIVE_UNHEALTHY:      { label: 'Degraded',              connected: true,  color: '#FBBF24' },
  RESTORING:             { label: 'Restoring',             connected: false, color: '#0EA5E9' },
  RESTORED:              { label: 'Restored',              connected: false, color: '#4ADE80' },
  CREATING:              { label: 'Creating',              connected: false, color: '#60A5FA' },
  PROVISIONING:          { label: 'Provisioning',          connected: false, color: '#60A5FA' },
  COMING_UP:             { label: 'Coming Up',             connected: false, color: '#60A5FA' },
  DELETING:              { label: 'Deleting',              connected: false, color: '#F87171' },
  DELETED:               { label: 'Deleted',               connected: false, color: '#9CA3AF' },
  PAUSING:               { label: 'Pausing',               connected: false, color: '#F59E0B' },
  PAUSED:                { label: 'Paused',                connected: false, color: '#F59E0B' },
  INACTIVE:              { label: 'Inactive',              connected: false, color: '#F59E0B' },
  SUSPENDED:             { label: 'Suspended',             connected: false, color: '#F59E0B' },
  RESUMING:              { label: 'Resuming',              connected: false, color: '#3B82F6' },
  UPDATING:              { label: 'Updating',              connected: false, color: '#6366F1' },
  MIGRATING:             { label: 'Migrating',             connected: false, color: '#8B5CF6' },
  MAINTENANCE:           { label: 'Maintenance',           connected: false, color: '#8B5CF6' },
  RESTARTING:            { label: 'Restarting',            connected: false, color: '#F87171' },
  BACKUP_IN_PROGRESS:    { label: 'Backup in progress',    connected: false, color: '#0EA5E9' },
  RESTORE_IN_PROGRESS:   { label: 'Restore in progress',   connected: false, color: '#0EA5E9' },
  FAILED:                { label: 'Failed',                connected: false, color: '#EF4444' },

  // fallback pentru tot ce nu e în map
  UNKNOWN:               { label: 'Unknown',               connected: false, color: '#9CA3AF' },
};


function DatabaseSection() {
  const [databases, setDatabases] = useState([{
    id: 'supabase',
    name: 'Supabase',
    connected: false,
    statusLabel: 'Loading...',
    lastSeen: '-',
    size: '-',
    region: '-',
    type: 'PostgreSQL',
    version: '-',
    color: '#22D3EE',
    error: false,
  }]);

  const projectSlug = workbenchStore.getSlug();
  const { t } = useTranslation('translation');
  const getSupabaseData = async () => {
    try {
      const res = await backendApiFetch(
        `/cloud/supabase/project-info/${projectSlug}`,
        { method: 'GET', headers: { 'Content-Type': 'application/json' } }
      );
      const json = await res.json();

      // 404: proiect inexistent
      if (json.statusCode === 404 && json.message === 'Project not found') {
        setDatabases([{
          id: "supabase",
          name: "Supabase",
          connected: false,
          statusLabel: 'Not Found',
          lastSeen: '-',
          size: '-',
          region: '-',
          type: "PostgreSQL",
          version: '-',
          color: '#22D3EE',
          error: true,
        }]);
        return;
      }

      // orice răspuns care are status
      if (json.status) {
        const {
          label: statusLabel,
          connected,
          color: statusColor,
        } = STATUS_MAP[json.status] ?? STATUS_MAP.UNKNOWN;
        const lastSeen =
          json.createdAt
            ? formatDistanceToNow(new Date(json.createdAt), { addSuffix: true })
            : '-';

        setDatabases([{
          id: projectSlug,
          name: json.name || projectSlug,
          connected,
          statusLabel,
          lastSeen,
          size: json.size ?? '-',
          region: json.region ?? '-',
          type: json.type ?? '-',
          version: json.version ?? '-',
          color: statusColor,
          error: false,
        }]);
      }
    } catch (err) {
      console.error('Error fetching Supabase data:', err);
      setDatabases((prev) =>
        prev.map((db) => ({
          ...db,
          connected: false,
          statusLabel: 'Error',
          error: true,
          color: '#EF4444',
        }))
      );
    }
  };

  useEffect(() => {
    getSupabaseData();
  }, []);

  return (
    <div className="bg-[#1A1F2E]/50 rounded-xl border border-white/5 overflow-hidden">
      <div className="flex items-center justify-between p-4 border-b border-white/5">
        <div className="flex items-center gap-3">
          <CircleStackIcon className="w-5 h-5 text-[#22D3EE]" />
          <h2 className="text-lg font-medium text-white">
            {t('database.title', 'Database Connections')}
          </h2>
        </div>
      </div>
      <div className="p-6 grid gap-4">
        {databases.map((db) => (
          <div key={db.id} className="bg-white/5 rounded-lg p-4 border border-white/5">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div
                  className="w-8 h-8 rounded-lg flex items-center justify-center"
                  style={{ backgroundColor: `${db.color}20`, color: db.color }}
                >
                  <CircleStackIcon className="w-5 h-5" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-white">{db.name}</h3>
                  <span className="text-sm text-white/50">{db.type}</span>
                </div>
                <span
                  className={clsx(
                    'px-2 py-0.5 rounded-full text-sm',
                    db.connected
                      ? 'bg-[#4ADE80]/10 text-[#4ADE80]'
                      : 'bg-white/10 text-white/50'
                  )}
                >
                  {t(`status.${db.statusLabel}`, db.statusLabel)}
                </span>
              </div>
              <button
                className={clsx(
                  'px-3 py-1.5 rounded-lg text-sm transition-colors',
                  db.connected
                    ? 'bg-[#4ADE80]/10 text-[#4ADE80] border border-[#4ADE80]/20'
                    : 'bg-white/5 text-white/70 hover:bg-white/10'
                )}
              >
                {db.error
                  ? t('database.button.notConnected', 'Not Connected')
                  : db.connected
                    ? t('database.button.connected', 'Connected')
                    : t('database.button.connect', 'Connect')}
              </button>
            </div>
            <div className="grid grid-cols-4 gap-4 text-sm">
              <div>
                <div className="text-white/50 mb-1">{t('database.fields.created', 'Created')}</div>
                <div className="text-white">{db.lastSeen}</div>
              </div>
              <div>
                <div className="text-white/50 mb-1">{t('database.fields.size', 'Size')}</div>
                <div className="text-white">{db.size}</div>
              </div>
              <div>
                <div className="text-white/50 mb-1">{t('database.fields.region', 'Region')}</div>
                <div className="text-white">{db.region}</div>
              </div>
              <div>
                <div className="text-white/50 mb-1">{t('database.fields.version', 'Version')}</div>
                <div className="text-white">{db.version}</div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default DatabaseSection;
