import React, { useState } from 'react';
import { useLoaderData } from 'react-router-dom';
import { ArrowDownTrayIcon, ChatBubbleLeftIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import ActionButton from '../../ActionButton';
import { UserStore } from '~/ai/lib/stores/user/userStore';
import { chatId, useChatHistory } from '~/ai/lib/persistence';
import { useTranslation } from 'react-i18next';
import { Copy } from 'lucide-react';
import { FaPaperPlane } from 'react-icons/fa';
import { backendApiFetch } from '~/ai/lib/backend-api';
import TransferModal from '~/backOffice/components/project/TransferProjectModal';
import ConfirmationDialog, { DialogConfirmationType } from '~/ai/components/ConfirmationDialog';
import { canDownloadOrExportAsync, canTransferProject } from '~/ai/lib/projectLimits';
import { sessionStore } from '~/ai/lib/stores/session/sessionStore';

interface LoaderDataType {
  id?: string;
}

function ProjectActions() {
  const { downloadProject, exportChat, duplicateCurrentChat } = useChatHistory();
  const [downloading, setDownloading] = React.useState(false);
  const [transferError, setTransferError] = useState<string | null>(null);
  const [showTransferProjectModal, setShowTransferProjectModal] = useState<string | boolean>(false);
  const [loadingTransferProjects, setLoadingTransferProjects] = useState<boolean>(false);
  const [isDuplicateOpen, setIsDuplicateOpen] = useState(false);
  const [isDuplicating, setIsDuplicating] = useState(false);
  const { t } = useTranslation('translation');

  const handleClickDuplicate = () => {
    setIsDuplicateOpen(true);
  };

  const generateProjectSlug = (chatId: string | undefined): string | null => {
    try {
      const user = UserStore.getInstance().getUser();

      if (!user || !user.id) {
        console.warn('User is not logged in or user ID is missing');
        return null;
      }

      if (!chatId) {
        console.warn('Chat ID is missing');
        return null;
      }

      return `${chatId}-${user.id}`;
    } catch (error) {
      console.error('Error generating project slug:', error);
      return null;
    }
  };

  const handleDownload = async () => {
    const slug = sessionStore.getSlug();

    if (!slug) {
      toast.error(t('projectActions.invalidSlug', 'Invalid project slug.'));
      return;
    }

    if (!(await canDownloadOrExportAsync(slug, 'download'))) {
      toast.error('You have reached the download limit for this project. Make changes to download again.');
      return;
    }

    setDownloading(true);

    try {
      await downloadProject(slug);
      toast.success(t('projectActions.downloadSuccess', 'Project downloaded successfully!'));
    } catch (error) {
      console.error('Error downloading project:', error);
      toast.error(t('projectActions.downloadError', 'Failed to download the project.'));
    } finally {
      setDownloading(false);
    }
  };

  const handleExport = async () => {
    const slug = generateProjectSlug(chatId.get());

    if (!slug) {
      toast.error(t('projectActions.invalidSlug', 'Invalid project slug.'));
      return;
    }

    if (!(await canDownloadOrExportAsync(slug, 'export'))) {
      toast.error('You have reached the export limit for this project. Make changes to export again.');
      return;
    }

    try {
      await exportChat(slug);
      toast.success(t('projectActions.exportSuccess', 'Chat exported! Check your downloads folder.'));
    } catch (error) {
      console.error('Error exporting chat:', error);
      toast.error(t('projectActions.exportError', 'Failed to export chat.'));
    }
  };

  const handleDuplicate = async () => {
    setIsDuplicating(true);

    const slug = generateProjectSlug(chatId.get());

    if (!slug) {
      toast.error(t('projectActions.invalidSlug', 'Invalid project slug.'));
      return;
    }

    try {
      await duplicateCurrentChat(slug);
      toast.success(t('projectActions.duplicateSuccess', 'Chat duplicated successfully!'));
    } catch (error) {
      console.error('Error duplicating chat:', error);
      toast.error(t('projectActions.duplicateError', 'Failed to duplicate chat.'));
    }

    setIsDuplicating(false);
    setIsDuplicateOpen(false);
  };

  const handleCloseTransfer = () => {
    setShowTransferProjectModal(false);
    setTransferError(null);
  };
  const handleTransferProject = async (transferTo: string) => {
    setLoadingTransferProjects(true);

    const slug = generateProjectSlug(chatId.get());

    if (!slug) {
      setTransferError(t('projectActions.invalidSlug', 'Invalid project slug.'));
      setLoadingTransferProjects(false);

      return;
    }

    if (!(await canTransferProject(slug, transferTo))) {
      setTransferError(
        t(
          'projectActions.transferLimit',
          'You have already transferred this version of the project to this user. Make changes to transfer again.',
        ),
      );
      setLoadingTransferProjects(false);

      return;
    }

    try {
      const response = await backendApiFetch(`/user-projects/${slug}/transfer/${transferTo}`, {method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });
      const data = await response.json();

      if (response.ok) {
        setTransferError(null);
        toast.success(
          t('transferSuccess', { user: transferTo, defaultValue: `Transferred successfully to ${transferTo}` }),
        );
        handleCloseTransfer();
      } else {
        throw new Error(data.message || 'transfer failed');
      }
    } catch (error: any) {
      const msgText = error.message || '';
      let message = t('transferError', 'Error transferring project');

      if (msgText.includes('permission')) {
        message = t('dontHavePermisionToTransfer', 'You do not have permission to transfer this project');
      } else if (msgText.includes('not found')) {
        message = t('transferProjectUserNotFound', {
          user: transferTo,
          defaultValue: `User ${transferTo} was not found!`,
        });
      } else if (msgText.includes('own account')) {
        message = t('transferErrorOwnAccount', 'You cannot transfer a project to your own account.');
      }

      setTransferError(message);
    } finally {
      setLoadingTransferProjects(false);
    }
  };

  return (
    <div className="flex items-center px-2.5 py-0.5 bg-[#232e41] border border-[#293447] rounded-lg border-radius-10">
      <ActionButton
        id={'download-project-files'}
        icon={ArrowDownTrayIcon}
        label={t('downloadProjectFiles', 'Download project files')}
        onClick={handleDownload}
        variant="download"
        downloading={downloading}
      />
      <ActionButton
        id={'export-chat'}
        icon={ChatBubbleLeftIcon}
        label={t('exportProjectChat', 'Export project chat')}
        onClick={handleExport}
      />
      <ActionButton
        id={'duplicate-project'}
        icon={Copy}
        label={t('duplicateProject', 'Duplicate project')}
        onClick={handleClickDuplicate}
      />
      <ActionButton
        id={'transfer-project'}
        icon={FaPaperPlane}
        openPosition={'bottom-end'}
        label={t('transferProject', 'Share a project copy with another biela user')}
        onClick={() => {
          setShowTransferProjectModal(true);
        }}
      />
      <TransferModal
        isOpen={showTransferProjectModal}
        isLoading={loadingTransferProjects}
        errorMessage={transferError}
        onClose={handleCloseTransfer}
        onTransfer={handleTransferProject}
      />

      <ConfirmationDialog
        isOpen={isDuplicateOpen}
        type={DialogConfirmationType.DUPLICATE}
        description={generateProjectSlug(chatId.get()) ?? ''}
        onConfirm={handleDuplicate}
        onCancel={() => setIsDuplicateOpen(false)}
        isLoading={isDuplicating}
      />
    </div>
  );
}

export default ProjectActions;
