import React from 'react';
import { AcademicCapIcon, CircleStackIcon, CodeBracketIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';

type SettingsSection = 'project' | 'database' | 'domain';

interface Section {
  id: SettingsSection;
  label: string;
  icon: React.ElementType;
  color: string;
}

interface SettingsHeaderProps {
  activeSection: SettingsSection;
  setActiveSection: (id: SettingsSection) => void;
}

const SettingsHeader: React.FC<SettingsHeaderProps> = ({ activeSection, setActiveSection }) => {
  const sections: Section[] = [
    { id: 'project', label: 'Project Info', icon: CodeBracketIcon, color: '[#4ADE80]' },
    { id: 'database', label: 'Database', icon: CircleStackIcon, color: '[#22D3EE]' },
    { id: 'domain', label: 'Domain Name', icon: AcademicCapIcon, color: '[#A78BFA]' },
  ];

  return (
    <div className="flex items-center justify-between px-4 py-2 border-b border-white/10">
      <div className="flex items-center gap-2">
        {sections.map(({ id, label, icon: Icon, color }) => {
          const bgColorClass = activeSection === id
            ? (id === 'project' ? 'bg-[#4ADE80]/10' :
               id === 'database' ? 'bg-[#22D3EE]/10' :
               id === 'domain' ? 'bg-[#A78BFA]/10' :
               'bg-red-500/10')
            : '';

          const textColorClass = activeSection === id
            ? (id === 'project' ? 'text-[#4ADE80]' :
               id === 'database' ? 'text-[#22D3EE]' :
               id === 'domain' ? 'text-[#A78BFA]' :
               'text-red-500')
            : 'text-white/70';

          return (
            <button
              key={id}
              onClick={() => setActiveSection(id)}
              className={clsx(
                'flex items-center gap-2 px-3 py-1.5 rounded-md transition-colors text-sm',
                bgColorClass,
                textColorClass,
                activeSection !== id && 'hover:bg-white/10'
              )}
            >
              <Icon className="w-4 h-4" />
              {label}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default SettingsHeader;
