import { UserStore } from '~/ai/lib/stores/user/userStore';

const BASE_URL = import.meta.env.CUSTOM_BACKEND_API;
const LOCAL_DEV_API = import.meta.env.CUSTOM_LOCAL_WITH_DEV_API;

export async function backendApiFetch(path: string, options: RequestInit = {}) {
  const userStore = UserStore.getInstance();

  const makeRequest = async () => {

    const normalizedHeaders: Record<string, string> = {};

    if (options.headers instanceof Headers) {
      options.headers.forEach((value, key) => {
        normalizedHeaders[key] = value;
      });
    } else if (Array.isArray(options.headers)) {
      options.headers.forEach(([key, value]) => {
        normalizedHeaders[key] = value;
      });
    } else if (typeof options.headers === 'object' && options.headers !== null) {
      Object.assign(normalizedHeaders, options.headers);
    }

    // if (LOCAL_DEV_API)
      // normalizedHeaders['user-cookie'] = refreshToken;

    return fetch(`${BASE_URL}${path}`, {
      ...options,
      headers: normalizedHeaders,
      credentials: 'include',
    });
  };

  // First attempt.
  let response = await makeRequest();

  // If unauthorized, refresh token and retry.
  if (response.status === 403) {
    response = await makeRequest(true);
  }

  return response;
}
