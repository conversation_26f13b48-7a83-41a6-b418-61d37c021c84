import { createContext, useContext, useState } from 'react';

const ChatOpenContext = createContext<{
  isChatOpen: boolean;
  setIsChatOpen: (val: boolean) => void;
}>({
  isChatOpen: false,
  setIsChatOpen: () => {},
});

export const ChatOpenProvider = ({ children }: { children: React.ReactNode }) => {
  const [isChatOpen, setIsChatOpen] = useState(false);

  return (
    <ChatOpenContext.Provider value={{ isChatOpen, setIsChatOpen }}>
      {children}
    </ChatOpenContext.Provider>
  );
};

export const useChatOpen = () => useContext(ChatOpenContext);
