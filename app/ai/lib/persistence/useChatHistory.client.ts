import { useNavigate, useSearchParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { atom } from 'nanostores';
import type { Message } from 'ai';
import { toast } from 'react-toastify';
import { workbenchStore } from '~/ai/lib/stores/workbench';
import { logStore } from '~/ai/lib/stores/logs'; // Import logStore
import { createChatFromMessages, duplicateChat, getMessages, getUrlId, openDatabase, setMessages } from './db';
import JSZip from 'jszip';
import { createProject, getProject, updateProjectName } from '~/api/projectsApi';
import { UserStore } from '../stores/user/userStore';
import { uploadChatMessages, downloadChatMessages } from '../stores/projects/chat';
import { downloadProjectZipBuffer, uploadProject } from '~/ai/lib/stores/projects/code';

export interface ChatHistoryItem {
  id: string;
  urlId?: string;
  description?: string;
  messages: Message[];
  timestamp: string;
}

type ChatExportData = {
  messages: {
    id: string;
    role: string;
    content: string;
    createdAt: string;
  }[];
  exportDate: string;
};

interface ChatMessagesResponse {
  items: any[];
  totalPages: number;
  page: number;
  limit: number;
  total?: number;
}

const persistenceEnabled = !import.meta.env.VITE_DISABLE_PERSISTENCE;
export const db = persistenceEnabled ? await openDatabase() : undefined;
export const chatId = atom<string | undefined>(generateUniqueChatId());
export const description = atom<string | undefined>(undefined);
export const chatTimestamp = atom<string | undefined>(undefined);

export function useChatHistory() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [initialMessages, setInitialMessages] = useState<Message[]>([]);
  const [ready, setReady] = useState<boolean>(false);
  const [urlId, setUrlId] = useState<string | undefined>();
  const [mixedId, setMixedId] = useState<string | undefined>();

  useEffect(() => {
    const id = new URLSearchParams(window.location.search).get('id');
    setMixedId(id || undefined);
  }, []);

  useEffect(() => {
    if (!db) {
      setReady(true);

      if (persistenceEnabled) {
        logStore.logError('Chat persistence initialization failed', new Error('Chat persistence is unavailable'));
        toast.error('Chat persistence is unavailable');
      }

      return;
    }

    if (mixedId) {
      loadChat(mixedId).then((found) => {
        if (!found) {
          //navigate('/', { replace: true });
        }

        setReady(true);
      });
    }
  }, [mixedId]);

  const loadChatById = async (id: string) => {
    if (!db) {
      setReady(true);
      return;
    }

    try {
      const chat = await getMessages(db, id);
      console.log(chat, 'chat');
      if (chat && chat.messages.length > 0) {
        await setMessages(db!, chat.id, chat.messages, chat.urlId, chat.description);
        
        setInitialMessages(chat.messages);
        setReady(true);
        chatId.set(chat.id);
      } else {
        setReady(true);
        //navigate('/', { replace: true });
      }
    } catch (error) {
      logStore.logError('Failed to load chat messages', error);
      toast.error(error.message);
      setReady(true);
    }
  };

  const loadChat = async (chatIdentifier: string) => {
    try {
      console.log(`Loading chat with identifier: ${chatIdentifier}`);

      const storedMessages = await getMessages(db, chatIdentifier);

      if (storedMessages && storedMessages.messages.length > 0) {
        const rewindId = searchParams.get('rewindTo');
        const filteredMessages = rewindId
          ? storedMessages.messages.slice(0, storedMessages.messages.findIndex((m) => m.id === rewindId) + 1)
          : storedMessages.messages;

        setInitialMessages(filteredMessages);
        setUrlId(storedMessages.urlId);
        description.set(storedMessages.description);
        chatId.set(storedMessages.id);
        chatTimestamp.set(storedMessages.timestamp);

        if (rewindId) {
          const newSearchParams = new URLSearchParams(searchParams.toString());
          newSearchParams.delete('rewindTo');
          navigate({ pathname: window.location.pathname, search: newSearchParams.toString() }, { replace: true });
        }

        return true;
      }

      const projectSlug = generateProjectSlug(chatIdentifier);

      if (!projectSlug) {
        console.warn('No project slug found for chat identifier:', chatIdentifier);
        return false;
      }

      const projectData = await getProject(projectSlug);

      if (projectData) {
        const remoteMessages = await fetchAllChatMessages(projectSlug);

        console.log(`Remote messages count: ${remoteMessages.length}`);

        if (remoteMessages && remoteMessages.length > 0) {
          console.log(`Storing messages for project: ${projectSlug}`);

          const projectName = projectData.projectName || 'Imported Project';

          const formattedMessages = remoteMessages.map((message) => ({
            id: message._id || message.id,
            role: message.role,
            content: message.content,
            ...(message.reasoning ? { reasoning: message.reasoning } : {}),
            createdAt: message.createdAt || new Date().toISOString(),
            ...(message.hiddenAt ? { hiddenAt: message.hiddenAt } : {}),
          }));

          await setMessages(db, chatIdentifier, formattedMessages, chatIdentifier, projectName);

          setInitialMessages(formattedMessages);
          setUrlId(chatIdentifier);
          description.set(projectName);
          chatId.set(chatIdentifier);
          chatTimestamp.set(new Date().toISOString());

          return true;
        }
      }

      return false;
    } catch (error) {
      logStore.logError('Failed to load chat messages', error);
      toast.error(error.message);
      setReady(true);

      return false;
    }
  };

  return {
    ready: !mixedId || ready,
    initialMessages,
    storeMessageHistory: async (messages: Message[]) => {
      if (!db || messages.length === 0) {
        return;
      }

      const { firstArtifact } = workbenchStore;

      if (!urlId && firstArtifact?.id) {
        const urlId = await getUrlId(db, firstArtifact.id);

        navigateChat(urlId);
        setUrlId(urlId);
      }

      if (!description.get() && firstArtifact?.title) {
        description.set(firstArtifact?.title);
      }

      if (initialMessages.length === 0 && !chatId.get()) {
        const nextId = generateUniqueChatId();

        chatId.set(nextId);
        chatTimestamp.set(new Date().toISOString());

        if (!urlId) {
          navigateChat(nextId);
        }
      }

      await setMessages(db, chatId.get() as string, messages, urlId, description.get());
    },
    duplicateCurrentChat: async (projectSlug: string, projectName?: string) => {
      if (!db || !projectSlug) {
        return;
      }

      try {
        const { newId, description, messages } = await duplicateChat(db, projectSlug.split('-')[0].toString());
        const newProjectSlug = `${newId}-${projectSlug.split('-')[1].toString()}`;

        // Create project and save messages to Database & Github
        await createProject(newProjectSlug, description);
        await uploadChatMessages(newProjectSlug, messages);

        const projectZip = await createFilesFromMessages(messages);
        await uploadProject(projectSlug, projectZip, newProjectSlug);

        if (projectName) {
          const newProjectName = projectName + ' - Copy';
          await updateProjectName(newProjectSlug, newProjectName);
        }
        navigate(`/chat/${newId}`);
      } catch (error) {
        toast.error('Failed to duplicate chat');
        console.error(error);
      }
    },
    importChat: async (description: string, messages: Message[], isUploadProject: boolean = false) => {
      const loadingToast = toast.loading(`Importing ${description}...`);

      try {
        const newId = await createChatFromMessages(db, description, messages);

        const projectSlug = generateProjectSlug(newId);

        if (!projectSlug) {
          throw new Error('Failed to generate project slug. User might not be logged in.');
        }

        // Create project and save messages to Database & Github
        await createProject(projectSlug, description);
        await uploadChatMessages(projectSlug, messages);
        if (isUploadProject) {
          await updateProjectName(projectSlug, description);
        }
        const projectZip = await createFilesFromMessages(messages);
        await uploadProject(projectSlug, projectZip);

        window.location.href = `/chat/${newId}`;
      } catch (error) {
        if (error instanceof Error) {
          toast.error('Failed to import chat: ' + error.message);
        } else {
          toast.error('Failed to import chat');
        }
      } finally {
        toast.dismiss(loadingToast);
      }
    },
    exportChat: async (projectSlug: string) => {
      try {
        const allMessages = await fetchAllChatMessages(projectSlug);

        const orderedMessages = allMessages
          .slice()
          .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

        // Transform the data to required export format
        const exportData: ChatExportData = {
          messages: orderedMessages.map((message) => ({
            id: message._id,
            role: message.role,
            content: message.content,
            ...(message.reasoning ? { reasoning: message.reasoning } : {}),
            createdAt: message.createdAt,
          })),
          exportDate: new Date().toISOString(),
        };

        // Export as JSON file
        const timestamp = new Date().toISOString().replace(/:/g, '_').replace(/\./g, '_');
        exportToFile(exportData, `chat-${timestamp}.json`, {
          type: 'application/json',
          transform: (data) => JSON.stringify(data, null, 2),
        });

        return exportData;
      } catch (error) {
        console.error('Error downloading chat messages:', error);
        throw error;
      }
    },
    downloadProject: async (projectSlug: string) => {
      try {
        const response = await downloadProjectZipBuffer(projectSlug);

        if (response === undefined || response === null) {
          toast.warning("This project hasn't been saved yet. Please try again later.");
          return;
        }

        // Export as ZIP file
        exportToFile(response, `${projectSlug}.zip`, {
          type: 'application/zip',
        });
      } catch (error) {
        console.error('Error downloading project:', error);
        toast.error('Failed to download the project. Please try again later.');
      }
    },
    loadChatById,
    loadChat,
  };
}

const exportToFile = <T>(
  data: T,
  filename: string,
  options: {
    type: string;
    transform?: (data: T) => BlobPart;
  },
) => {
  const { type, transform } = options;
  const blobData = transform ? transform(data) : (data as BlobPart);
  const blob = new Blob([blobData], { type });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = url;
  link.download = filename;

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export function generateProjectSlug(chatId: string): string | null {
  try {
    const userStore = UserStore.getInstance();
    const user = userStore.getUser();

    if (!user || !user.id) {
      console.warn('User is not logged in or user ID is missing');
      return null;
    }

    return `${chatId}-${user.id}`;
  } catch (error) {
    console.error('Error generating project slug:', error);
    return null;
  }
}

export async function fetchAllChatMessages(projectSlug: string) {
  try {
    let allMessages: any[] = [];

    // Get the first page to determine total pages
    const firstPageResponse = (await downloadChatMessages(projectSlug, 0, 100)) as ChatMessagesResponse;

    // Add messages from first page
    allMessages = [...(firstPageResponse.items || [])];

    // If there are more pages, fetch them
    const totalPages = firstPageResponse.totalPages || 1;

    for (let page = 1; page < totalPages; page++) {
      try {
        const pageResponse = (await downloadChatMessages(projectSlug, page, 100)) as ChatMessagesResponse;
        const pageMessages = pageResponse.items || [];

        // Add messages from this page to our collection
        allMessages = [...allMessages, ...pageMessages];
      } catch (pageError) {
        console.error(`Error fetching page ${page}:`, pageError);
      }
    }

    return allMessages;
  } catch (error) {
    console.error('Error fetching all chat messages:', error);
    return [];
  }
}

async function createFilesFromMessages(messages: Message[]): Promise<Blob> {
  const zip = new JSZip();
  const fileMap = new Map<string, string>();
  const assistantMessages = messages.filter((msg) => msg.role === 'assistant');

  for (const message of assistantMessages) {
    if (!message.content) {
      continue;
    }

    // Look for bielaAction tags in the content using regex
    const regex = /<bielaAction\s+type="file"\s+filePath="([^"]+)">([\s\S]*?)<\/bielaAction>/g;

    let match;

    while ((match = regex.exec(message.content)) !== null) {
      const filePath = match[1];
      const content = match[2].trim();

      if (filePath && content) {
        fileMap.set(filePath, content);
      }
    }
  }

  // Add files to the zip
  for (const [filePath, content] of fileMap.entries()) {
    const relativePath = filePath.replace(/^\/+/, '');
    const pathSegments = relativePath.split('/');

    // If there's more than one segment, we need to create folders
    if (pathSegments.length > 1) {
      let currentFolder = zip;

      for (let i = 0; i < pathSegments.length - 1; i++) {
        currentFolder = currentFolder.folder(pathSegments[i])!;
      }
      currentFolder.file(pathSegments[pathSegments.length - 1], content);
    } else {
      // If there's only one segment, it's a file in the root
      zip.file(relativePath, content);
    }
  }

  return await zip.generateAsync({ type: 'blob' });
}

function navigateChat(nextId: string) {
  /**
   * FIXME: Using the intended navigate function causes a rerender for <Chat /> that breaks the app.
   *
   * `navigate(`/chat/${nextId}`, { replace: true });`
   */
  const url = new URL(window.location.href);
  url.pathname = `/chat/${nextId}`;

  window.history.replaceState({}, '', url);
}

export function generateUniqueChatId() {
  return Date.now().toString();
}
