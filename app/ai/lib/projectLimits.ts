import { getProjectCommits } from '~/ai/lib/stores/projects/code';

export const getLastCommitHashAsync = async (slug: string): Promise<string | null> => {
  try {
    const commits = await getProjectCommits(slug);
    if (!commits || !commits.length) return null;
    
    localStorage.setItem(`commits-${slug}`, JSON.stringify(commits));
    return commits[0].sha;
  } catch {
    return null;
  }
};

export const canDownloadOrExportAsync = async (slug: string, action: 'download' | 'export'): Promise<boolean> => {
  const lastHash = await getLastCommitHashAsync(slug);
  if (!lastHash) return true;

  const key = `${action}-count-${slug}`;
  const hashKey = `${action}-hash-${slug}`;
  const lastUsedHash = localStorage.getItem(hashKey);
  let count = Number(localStorage.getItem(key) || 0);

  if (lastUsedHash !== lastHash) {
    localStorage.setItem(hashKey, lastHash);
    localStorage.setItem(key, '1');
    return true;
  } else {
    if (count >= 3) return false;
    localStorage.setItem(key, String(count + 1));
    return true;
  }
};

export const canTransferProject = async (slug: string, transferTo: string): Promise<boolean> => {
  const lastHash = await getLastCommitHashAsync(slug);
  if (!lastHash) return true;
  const key = `transfer-${slug}-${transferTo}`;
  const lastTransferredHash = localStorage.getItem(key);
  if (lastTransferredHash === lastHash) {
    return false;
  }

  localStorage.setItem(key, lastHash);
  return true;
};