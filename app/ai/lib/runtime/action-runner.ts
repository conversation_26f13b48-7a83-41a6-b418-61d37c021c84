import { atom, map, type MapStore } from 'nanostores';
import type { ActionAlert, bielaAction } from '~/types/actions';
import { createScopedLogger } from '~/utils/logger';
import { unreachable } from '~/utils/unreachable';
import type { ActionCallbackData } from './message-parser';
import { UserStore } from '~/ai/lib/stores/user/userStore';
import SessionStore, { sessionStore } from '../stores/session/sessionStore';
import { workbenchStore } from '~/ai/lib/stores/workbench';
import { backendApiFetch } from '../backend-api';
import { toast } from 'react-toastify';

const logger = createScopedLogger('ActionRunner');

export type ActionStatus = 'pending' | 'running' | 'complete' | 'aborted' | 'failed';

export type BaseActionState = bielaAction & {
  status: Exclude<ActionStatus, 'failed'>;
  abort: () => void;
  executed: boolean;
  abortSignal: AbortSignal;
};

export type FailedActionState = bielaAction &
  Omit<BaseActionState, 'status'> & {
  status: Extract<ActionStatus, 'failed'>;
  error: string;
};

export type ActionState = BaseActionState | FailedActionState;

type BaseActionUpdate = Partial<Pick<BaseActionState, 'status' | 'abort' | 'executed'>>;

export type ActionStateUpdate =
  | (BaseActionUpdate & { migrationTitle?: string })
  | (Omit<BaseActionUpdate, 'status'> & { status: 'failed'; error: string });

type ActionsMap = MapStore<Record<string, ActionState>>;

class ActionCommandError extends Error {
  readonly _output: string;
  readonly _header: string;

  constructor(message: string, output: string) {
    // Create a formatted message that includes both the error message and output
    const formattedMessage = `Failed To Execute Shell Command: ${message}\n\nOutput:\n${output}`;
    super(formattedMessage);

    // Set the output separately so it can be accessed programmatically
    this._header = message;
    this._output = output;

    // Maintain proper prototype chain
    Object.setPrototypeOf(this, ActionCommandError.prototype);

    // Set the name of the error for better debugging
    this.name = 'ActionCommandError';
  }

  // Optional: Add a method to get just the terminal output
  get output() {
    return this._output;
  }

  get header() {
    return this._header;
  }
}

function extractMigrationTitle(content: string): string | null {
  const lines = content.split('\n');

  for (const line of lines) {
    const trimmed = line.trim();

    if (trimmed.startsWith('#')) {
      return trimmed.replace(/^#\s*/, '');
    }

    if (/^\d+\.\s+/.test(trimmed)) {
      return trimmed.replace(/^\d+\.\s+/, '');
    }
  }

  return null;
}

function extractMigrationTitleAfterPath(filePath: string): string {
  const match = filePath.match(/migrations\/(.*?)\.sql/);
  if (!match || !match[1]) {
    throw new Error('Invalid file path format');
  }
  return match[1]
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

const globalCommandTracking = {
  executedCommands: new Set<string>(),
  runningCommands: new Set<string>(),
  queuedCommands: new Set<string>(),
  isInitialLoad: true,
  initialLoadTimeout: null as NodeJS.Timeout | null
};

function completeInitialLoad() {
  if (globalCommandTracking.initialLoadTimeout) {
    clearTimeout(globalCommandTracking.initialLoadTimeout);
  }
  globalCommandTracking.isInitialLoad = false;
  globalCommandTracking.executedCommands.clear();
  globalCommandTracking.runningCommands.clear();
  globalCommandTracking.queuedCommands.clear();
}

if (typeof window !== 'undefined') {
  globalCommandTracking.initialLoadTimeout = setTimeout(() => {
    completeInitialLoad();
  }, 30000);
}

export class ActionRunner {
  #currentExecutionPromise: Promise<void> = Promise.resolve();
  #currentShellAction: Promise<void> = Promise.resolve();
  runnerId = atom<string>(`${Date.now()}`);
  actions: ActionsMap = map({});
  store = SessionStore.getInstance();

  onAlert?: (alert: ActionAlert) => void;

  constructor(
    onAlert?: (alert: ActionAlert) => void,
  ) {
    this.onAlert = onAlert;
  }

  addAction(data: ActionCallbackData) {
    const { actionId } = data;

    const actions = this.actions.get();
    const action = actions[actionId];

    if (action) {
      if (action.type === 'file' && action.status === 'running') {
        this.#updateAction(actionId, { status: 'failed', error: 'Action failed' });
      }

      // action already added
      return;
    }

    if (globalCommandTracking.isInitialLoad && data.action.type === 'migration') {
      // const title = extractMigrationTitleAfterPath(data.action.path);
      // this.#updateAction(actionId, { status: 'complete', executed: true, migrationTitle: title });
      return;
    }

    if (globalCommandTracking.isInitialLoad &&
      (data.action.type === 'shell' || data.action.type === 'start' || data.action.type === 'unit_testing_shell')) {
      const command = data.action.content;

      if (globalCommandTracking.executedCommands.has(command)) {
        return;
      }

      if (globalCommandTracking.runningCommands.has(command) || globalCommandTracking.queuedCommands.has(command)) {
        return;
      }
    }

    const abortController = new AbortController();

    this.actions.setKey(actionId, {
      ...data.action,
      status: 'pending',
      executed: false,
      abort: () => {
        abortController.abort();
        this.#updateAction(actionId, { status: 'aborted' });
      },
      abortSignal: abortController.signal,
    });
    if(data.action.type == "file")
      this.#currentExecutionPromise.then(() => {
        this.#updateAction(actionId, { status: 'running' });
      });
  }

  async runAction(data: ActionCallbackData, isStreaming: boolean = false) {
    const { actionId } = data;
    const action = this.actions.get()[actionId];


    if (!action) {
      unreachable(`Action ${actionId} not found`);
    }

    if (action.executed) {
      return; // No return value here
    }

    if (globalCommandTracking.isInitialLoad && action.type === 'migration') {
      return;
    }

    if (isStreaming && action.type !== 'file') {
      return;
    }

    if (globalCommandTracking.isInitialLoad &&
      (action.type === 'shell' || action.type === 'start' || action.type === 'unit_testing_shell')) {
      const command = action.content;

      if (globalCommandTracking.executedCommands.has(command)) {
        this.#updateAction(actionId, { status: 'complete', executed: true });
        return;
      }

      if (globalCommandTracking.runningCommands.has(command) || globalCommandTracking.queuedCommands.has(command)) {
        this.#updateAction(actionId, { status: 'complete', executed: true });
        return;
      }

      globalCommandTracking.queuedCommands.add(command);
    }

    this.#currentExecutionPromise = this.#currentExecutionPromise
      .then(() =>  this.#executeAction(actionId, isStreaming))
      .catch((error) => {
        console.error('Action failed:', error);
      });

    await this.#currentExecutionPromise;

    return;
  }

  async #executeAction(actionId: string, isStreaming: boolean = false) {
    const action = this.actions.get()[actionId];

    try {
      switch (action.type) {
        case 'shell':
        case 'unit_testing_shell': {
          this.#currentShellAction = this.#currentShellAction.then(()=>this.#runShellAction(actionId, action));
          break;
        }
        case 'file': {
          this.#updateAction(actionId, { status: 'running' });
          await this.#runFileAction(actionId, action);
          this.#updateAction(actionId, { status: isStreaming ? 'running' : action.abortSignal.aborted ? 'aborted' : 'complete' });
          break;
        }
        case 'migration': {
          await this.#runMigrationAction(actionId, action);
          break;
        }
        case 'start': {
          // making the start app non blocking
          this.#currentShellAction = this.#currentShellAction.then(()=>this.#runStartAction(actionId, action));
          /*
           * adding a delay to avoid any race condition between 2 start actions
           * i am up for a better approach
           */
          await new Promise((resolve) => setTimeout(resolve, 2000));

          return;
        }
      }
    } catch (error) {
      if (action.abortSignal.aborted) {
        return;
      }

      this.#updateAction(actionId, { status: 'failed', error: 'Action failed' });
      logger.error(`[${action.type}]:Action failed\n\n`, error);

      if (!(error instanceof ActionCommandError)) {
        return;
      }

      if (action.type !==  'migration') {

        this.onAlert?.({
          type: 'error',
          title: 'Dev Server Failed',
          description: error.header,
          content: error.output,
        });
      }

      // re-throw the error to be caught in the promise chain
      throw error;
    }
  }

  async #runShellAction(actionId:string, action: ActionState) {
    if (action.type !== 'shell' && action.type !== 'unit_testing_shell') {
      unreachable('Expected shell action');
    }

    const command = action.content;

    try {
      if (globalCommandTracking.isInitialLoad) {
        globalCommandTracking.queuedCommands.delete(command);
        globalCommandTracking.runningCommands.add(command);
      }

      this.#updateAction(actionId, { status: 'running'});
      await this.store.runTask({ command: action.content });
      this.#updateAction(actionId, { status: 'complete', executed:true });

      if (globalCommandTracking.isInitialLoad) {
        globalCommandTracking.executedCommands.add(command);

        if (command === 'npm install') {
          setTimeout(() => {
            if (globalCommandTracking.isInitialLoad) {
            }
          }, 5000);
        }
      }
    } finally {
      if (globalCommandTracking.isInitialLoad) {
        globalCommandTracking.runningCommands.delete(command);
        globalCommandTracking.queuedCommands.delete(command);
      }
    }
  }

  async #runStartAction(actionId:string, action: ActionState) {
    if (action.type !== 'start') {
      unreachable('Expected start action');
    }

    const command = action.content;

    try {
      if (globalCommandTracking.isInitialLoad) {
        globalCommandTracking.queuedCommands.delete(command);
        globalCommandTracking.runningCommands.add(command);
      }

      this.#updateAction(actionId, { status: 'running'});
      try{
        await this.store.runTask({ command:action.content, isStartTask:true });
      }
      catch (error){
        this.#updateAction(actionId, { status: 'failed', error: 'Action failed' });
        this.onAlert?.({
          type: 'error',
          title: 'Dev Server Failed',
          description: error.header,
          content: error.output,
        });
        return
      }
      this.#updateAction(actionId, { status: 'complete', executed: true });

      workbenchStore.currentView.set("preview");
      this.store.onStartPreview.set(new Date());

      if (globalCommandTracking.isInitialLoad) {
        globalCommandTracking.executedCommands.add(command);

        if (command === 'npm run dev') {
          setTimeout(() => {
            completeInitialLoad();
          }, 3000);
        }
      }
    } finally {
      if (globalCommandTracking.isInitialLoad) {
        globalCommandTracking.runningCommands.delete(command);
        globalCommandTracking.queuedCommands.delete(command);
      }
    }
  }

  async #runFileAction(actionId:string, action: ActionState) {
    if (action.type !== 'file') {
      unreachable('Expected file action');
    }


   // this.store.openFile({ filePath: action.filePath});
  }

  #updateAction(id: string, newState: ActionStateUpdate) {
    const actions = this.actions.get();
    const action = { ...actions[id], ...newState };
    sessionStore.onAction.set(action);
    this.actions.setKey(id, action);
  }

  async #runMigrationAction(actionId: string, action: ActionState) {
    if (action.type !== 'migration') {
      unreachable('Expected migration action');
    }

    const userStore = UserStore.getInstance();
    const user = userStore.getUser();

    if (action.abortSignal.aborted) {
      this.#updateAction(actionId, { status: 'aborted' });
      return;
    }

    this.#updateAction(actionId, { status: 'running' });

    if (!user) {
      return;
    }

    try {
      const filePath = action.path.startsWith('/')
        ? action.path.slice(1)
        : action.path;

      const cleanPath = action.path.replace(/^\/home\/<USER>\//, '');
      const fileContent = await this.store.getFileContent({ filePath });
      const title = extractMigrationTitleAfterPath(filePath);

      this.#updateAction(actionId, {
        ...(title ? { migrationTitle: title } : {}),
      });

      const projectSlug = localStorage.getItem('projectSlug');

      if (!projectSlug) {
        throw new Error('Project not found');
      }

      const response = await backendApiFetch(`/cloud/supabase/migration`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          migrationContent: JSON.parse(fileContent),
          path: action.path,
          projectSlug: projectSlug
        })
      });

      const migrationResponse = await response.json();


      if (migrationResponse.success === false) {

        if (migrationResponse.error === 'SERVICE_UNAVAILABLE') {
          const toastId = toast.error(migrationResponse.message);

          toast.update(toastId, {
            duration: Infinity,
            autoClose: false
          });
          throw new Error(migrationResponse.message);
        }

        this.onAlert?.({
          type: 'migration',
          title: 'Migration Failed',
          description: migrationResponse.message,
          content: migrationResponse.error,
          source:'preview',
        });

        throw new Error(migrationResponse.message);
      }

      this.#updateAction(actionId, { status: 'complete', executed: true });

      logger.info('Migration executed successfully:', migrationResponse);

      return migrationResponse;

    } catch (error) {
      if (!action.abortSignal.aborted) {
        this.#updateAction(actionId, {
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      } else {
        this.#updateAction(actionId, { status: 'aborted' });
      }

      logger.error(`Migration failed for user: ${user.id} :`, error);
      throw new ActionCommandError(
        'Failed to execute migration',
        ''
      );
    }
  }

  static completeInitialLoad() {
    completeInitialLoad();
  }

  static resetInitialLoadProtection() {
    globalCommandTracking.isInitialLoad = true;
    globalCommandTracking.executedCommands.clear();
    globalCommandTracking.runningCommands.clear();
    globalCommandTracking.queuedCommands.clear();

    if (globalCommandTracking.initialLoadTimeout) {
      clearTimeout(globalCommandTracking.initialLoadTimeout);
    }

    globalCommandTracking.initialLoadTimeout = setTimeout(() => {
      completeInitialLoad();
    }, 30000);

  }

  static getTrackingState() {
    return {
      isInitialLoad: globalCommandTracking.isInitialLoad,
      executedCommands: Array.from(globalCommandTracking.executedCommands),
      runningCommands: Array.from(globalCommandTracking.runningCommands),
      queuedCommands: Array.from(globalCommandTracking.queuedCommands)
    };
  }
}
