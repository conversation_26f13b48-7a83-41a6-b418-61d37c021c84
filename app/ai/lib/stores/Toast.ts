import { toast, type ToastOptions } from 'react-toastify';

const defaultOptions: ToastOptions = {
  position: 'bottom-center',
  autoClose: false,
  hideProgressBar: true,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
};

let activeToasts: Set<string> = new Set();

export const showToast = {
  success: (message: string, options?: ToastOptions) => {
    if (!activeToasts.has(message)) {
      activeToasts.add(message);
      toast.success(message, {
        ...defaultOptions,
        ...options,
        onClose: () => activeToasts.delete(message),
      });
    }
  },
  error: (message: string, options?: ToastOptions) => {
    if (!activeToasts.has(message)) {
      activeToasts.add(message);
      toast.error(message, {
        ...defaultOptions,
        ...options,
        onClose: () => activeToasts.delete(message),
      });
    }
  },
};
