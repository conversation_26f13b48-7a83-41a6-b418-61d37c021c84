import { map, MapStore } from 'nanostores';
import { Estimations, Project } from '~/api/projectsApi';

type Metrics = {
  estimatedCost: {
    traditional: Project['estimatedCostTraditional'];
    tokens: Project['timeSpent'];
  };
  timeMetrics: {
    traditional: Project['estimatedTimeTraditional'];
    actual: Project['timeSpentAi'];
  };

  estimations:{
    confidenceScore: Project['estimations.confidenceScore'];
    estimatedCostTraditional: Estimations['estimations.estimatedCostTraditional'];
    estimatedTimeTraditional: Estimations['estimations.estimatedTimeTraditional'];
    estimatedNumberOfDevelopers: Estimations['estimaations.estimatedNumberOfDevelopers'];
    recommendedDeveloperLevel: Estimations['estimations.recommendedDeveloperLevel'];
    timeToMarket: Estimations['estimations.timeToMarket'];
    maintenanceCostPercentage: Estimations['estimations.maintenanceCostPercentage'];
    projectType: Estimations['estimations.projectType'];
    projectComplexity: Estimations['estimations.projectComplexity'];
    uniqueComponentCount: Estimations['estimations.uniqueComponentCount'];
    featureCount: Estimations['estimations.featureCount'];
    rangeOfUncertainty: Estimations['estimations.rangeOfUncertainty'];
    keyTechnologies: Estimations['keyTechnologies'];
    breakdown: Estimations['breakdown'];
  }
};

interface ChatStore {
  started: boolean;
  aborted: boolean;
  showChat: boolean;
  showPrompt: boolean;
  checkingList: boolean;
  errorDetails: boolean;
  cleaningProject: boolean;
  projectImportName: string;
  mode: 'chat' | 'code';
  isStreaming: boolean;
  isModifiedCode: boolean;
  estimationsProject: undefined | Metrics
};

export const chatStore: MapStore<ChatStore> = map({
  started: false,
  aborted: false,
  showChat: true,
  showPrompt: true,
  checkingList: false,
  errorDetails: false,
  cleaningProject: false,
  projectImportName: "",
  mode: 'code' as 'chat' | 'code',
  isStreaming: false,
  isModifiedCode: true,
  estimationsProject:undefined,
});

export function setChatMode(mode: 'chat' | 'code') {
  chatStore.setKey('mode', mode);
}

export function setIsStreaming(isStreaming: boolean) {
  chatStore.setKey('isStreaming', isStreaming);
}

export function setShowChat(showChat: boolean) {
  chatStore.setKey('showChat', showChat);
}

export function setShowPrompt(showPrompt: boolean) {
  chatStore.setKey('showPrompt', showPrompt);
}

export function setCheckingList(checkingList: boolean) {
  chatStore.setKey('checkingList', checkingList);
}

export function setCleaningProject(cleaningProject: boolean) {
  chatStore.setKey('cleaningProject', cleaningProject);
}
export function setErrorDetails(errorDetails: boolean) {
  chatStore.setKey('errorDetails', errorDetails);
}
export function setProjectImportName(projectImportName: string) {
  chatStore.setKey('projectImportName', projectImportName);
}
export function setIsModifiedCode(isModifiedCode: boolean) {
  chatStore.setKey('isModifiedCode', isModifiedCode);
}
export function setEstimationsData(estimationsProject: Metrics | undefined){
  chatStore.setKey('estimationsProject', estimationsProject);
}
