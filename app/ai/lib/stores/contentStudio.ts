import { backendApiFetch } from '~/ai/lib/backend-api';
import type { Filters, Folder, Image, Tag } from '../../../types/contentStudio';

interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export async function createFolder(name: string, parentId: string | null): Promise<Folder | undefined> {
  try {
    const path = `/user-studio/folders/create`;
    const body = { name, parentId };

    const response = await backendApiFetch(path, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });

    return await response.json();
  } catch (error) {
    console.error('Error creating a new folder:', error instanceof Error ? error.message : String(error));
  }
}

export async function getFoldersV2(
  page = 0,
  limit = 10,
  sortBy = 'updatedAt',
  order: 'ASC' | 'DESC' = 'DESC',
  searchTerm = ''
): Promise<PaginatedResponse<Folder> | undefined> {
  try {
    const path = `/user-studio/folders/v2`;
    const filtersObj = searchTerm ? { name: { search: searchTerm } } : {};

    const body = { page, limit, sortBy, order, filter: filtersObj };

    const response = await backendApiFetch(path, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });

    return await response.json();
  } catch (error) {
    console.error('Error getting folders:', error instanceof Error ? error.message : String(error));
  }
}

export async function renameFolder(folderId: string, name: string): Promise<Folder | undefined> {
  try {
    const path = `/user-studio/folders/${folderId}`;
    const body = { name };

    const response = await backendApiFetch(path, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });

    return await response.json();
  } catch (error) {
    console.error('Error renaming a folder:', error instanceof Error ? error.message : String(error));
  }
}

export async function deleteFolder(folderId: string): Promise<{ success: boolean } | undefined> {
  try {
    const path = `/user-studio/folders/${folderId}`;

    const response = await backendApiFetch(path, {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
    });

    return await response.json();
  } catch (error) {
    console.error('Error deleting a folder:', error instanceof Error ? error.message : String(error));
  }
}

// This is being used to upload the image to s3 as soon as the user selects it
export async function uploadResource(file: File): Promise<{ url: string; key: string } | undefined> {
  try {
    const path = `/user-studio/upload-file`;
    const formData = new FormData();
    formData.append('file', file);

    const response = await backendApiFetch(path, {
      method: 'POST',
      body: formData,
    });

    return await response.json();
  } catch (error) {
    console.error('Error uploading resources:', error instanceof Error ? error.message : String(error));
  }
}

// This is being used to create the resource, using the url provided by the uploadResource function
export async function createResource(resource: Partial<Image>): Promise<Image | undefined> {
  try {
    const path = `/user-studio/resources/upload`;

    const response = await backendApiFetch(path, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(resource),
    });

    return await response.json();
  } catch (error) {
    console.error('Error creating resources:', error instanceof Error ? error.message : String(error));
  }
}

export async function getResourcesV2(
  page = 0,
  limit = 10,
  sortBy = 'updatedAt',
  order: string | null = 'DESC',
  filters: Filters,
  selectedFolderId: string = 'all',
  searchTerm = '',
  recursive = false
): Promise<PaginatedResponse<Image> | undefined> {
  try {
    const path = `/user-studio/resources/v2?recursive=${recursive}`;
    const filtersObj: any = {};

    let from: number | undefined, to: number | undefined;
    if (filters?.dateFrom) from = new Date(filters.dateFrom).getTime();
    if (filters?.dateTo) to = new Date(filters.dateTo).getTime();

    if (from && to) {
      filtersObj.createdAt = { and: [{ gt: from }, { lt: to }] };
    } else if (from) {
      filtersObj.createdAt = { gt: from };
    } else if (to) {
      filtersObj.createdAt = { lt: to };
    }

    if (filters?.tags?.length) {
      filtersObj.tags = { or: filters.tags.map(tag => ({ eq: tag })) };
    }

    if (selectedFolderId && selectedFolderId != 'all') {
      filtersObj.folderId = { eq: selectedFolderId };
    }

    if (searchTerm) {
      filtersObj.title = { search: searchTerm };
    }

    const body = {
      page,
      limit,
      sortBy,
      order,
      filter: filtersObj,
    };

    const response = await backendApiFetch(path, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });

    return await response.json();
  } catch (error) {
    console.error('Error getting resources:', error instanceof Error ? error.message : String(error));
  }
}

export async function updateResource(resourceId: string, body: Partial<Image>): Promise<Image | undefined> {
  try {
    const path = `/user-studio/resources/${resourceId}`;

    const response = await backendApiFetch(path, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });

    return await response.json();
  } catch (error) {
    console.error('Error updating a resource:', error instanceof Error ? error.message : String(error));
  }
}

export async function deleteResource(resourceId: string): Promise<{ success: boolean } | undefined> {
  try {
    const path = `/user-studio/resources/${resourceId}`;

    const response = await backendApiFetch(path, {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
    });

    return await response.json();
  } catch (error) {
    console.error('Error deleting a resource:', error instanceof Error ? error.message : String(error));
  }
}

export async function getTagsV2(
  page = 0,
  limit = 10,
  sortBy = 'updatedAt',
  order: 'ASC' | 'DESC' = 'DESC',
  searchTerm = ''
): Promise<PaginatedResponse<Tag> | undefined> {
  try {
    const path = `/user-studio/tags/v2`;
    const filtersObj = searchTerm ? { name: { search: searchTerm } } : {};

    const body = { page, limit, sortBy, order, filter: filtersObj };

    const response = await backendApiFetch(path, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });

    return await response.json();
  } catch (error) {
    console.error('Error getting tags:', error instanceof Error ? error.message : String(error));
  }
}

export async function createTag(name: string): Promise<Tag | undefined> {
  try {
    const path = `/user-studio/tags/create`;
    const body = { name };

    const response = await backendApiFetch(path, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });

    return await response.json();
  } catch (error) {
    console.error('Error creating a new tag:', error instanceof Error ? error.message : String(error));
  }
}

export async function deleteTag(tagId: string): Promise<{ success: boolean } | undefined> {
  try {
    const path = `/user-studio/tags/${tagId}`;

    const response = await backendApiFetch(path, {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
    });

    return await response.json();
  } catch (error) {
    console.error('Error deleting a tag:', error instanceof Error ? error.message : String(error));
  }
}

export async function deleteImageFromS3(imageId: string): Promise<{ success: boolean } | undefined> {
  try {
    const path = `/user-studio/delete-temp-image`;
    const body = {
      imageId,
      isPublic: true,
    };

    const response = await backendApiFetch(path, {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });

    return await response.json();
  } catch (error) {
    console.error('Error deleting an s3 image:', error instanceof Error ? error.message : String(error));
  }
}