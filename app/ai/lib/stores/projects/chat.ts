import { backendApiFetch } from '~/ai/lib/backend-api';
import type { Message } from 'ai';

export async function downloadChatMessages(projectSlug: string, page: number = 0, limit: number = 25) {
  try {
    const response = await backendApiFetch(`/user-projects/chat/${projectSlug}/messages?page=${page}&limit=${limit}`, {
      method: 'GET',
    });

    return await response.json();
  } catch (error) {
    console.error('Error downloading chat messages:', error instanceof Error ? error.message : String(error));
  }
}

export async function uploadChatMessages(projectSlug: string, messages: Message[]) {
  try {
    const response = await backendApiFetch(`/user-projects/chat/${projectSlug}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ messages }),
    });

    return await response.json();
  } catch (error) {
    console.error('Error uploading chat messages:', error instanceof Error ? error.message : String(error));
  }
}

export async function clearChatMessages(projectSlug: string) {
  try {
    const response = await backendApiFetch(`/user-projects/chat/${projectSlug}/messages/clean`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return await response.json();
  } catch (error) {
    console.error('Error clearing chat messages:', error instanceof Error ? error.message : String(error));
    throw error;
  }
}