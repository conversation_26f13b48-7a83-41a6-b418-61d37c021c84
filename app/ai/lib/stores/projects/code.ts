import { backendApiFetch } from '~/ai/lib/backend-api';
import { workbenchStore } from '~/ai/lib/stores/workbench';
import { Message } from 'ai';

export interface Project {
  createdAt: string;
  projectSlug: string;
  updatedAt: Date;
  userId: string;
  _id: string;
}

export interface ProjectCommits {
  date: Date;
  message: string;
  sha: string;
  messages?: Message[];
}

export async function uploadProject(projectIdentifier?: string, projectZip?: Blob, newProjectSlug?:string): Promise<ProjectCommits[] | null> {
  try {
    const useProvidedData = Boolean(projectIdentifier && projectZip);
    const { projectSlug = projectIdentifier, content = projectZip } = useProvidedData
      ? {}
      : await workbenchStore.getFilesZip();

    if (content) {
      const formData = new FormData();
      formData.append('file', content, `${projectSlug}.zip`);

      if (newProjectSlug) {
        formData.append('newProjectSlug', newProjectSlug);
        formData.append('isDuplicateAction', 'true');
      }

      const url = `/user-projects/code/${projectSlug}/`;

      const response = await backendApiFetch(url, {
        method: 'POST',
        body: formData,
      });

      return (await response.json()) as ProjectCommits[];
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error saving project:', error instanceof Error ? error.message : String(error));
    return null;
  }
}

export async function downloadProjectZipBuffer(projectSlug: string, commitHash?: string) {
  try {
    let url = `/user-projects/code/${projectSlug}`;

    if (commitHash) {
      url += `/${commitHash}`;
    }

    const response = await backendApiFetch(url, {
      method: 'GET',
    });

    if (!response.ok) {
      if (response.status === 400) {
        return null;
      }
    }

    return await response.arrayBuffer();
  } catch (error) {
    console.error('Error deploying project:', error instanceof Error ? error.message : String(error));
    return null;
  }
}

export function setProjectCommits(projectSlug: string, commits: ProjectCommits[]) {
  const key = `commits-${projectSlug}`;

  localStorage.setItem(key, JSON.stringify(commits));
}

export async function getProjectCommits(projectSlug: string) {
  const key = `commits-${projectSlug}`;

  // ------------------------------------------------------------------------
  // ⚠️ TEMPORARY WORKAROUND: Disable over-eager localStorage caching
  //
  // We’re commenting out the initial localStorage check to force a fresh
  // backend request on every call. Without this, once we cache any commits
  // (e.g. saved before 13:00), the app will never fetch newer ones (e.g.
  // commits made until 17:00), resulting in stale or missing data.
  //
  // This is just a small, temporary fix – the final solution should instead
  // introduce proper cache invalidation (e.g., TTL, versioning, or manual
  // refresh) rather than removing caching entirely.
  // ------------------------------------------------------------------------
  // const locale = localStorage.getItem(key);
  //
  // if (locale) {
  //   return JSON.parse(locale) as ProjectCommits[];
  // }

  const commits = await listProjectCommits(projectSlug);

  setProjectCommits(projectSlug, commits as ProjectCommits[]);

  return commits;
}

export async function listProjectCommits(projectSlug?: string) {
  try {
    const slug = projectSlug ?? workbenchStore.getSlug();

    const url = `/user-projects/code/${slug}/commits`;

    const response = await backendApiFetch(url, {
      method: 'GET',
    });

    const json = await response.json();

    return json as ProjectCommits[];
  } catch (error) {
    console.error('Error listing project commits:', error instanceof Error ? error.message : String(error));
    return null;
  }
}
