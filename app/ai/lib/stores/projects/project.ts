import { backendApiFetch } from '~/ai/lib/backend-api';
import { workbenchStore } from '~/ai/lib/stores/workbench';

export async function updateProject(newName: string) {
  try {
    const projectSlug = await workbenchStore.getSlug();

    const body = {
      projectName: newName,
    };

    const url = `/user-projects/${projectSlug}/`;

    const response = await backendApiFetch(url, {
      method: 'PATCH',
      body: JSON.stringify(body),
      headers: { 'Content-Type': 'application/json' },
    });

    return await response.json();
  } catch (error) {
    console.error('Error updating project:', error instanceof Error ? error.message : String(error));
    return null;
  }
}
