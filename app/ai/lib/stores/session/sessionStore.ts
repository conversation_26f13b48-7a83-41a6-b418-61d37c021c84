import { workbenchStore } from '../workbench';
import { atom } from 'nanostores';
import { backendApiFetch } from '../../backend-api';
import { ProjectCommits } from '../projects/code';
import { ActionState } from '../../runtime/action-runner';
import { setAppData } from '~/ai/lib/stores/cloud/cloud-storage';

const sum = (arr: number[]) => arr.reduce((a, b) => a + b, 0);

const LOCAL_DEV_API = import.meta.env.CUSTOM_LOCAL_WITH_DEV_API;

interface SessionData {
  host: string;
  editorPath: string;
  hashedPassword: string;
  userIp: string;
  webcontainerApiPath: string;
  webcontainerId: string;
  portPathTemplate: string;
}

class SessionStore {
  private static _instance: SessionStore;
  sessionData = null as unknown as SessionData;
  hasWebcontainerSession = atom(false);
  availablePorts = atom<number[]>([]); // array cu toate porturile găsite

  onAction = atom<ActionState | undefined>(undefined);
  onStreamStart = atom<Date>(new Date());
  onStreamEnd = atom<Date>(new Date());
  onStartPreview = atom<Date>(new Date());
  onTerminalOutput = atom<string>('');

  private constructor() {
    this.onStreamEnd.listen(() => {
      this.saveProject();
    });
    this.hasWebcontainerSession.listen(() => {
      this._listenForPorts();
      this.listenForTerminalOutput();
    });
  }

  private _listenForPorts() {
    const url = `${this.sessionData.host}/process/listen/${this.getSlug()}`;

    try {
      const eventSource = new EventSource(url, { withCredentials: true });

      eventSource.onmessage = (event) => {
        if (event.data !== 'keep-alive') {
          const ports = JSON.parse(event.data);

          this.setPorts(ports);
        }
      };
    } catch {}
  }

  listenForTerminalOutput() {
    try {
      const url = `${this.sessionData.host}${this.sessionData.webcontainerApiPath}/terminal/output/${this.getSlug()}`;

      const eventSource = new EventSource(url, { withCredentials: true });

      eventSource.onmessage = (event) => {
        this.onTerminalOutput.set(event.data);
      };
    } catch {}
  }

  static getInstance(): SessionStore {
    if (!SessionStore._instance) {
      SessionStore._instance = new SessionStore();
    }

    return SessionStore._instance;
  }

  getSlug() {
    return workbenchStore.getSlug();
  }

  async restoreProject(commitHash: string) {
    return fetch(`${this.sessionData.host}/user-projects/code/${this.getSlug()}/${commitHash}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
    }).then((x) => x.json<ProjectCommits[]>());
  }

  async saveProject() {
    return fetch(`${this.sessionData.host}/user-projects/code/${this.getSlug()}/save`, { credentials: 'include' }).then(
      (x) => x.json<ProjectCommits[]>(),
    );
  }

  async runTask({ command, isStartTask }: { command: string; isStartTask?: boolean }) {
    try {
      const response = await this.extensionApiFetch(
        `/terminal/run/${this.getSlug()}?command=${command}&isStartTask=${isStartTask}`,
      );

      return response.ok;
    } catch (err) {
      console.error('Error running task:', err);
      return false;
    }
  }

  async openFile({ filePath }: { filePath: string }) {
    const response = await this.extensionApiFetch(`/ai/file/open/${this.getSlug()}/${filePath}`);

    return response.ok;
  }

  async getFileContent({ filePath }: { filePath: string }) {
    const encodedPath = encodeURIComponent(filePath);

    const response = await fetch(`${this.sessionData.host}/file/${this.getSlug()}/${encodedPath}`, {
      credentials: 'include',
    });

    if (response.ok) {
      return await response.text();
    }

    return '';
  }

  async getSessionData() {
    if (this.sessionData) {
      return this.sessionData;
    }

    try {
      const response = await backendApiFetch(`/webcontainers/get-session?slug=${this.getSlug()}`);
      const data = await response.json<SessionData>();

      this.sessionData = data;
      this.hasWebcontainerSession.set(true);

      return data;
    } catch (err: any) {
      console.error(err.message || 'Failed to fetch session data');
      return null as unknown as SessionData;
    }
  }

  previewUrl(port: string) {
    const portPathTemplate = this.sessionData!.portPathTemplate;
    return `${portPathTemplate.replace('PORT', port)}`;
  }

  getAvailablePorts() {
    return this.availablePorts.get();
  }

  async fetchPreviewPorts(): Promise<string[]> {
    const slug = this.getSlug();

    if (this.sessionData == null) {
      await this.getSessionData();
    }

    const response = await fetch(`${this.sessionData.host}/process/details/${slug}`, {
      credentials: 'include',
    });

    const ports = await response.json();
    this.setPorts(ports);

    return ports;
  }

  setPorts = (ports: string[]) => {
    const newPorts = ports.map(Number);

    if (sum(newPorts) != sum(this.availablePorts.get()) || newPorts.length != this.availablePorts.get().length) {
      this.availablePorts.set(newPorts);
    }
  };

  async extensionApiFetch(
    path: string,
    options: RequestInit = {},
    maxRetries = 5,
    retryDelay = 1000,
  ): Promise<Response> {
    await this.getSessionData();

    const EXTENSION_URL = `${this.sessionData?.host}${this.sessionData.webcontainerApiPath}`;

    const headers: Record<string, string> = {
      ...(options.headers as Record<string, string>),
    };

    if (LOCAL_DEV_API && this.sessionData?.hashedPassword) {
      headers.Cookie = `user-server-session=${this.sessionData.hashedPassword}`;
    }

    let attempt = 0;

    while (attempt < maxRetries) {
      try {
        const response = await fetch(`${EXTENSION_URL}${path}`, {
          body: options.body,
          method: options.method,
          signal: options.signal,
          headers,
          credentials: 'include',
        });

        if (!response.ok && response.status >= 500) {
          throw new Error(`Temporary error: ${response.status}`);
        }

        return response;
      } catch (error) {
        attempt++;

        if (attempt >= maxRetries) {
          throw error;
        }

        // Optional: Log the retry attempt
        console.warn(`Fetch attempt ${attempt} failed. Retrying in ${retryDelay}ms...`);

        await new Promise((resolve) => setTimeout(resolve, retryDelay));
      }
    }
    throw new Error('Failed to fetch after max retries');
  }

  async abortStream() {
    return await fetch(`${this.sessionData.host}/ai/abort/${this.getSlug()}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
    });
  }

  async deploy() {
    try {
      const projectSlug = this.getSlug();
      const response = await fetch(`${this.sessionData.host}/cloud/app/${projectSlug}/deploy`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
      });

      const data = await response.json();

      setAppData(projectSlug, data);

      return data;
    } catch (e) {
      console.error('Error deploying project:', e instanceof Error ? e.message : String(e));
      alert('Failed to deploy project. Please try again later.');

      return null;
    }
  }
}

export const sessionStore = SessionStore.getInstance();

export default SessionStore;
