import { clearCloudStore } from '~/ai/lib/stores/cloud/cloud-storage';
import { UserSession } from '~/types/user';

const BASE_URL = import.meta.env.CUSTOM_BACKEND_API;

// to be used for backend requests
export function getUserHeader<PERSON>ey() {
  return 'user-token';
}

export function getCookieDomain(): string {
  const hostname = window.location.hostname;

  if (hostname === 'localhost') {
    return '';
  }

  const domainName = import.meta.env.CUSTOM_DOMAIN_NAME || 'biela.dev';

  return '.' + domainName;
}

export function deleteCookie(name: string): void {
  const domain = getCookieDomain();
  let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax`;

  if (domain) {
    cookieString += `; domain=${domain}`;
  }

  document.cookie = cookieString;
}

export async function signUp(
  formData: {
    firstName: string;
    lastName: string;
    username: string;
    email: string;
    phoneNumber: string;
    password: string;
    referral?: string;
    acceptTerms: boolean;
  },
  turnstileToken: string,
) {
  return await fetch(`${BASE_URL}/user/register`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      firstName: formData.firstName,
      lastName: formData.lastName,
      username: formData.username,
      email: formData.email,
      phoneNumber: formData.phoneNumber,
      password: formData.password,
      referral: formData.referral,
      acceptTerms: formData.acceptTerms,
      turnstileToken,
    }),
  });
}

export async function loginUser(formData: { username: string; password: string }, turnstileToken: string) {
  const body = formData.username.includes('@')
    ? JSON.stringify({
        email: formData.username,
        password: formData.password,
        turnstileToken,
      })
    : JSON.stringify({
        username: formData.username,
        password: formData.password,
        turnstileToken,
      });

  const response = await fetch(`${BASE_URL}/user/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: body,
    credentials: 'include',
  });

  const data = await response.json();

  if (!response.ok) {
    throw new Error((data as { message: string }).message || 'Login failed');
  }

  return data as UserSession;
}

export async function resendVerificationEmail(payload: { email?: string; username?: string }) {
  const requestBody: { email?: string; username?: string } = {};

  if (payload.email?.trim()) {
    requestBody.email = payload.email.trim();
  }

  if (payload.username?.trim()) {
    requestBody.username = payload.username.trim();
  }
  const response = await fetch(`${BASE_URL}/user/resend-confirmation`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(requestBody),
    credentials: 'include',
  });

  if (!response.ok) {
    const data: { message?: string } = await response.json();
    throw new Error(data?.message || 'Failed to resend verification email');
  }

  return true;
}

export async function confirmUserEmail(token: string, setUserCallback?: (user: UserSession) => void) {
  const response = await fetch(`${BASE_URL}/user/confirm-email?token=${token}`, {
    method: 'POST',
    credentials: 'include',
  });

  const data = (await response.json()) as UserSession;

  if (!response.ok) {
    throw new Error('Failed to confirm email.');
  }

  if (setUserCallback) {
    setUserCallback(data);
  }

  return data;
}

function removeAllCommitsKeys() {
  for (let i = localStorage.length - 1; i >= 0; i--) {
    const key = localStorage.key(i);

    if (key && key.startsWith('commits-')) {
      localStorage.removeItem(key);
    }
  }
}

export async function logoutUser() {
  try {
    await fetch(`${BASE_URL}/user/logout`, {
      method: 'GET',
      credentials: 'include',
    });

    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
    localStorage.removeItem('user_time_tracking');

    // clear cache for other modules here
    clearCloudStore();

    // Remove all commits-* keys
    removeAllCommitsKeys();
  } catch (err) {
    console.error('Failed to logout on the backend:', err);
  }
}

export async function forgetPassword(formData: { email: string }, turnstileToken: string) {
  return await fetch(`${BASE_URL}/user/forgot-password?email=${formData.email}&turnstileToken=${turnstileToken}`, {
    method: 'POST',
  });
}

export async function confirmPassword({ token, password }: { token: string; password: string }) {
  try {
    const response = await fetch(`${BASE_URL}/user/confirm-forgot-password`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ token, password }),
    });

    // Check if the response is ok
    if (!response.ok) {
      const errorData = (await response.json()) as { message: string };
      throw new Error(errorData.message || 'Password update failed');
    }

    return response;
  } catch (error) {
    throw new Error((error as { message: string }).message || 'Something went wrong');
  }
}

export async function handleGitHubLogin(code: string) {
  try {
    const referral = localStorage.getItem('referral') || undefined;

    let url = `${BASE_URL}/user/github/${code}`;
    if (referral) {
      url += `?referral=${encodeURIComponent(referral)}`;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
    });
    const jsonRes = await response.json();

    if (!response.ok) {
      throw new Error((jsonRes as { message: string }).message || 'Failed to authenticate with Github');
    }

    return jsonRes as UserSession;
  } catch (error) {
    throw new Error((error as { message: string })?.message || 'Something went wrong');
  }
}

export async function handlePaypalLogin(code: string) {
  try {
    const response = await fetch(`${BASE_URL}/user/paypal?code=${code}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: "include"
    });
    const jsonRes = await response.json();
    if (!response.ok) {
      throw new Error((jsonRes as { message: string }).message || 'Failed to authenticate with Paypal');
    }

    return jsonRes as UserSession;
  } catch (error) {
    throw new Error((error as { message: string })?.message || 'Something went wrong');
  }
}

export function handlePaypalAuth() {
  //In Paypal dashboard settings, it need to set a redirectUrl, same as one below. But it doesn`t allow localhost. Must run app in vite --host mode to use ip instead of host

  //Request to get token for biela_api might fail when you use ip from vite --host as it is not found in biela_api allowed hosts list
  //In that case add ip to biela_ide .env ORIGIN variable  ex:http://***********:5173
  const redirectUri = import.meta.env.CUSTOM_PAYPAL_REDIRECT_URI ?? encodeURIComponent(`${window.location.origin}/paypal`);
  const scope = 'openid profile email';
  window.location.href = `https://www.sandbox.paypal.com/signin/authorize?response_type=code&client_id=${import.meta.env.CUSTOM_PAYPAL_CLIENT_ID}&scope=${scope}&redirect_uri=${redirectUri}`
}

export function handleGitHubAuth(referral?: string) {
  const scope = 'repo:status repo_deployment user:email';
  const redirectUri = encodeURIComponent(`${window.location.origin}/github`);

  let authUrl = `https://github.com/login/oauth/authorize?client_id=${import.meta.env.CUSTOM_GITHUB_CLIENT_ID}&redirect_uri=${redirectUri}&scope=${encodeURIComponent(scope)}`;

  if (referral) {
    authUrl += `&referral=${encodeURIComponent(referral)}`;
  }

  window.location.href = authUrl;
}

export async function handleGoogleLogin(code: string) {
  try {
    const referral = localStorage.getItem('referral') || undefined;

    const url = new URL(`${BASE_URL}/user/google-auth-callback?code=${code}`);
    if (referral) {
      url.searchParams.append('referral', referral);
    }

    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: { 'Content-type': 'application/json' },
      credentials: "include"
    });

    const jsonRes = await response.json();

    if (!response.ok) {
      throw new Error((jsonRes as { message: string }).message || 'Failed to authenticate with Google');
    }

    return jsonRes as UserSession;
  } catch (error) {
    throw new Error((error as { message: string })?.message || 'Something went wrong');
  }
}

export function handleGoogleAuth(baseState: string, referral?: string) {
  const scope = 'email';
  const redirectUri = encodeURIComponent(`${window.location.origin}/google`);

  const statePayload = {
    state: baseState,
    referral: referral || null,
  };

  const encodedState = btoa(JSON.stringify(statePayload));

  const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?scope=${encodeURIComponent(
    scope
  )}&access_type=online&include_granted_scopes=true&response_type=code&state=${encodedState}&redirect_uri=${redirectUri}&client_id=${
    import.meta.env.CUSTOM_GOOGLE_CLIENT_ID
  }`;

  window.location.href = authUrl;
}

export async function doRefresh(refreshToken: string) {
  const res = await fetch(`${BASE_URL}/user/refresh-token`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ refreshToken }),
    credentials: "include"
  });

  if (!res.ok) {
    throw new Error('Failed to refresh token');
  }

  return (await res.json()) as UserSession;
}

export async function getUserByToken(token: string) {
  const res = await fetch(`${BASE_URL}/user/authenticate-token`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ token }),
  });

  if (!res.ok) {
    throw new Error('Failed to refresh token');
  }

  return (await res.json()) as UserSession;
}
