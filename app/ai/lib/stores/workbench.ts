import { atom, map, type MapStore, type WritableAtom } from 'nanostores';
import { ActionRunner } from '~/ai/lib/runtime/action-runner';
import type { ActionCallbackData, ArtifactCallbackData } from '~/ai/lib/runtime/message-parser';
import { unreachable } from '~/utils/unreachable';
import { chatId, chatTimestamp, description } from '~/ai/lib/persistence';
import { createSampler } from '~/utils/sampler';
import type { ActionAlert } from '~/types/actions';
import { UserStore } from '~/ai/lib/stores/user/userStore';


export interface ArtifactState {
  id: string;
  title: string;
  type?: string;
  closed: boolean;
  runner: ActionRunner;
}

export type ArtifactUpdateState = Pick<ArtifactState, 'title' | 'closed'>;

type Artifacts = MapStore<Record<string, ArtifactState>>;

export type WorkbenchViewType = 'code' | 'preview' | 'settings' | 'content-studio';

export class WorkbenchStore {
  #reloadedMessages = new Set<string>();

  artifacts: Artifacts = import.meta.hot?.data.artifacts ?? map({});

  showWorkbench: WritableAtom<boolean> = import.meta.hot?.data.showWorkbench ?? atom(true);
  currentView: WritableAtom<WorkbenchViewType> = import.meta.hot?.data.currentView ?? atom('code');
  unsavedFiles: WritableAtom<Set<string>> = import.meta.hot?.data.unsavedFiles ?? atom(new Set<string>());
  actionAlert: WritableAtom<ActionAlert | undefined> = import.meta.hot?.data.unsavedFiles ?? atom<ActionAlert | undefined>(undefined);
  modifiedFiles = new Set<string>();
  artifactIdList: string[] = [];
  #globalExecutionQueue = Promise.resolve();

  #lastGeneratedFilePath?: string;
  userSelectedFileManually = false;

  constructor() {
    if (import.meta.hot) {
      import.meta.hot.data.artifacts = this.artifacts;
      import.meta.hot.data.unsavedFiles = this.unsavedFiles;
      import.meta.hot.data.showWorkbench = this.showWorkbench;
      import.meta.hot.data.currentView = this.currentView;
      import.meta.hot.data.actionAlert = this.actionAlert;
    }
  }

  addToExecutionQueue(callback: () => Promise<void>) {
    this.#globalExecutionQueue = this.#globalExecutionQueue.then(() => callback());
  }

 

  get firstArtifact(): ArtifactState | undefined {
    return this.#getArtifact(this.artifactIdList[0]);
  }

 

  get alert() {
    return this.actionAlert;
  }



  clearAlert() {
    this.actionAlert.set(undefined);
  }

  

  setShowWorkbench(show: boolean) {
    this.showWorkbench.set(show);
  }

 

  

  abortAllActions() {
    // TODO: what do we wanna do and how do we wanna recover from this?
  }

  setReloadedMessages(messages: string[]) {
    this.#reloadedMessages = new Set(messages);
  }

  addArtifact({ messageId, title, id, type }: ArtifactCallbackData) {
    const artifact = this.#getArtifact(messageId);

    if (artifact) {
      return;
    }

    if (!this.artifactIdList.includes(messageId)) {
      this.artifactIdList.push(messageId);
    }

    this.artifacts.setKey(messageId, {
      id,
      title,
      closed: false,
      type,
      runner: new ActionRunner(
        (alert) => {
          if (this.#reloadedMessages.has(messageId)) {
            return;
          }
          this.actionAlert.set(alert);
        },
      ),
    });
  }

  updateArtifact({ messageId }: ArtifactCallbackData, state: Partial<ArtifactUpdateState>) {
    const artifact = this.#getArtifact(messageId);

    if (!artifact) {
      return;
    }

    this.artifacts.setKey(messageId, { ...artifact, ...state });
  }

  addAction(data: ActionCallbackData) {
    this.addToExecutionQueue(() => this._addAction(data));
  }

  async _addAction(data: ActionCallbackData) {
    const { messageId } = data;

    const artifact = this.#getArtifact(messageId);

    if (!artifact) {
      unreachable('Artifact not found');
    }

    return artifact.runner.addAction(data);
  }

  runAction(data: ActionCallbackData, isStreaming: boolean = false) {
    if (isStreaming) {
      this.actionStreamSampler(data, isStreaming);
    } else {
      this.addToExecutionQueue(() => this._runAction(data, isStreaming));
    }
  }

  async _runAction(data: ActionCallbackData, isStreaming: boolean = false) {
    const { messageId } = data;

    const artifact = this.#getArtifact(messageId);

    if (!artifact) {
      unreachable('Artifact not found');
    }

    const action = artifact.runner.actions.get()[data.actionId];

    if (!action || action.executed) {
      return;
    }
   
    await artifact.runner.runAction(data);

    // if (data.action.type === 'start' && this.currentView.value !== 'preview')
    //   this.currentView.set('preview');

  }

  actionStreamSampler = createSampler(async (data: ActionCallbackData, isStreaming: boolean = false) => {
    return await this._runAction(data, isStreaming);
  }, 100); // TODO: remove this magic number to have it configurable

  #getArtifact(id: string) {
    const artifacts = this.artifacts.get();
    return artifacts[id];
  }



  getChatMetadata() {
    const projectName = (description.value ?? 'project').toLocaleLowerCase().split(' ').join('_');
    return {
      chatId: chatId.value,
      chatTimestamp: chatTimestamp.value,
      description: description.value,
      projectName,
    };
  }

  getSlug() {
    const metadata = this.getChatMetadata();

    if (!metadata.chatId) {
      throw new Error('Chat ID is not set');
    }

    const userStore = UserStore.getInstance();
    const user = userStore.getUser();

    const uniqueProjectId = `${metadata.chatId}-${user?.id}`;

    return uniqueProjectId.toLowerCase();
  }

}

export const workbenchStore = new WorkbenchStore();
