.BaseChat {
  &[data-chat-visible='false'] {
    --workbench-inner-width: 100%;
    --workbench-left: 0;

    .Chat {
      --at-apply: biela-ease-cubic-bezier;
      transition-property: transform, opacity;
      transition-duration: 0.3s;
      will-change: transform, opacity;
      transform: translateX(-50%);
      opacity: 0;
    }
  }
}

@media screen and (max-width: 800px) {
  .BaseChat {
    &[data-chat-visible='true'] {
      --workbench-left: calc(100vw - 50px);
    }
  }
}

.chatborder {
  :global(html[data-theme='light']) & {
    border: 1px solid #e9e9e9;
    background: #fff;
    box-shadow: 0px 32px var(--blurr-blurr-lg, 36px) 0px rgba(0, 0, 0, 0.06);
  }

  :global(html[data-theme='dark']) & {
    backdrop-filter: blur(15px);
    border: 1px solid #2e2e2e;
    background-repeat: no-repeat;
    background-position: 100% 0%;
    background-size: contain;
    background-image: url('/app/assets/icons/border-dark.svg'),
    linear-gradient(0deg, rgba(0, 0, 0, 0.05) 0%, rgba(31, 31, 31, 0.8) 100%);
  }
}

.Chat {
  opacity: 1;
  width: 100%;
  flex-shrink: 0;
  position: relative;

  @media (min-width: 1024px) {
    width: min(500px, 100%);
  }
}

.textWeight {
  font-family: 'Manrope', sans-serif;
  font-weight: 300;
}

.RextonFont {
  font-family: 'Rexton', sans-serif;
}

.PromptEffectContainer {
  --prompt-container-offset: 50px;
  --prompt-line-stroke-width: 1px;
  position: absolute;
  pointer-events: none;
  inset: calc(var(--prompt-container-offset) / -2);
  width: calc(100% + var(--prompt-container-offset));
  height: calc(100% + var(--prompt-container-offset));
}


.PromptEffectLine {
  width: calc(100% - var(--prompt-container-offset) + var(--prompt-line-stroke-width));
  height: calc(100% - var(--prompt-container-offset) + var(--prompt-line-stroke-width));
  x: calc(var(--prompt-container-offset) / 2 - var(--prompt-line-stroke-width) / 2);
  y: calc(var(--prompt-container-offset) / 2 - var(--prompt-line-stroke-width) / 2);
  rx: calc(8px - var(--prompt-line-stroke-width));
  fill: transparent;
  stroke-width: var(--prompt-line-stroke-width);
  stroke: url(#line-gradient);
  stroke-dasharray: 35px 65px;
  stroke-dashoffset: 10;
}

.PromptShine {
  fill: url(#shine-gradient);
  mix-blend-mode: overlay;
}

.wrapperParent {
  position: relative;
}


.containerUnitTesting {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}

.wrapper {
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #4ADE80;
  background: rgba(255, 255, 255, 0.04);
}

.wrapper:after {
  content: '';
  position: absolute;
  border-radius: inherit;
  bottom: -1px;
  width: calc(100% + 2px);
  height: calc(100% + 2px);
  left: -1px;
  background: linear-gradient(90deg, rgba(40, 255, 122, 0) 0%, #171717 96%);
  z-index: 1;
}
.errorWrapper {
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #FFFFFF1a;
}

.errorWrapperParent {
  position: relative;
}

.chatSuggestionPrimaryButton {
  border-radius: 8px;
  border: 1px solid rgba(40, 255, 122, 0.50);
  background: linear-gradient(180deg, rgba(40, 255, 122, 0) 10%, rgba(40, 255, 122, 0.00) 100%);
  backdrop-filter: blur(8px);
}

.chatSuggestionSecondaryButton:after,
.chatSuggestionPrimaryButton:after {
  content: '';
  position: absolute;
  border-radius: inherit;
  bottom: -1px;
  width: calc(100% + 2px);
  height: calc(100% + 2px);
  left: -1px;
  background: linear-gradient(180deg, rgba(40, 255, 122, 0) 0%, rgba(23, 23, 23, 0.7) 96%);
  z-index: 1;
}

.chatSuggestionSecondaryButton {
  border-radius: 8px;
  border: 1px solid rgba(241, 2, 1, 0.50);
  background: linear-gradient(180deg, rgba(241, 2, 1, 0.25) 0%, rgba(241, 2, 1, 0.00) 100%);
  backdrop-filter: blur(8px);
}

.container-textarea.dragging::after {
  opacity: 0 !important;
  background: none !important;
  transition: opacity .15s ease;
}

.overlayMessage {
  opacity: 0;
  transform: scale(0.95);
  transition: opacity 200ms ease, transform 200ms ease;
  font-family: 'Manrope', sans-serif;

}

.dragActive .overlayMessage {
  opacity: 1;
  transform: scale(1);
  font-family: 'Manrope', sans-serif;
}



