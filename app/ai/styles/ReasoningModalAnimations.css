@keyframes pulse-strong {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.4; transform: scale(0.8); }
    100% { opacity: 1; transform: scale(1); }
}

.animate-pulse-strong {
    animation: pulse-strong 1s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.delay-300 {
    animation-delay: 300ms;
}

.delay-600 {
    animation-delay: 600ms;
}

.shadow-inner-custom {
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}