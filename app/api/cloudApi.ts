import { backendApiFetch } from '~/ai/lib/backend-api';

interface Domain {
    id: string;
    name: string;
    isDefault: boolean;
    status: string;
    lastVerified: string;
    type: string;
  }

  interface DomainsResponse {
    domains: Domain[];
  }
export async function addDomain(
  projectSlug: string,
  domainData: {
    name: string;
    redirect?: string;
    redirectStatusCode?: number;
  },
) {
  try {
    const response = await backendApiFetch(`/cloud/app/${projectSlug}/dns`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(domainData),
    });

    if (!response.ok) {
      throw new Error(`Failed to add domain. Status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error adding domain:', error);
    throw error;
  }
}

export async function getDomains(
    projectSlug: string,
    verify?: boolean,
  ): Promise<DomainsResponse> {
    try {
      let url = `/cloud/app/${projectSlug}/dns`;

      if (verify !== undefined) {
        const verifyParam = `verify=${verify}`;
        url += `?${verifyParam}`;
      }

      const response = await backendApiFetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch domains. Status: ${response.status}`);
      }

      const data = await response.json();
      return data as DomainsResponse;
    } catch (error) {
      console.error('Error fetching domains:', error);
      throw error;
    }
  }

export async function deleteDomain(projectSlug: string, domainName: string) {
  try {
    const response = await backendApiFetch(`/cloud/app/${projectSlug}/dns/${domainName}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to delete domain. Status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error deleting domain:', error);
    throw error;
  }
}
