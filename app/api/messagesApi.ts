import { backendApiFetch } from '~/ai/lib/backend-api';

export async function deleteProjectMessages(projectSlug: string, createdAts: Date[]) {
  try {
    const createdAtStrings = createdAts.map((date) => (date instanceof Date ? date.toISOString() : date));

    const response = await backendApiFetch(`/user-projects/${projectSlug}/messages`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ createdAts: createdAtStrings }),
    });

    if (!response.ok) {
      throw new Error(`Failed to delete messages. Status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error deleting messages:', error);
    throw error;
  }
}
