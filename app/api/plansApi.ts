import { backendApiFetch } from '~/ai/lib/backend-api';

export async function fetchPlans() {
  try {
    const response = await backendApiFetch('/plans', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch plans. Status: ${response.status}`);
    }

    const data = await response.json();

    return data;
  } catch (error) {
    console.error('Error fetching plans:', error);
    throw error;
  }
}