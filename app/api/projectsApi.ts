import { backendApiFetch } from '~/ai/lib/backend-api';

export type Estimations = {
  breakdown: Record<string, number>;
  confidenceScore: number;
  countryForEstimation: string;
  createdAt: string;
  estimatedCostTraditional: number;
  estimatedNumberOfDevelopers: number;
  estimatedTimeTraditional: number;
  featureCount: number;
  keyTechnologies: string[];
  maintenanceCostPercentage: number;
  projectComplexity: string;
  projectType: string;
  rangeOfUncertainty: string;
  recommendedDeveloperLevel: string;
  timeToMarket: string;
  uniqueComponentCount: number;
  updatedAt: string;
};

export type Project = {
  id: string | undefined;
  name: string;
  projectSlug: string;
  _id: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  projectName: string;
  isDeployed: boolean;
  estimatedCostTraditional: number;
  estimatedTimeTraditional: number;
  estimatedNumberOfDevelopers: number;
  estimations?: Estimations;
  timeSpent: number;
  timeSpentAi: number;
  totalTokens: number;
  totalTokensCost: number;
  cloudAppUrl?: string;
  screenshotUrl?: string;
  screenshotUrl2?: string;
};

export type FetchProjectsResponse = {
  items: Project[];
  totalPages: number;
  totalItems: number;
};

export const fetchProjects = async (
  id: string | undefined,
  page = 0,
  limit = 4,
  sortBy = 'updatedAt',
  order = 'DESC',
  projectName = '',
): Promise<FetchProjectsResponse> => {
  try {
    let response;
    const query = `/user-projects?page=${page}&limit=${limit}&sortBy=${sortBy}&order=${order}${projectName ? `&projectName=${projectName}` : ''}`;
    if (id) {
      response = await backendApiFetch(`${query}&folderId=${id}`, {
        method: 'GET',
      });
    } else {
      response = await backendApiFetch(query, {
        method: 'GET',
      });
    }

    const data: any = await response.json();

    return { items: data.items || [], totalPages: data.totalPages, totalItems: data.total };
  } catch (error) {
    console.error('Error fetching projects data:', error);
    throw error;
  }
};

export const createProject = async (projectSlug: string, projectName: string): Promise<any> => {
  try {
    const response = await backendApiFetch(`/user-projects/${projectSlug}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ projectName }),
    });

    if (!response.ok) {
      throw new Error(`Failed to create project. Status: ${response.status}`);
    }

    const data = await response.json();

    return data;
  } catch (error) {
    console.error('Error creating project:', error);
    throw error;
  }
};

export const getProject = async (projectSlug: string): Promise<any> => {
  try {
    const response = await backendApiFetch(`/user-projects/${projectSlug}`, {
      method: 'GET',
    });

    if (!response.ok) {
      return null;
    }

    const contentType = response.headers.get('content-type');

    if (contentType && contentType.includes('application/json')) {
      const text = await response.text();

      if (text && text.trim()) {
        return JSON.parse(text);
      }
    }

    return null;
  } catch (error) {
    console.error('Error getting project:', error);
    return null;
  }
};

export const createFolder = async (folderName: string): Promise<any> => {
  try {
    const response = await backendApiFetch('/user-folders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name: folderName }),
    });

    if (!response.ok) {
      throw new Error(`Failed to create folder. Status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error creating folder:', error);
    throw error;
  }
};

export const getFolders = async (): Promise<any> => {
  try {
    const response = await backendApiFetch('/user-folders', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to get folders. Status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting folders:', error);
    throw error;
  }
};

export const fetchContestProjects = async (): Promise<any[]> => {
  try {
    const response = await backendApiFetch('/contest/user/projects', {
      method: 'GET',
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch contest projects. Status: ${response.status}`);
    }

    const data = await response.json();

    return {
      projects: data.projects || [],
      qualification: data.qualification || {},
    };
  } catch (error) {
    console.error('Error fetching contest projects:', error);
    throw error;
  }
};

export const postProjectToContest = async (projectSlug: string): Promise<any> => {
  try {
    const response = await backendApiFetch('/contest/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ projectSlug: projectSlug }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      const errorMessage = errorData.message || `Failed to post project to contest. Status: ${response.status}`;
      throw new Error(errorMessage);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error to post project to contest:', error);
    throw error;
  }
};

export const updateContestProject = async (projectSlug: string): Promise<void> => {
  try {
    const response = await backendApiFetch('/contest/project/update', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ projectSlug }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      const errorMessage = errorData.message || `Failed to update contest project. Status: ${response.status}`;
      throw new Error(errorMessage);
    }
  } catch (error) {
    console.error('Error updating contest project:', error);
    throw error;
  }
};

export const deleteContestProject = async (projectSlug: string): Promise<void> => {
  try {
    const response = await backendApiFetch('/contest/project', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ projectSlug }),
    });

    if (!response.ok) {
      throw new Error(`Failed to delete contest project. Status: ${response.status}`);
    }
  } catch (error) {
    console.error('Error deleting contest project:', error);
    throw error;
  }
};

export const deleteFolder = async (folderId: string): Promise<any> => {
  try {
    const response = await backendApiFetch(`/user-folders/${folderId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to delete folder. Status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error delete folder:', error);
    throw error;
  }
};

export const deleteProjectFolder = async (projectId: string, folderId: string): Promise<any> => {
  try {
    const response = await backendApiFetch(`/user-folders/${folderId}/projects`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ projectId: projectId }),
    });

    if (!response.ok) {
      throw new Error(`Failed to delete folder. Status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error delete folder:', error);
    throw error;
  }
};

export const deleteProject = async (projectSlug: string): Promise<any> => {
  try {
    const response = await backendApiFetch(`/user-projects/${projectSlug}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to delete project. Status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error deleting project:', error);
    throw error;
  }
};

export const updateFolderName = async (folderId: string, newFolderName: string): Promise<any> => {
  try {
    const response = await backendApiFetch(`/user-folders/${folderId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name: newFolderName }),
    });

    if (!response.ok) {
      throw new Error(`Failed to edit folder. Status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error edit folder:', error);
    throw error;
  }
};
export const changeProjectFolder = async (folderId: string, projectId: string): Promise<any> => {
  try {
    const response = await backendApiFetch(`/user-folders/${folderId}/projects`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ projectId: projectId }),
    });

    if (!response.ok) {
      throw new Error(`Failed to edit folder. Status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error edit folder:', error);
    throw error;
  }
};

export const updateProjectName = async (projectSlug: string, newProjectName: string): Promise<any> => {
  try {
    const response = await backendApiFetch(`/user-projects/${projectSlug}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ projectName: newProjectName }),
    });

    if (!response.ok) {
      throw new Error(`Failed to update project name. Status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error updating project name:', error);
    throw error;
  }
};