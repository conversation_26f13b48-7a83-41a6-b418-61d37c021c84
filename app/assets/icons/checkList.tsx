import React from 'react';
import { CheckIcon } from 'lucide-react';

const CheckList = ({ width = '15', height = '14', color = '#e5e7eb', checked = false }) => {
  if (checked) {
    return (
      <CheckIcon className="w-4 h-4" />
    );
  } else {
    return (
      <svg width={width} height={height} viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clipPath="url(#clip0_27283_3955)">
          <path
            d="M11.5833 0H3.41667C2.64312 0 1.90125 0.307291 1.35427 0.854272C0.807291 1.40125 0.5 2.14312 0.5 2.91667L0.5 11.0833C0.5 11.4664 0.575442 11.8456 0.722018 12.1995C0.868594 12.5534 1.08343 12.8749 1.35427 13.1457C1.90125 13.6927 2.64312 14 3.41667 14H11.5833C11.9664 14 12.3456 13.9246 12.6995 13.778C13.0534 13.6314 13.3749 13.4166 13.6457 13.1457C13.9166 12.8749 14.1314 12.5534 14.278 12.1995C14.4246 11.8456 14.5 11.4664 14.5 11.0833V2.91667C14.5 2.53364 14.4246 2.15437 14.278 1.80051C14.1314 1.44664 13.9166 1.12511 13.6457 0.854272C13.3749 0.583434 13.0534 0.368594 12.6995 0.222018C12.3456 0.0754418 11.9664 0 11.5833 0ZM13.3333 11.0833C13.3333 11.5475 13.149 11.9926 12.8208 12.3208C12.4926 12.649 12.0475 12.8333 11.5833 12.8333H3.41667C2.95254 12.8333 2.50742 12.649 2.17923 12.3208C1.85104 11.9926 1.66667 11.5475 1.66667 11.0833V2.91667C1.66667 2.45254 1.85104 2.00742 2.17923 1.67923C2.50742 1.35104 2.95254 1.16667 3.41667 1.16667H11.5833C12.0475 1.16667 12.4926 1.35104 12.8208 1.67923C13.149 2.00742 13.3333 2.45254 13.3333 2.91667V11.0833Z"
            fill={color}
          />
        </g>
        <defs>
          <clipPath id="clip0_27283_3955">
            <rect width="14" height="14" fill="white" transform="translate(0.5)" />
          </clipPath>
        </defs>
      </svg>
    );
  }
};

export default CheckList;
