import React from 'react';

const FiSrCross = ({ width = '16', height = '17', color = 'white' }) => {
  return (
    <svg width={width} height={height} viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_27397_4618)">
        <path
          d="M8.94269 8.49585L15.8048 1.63373C16.0606 1.36891 16.0533 0.946844 15.7885 0.691058C15.5301 0.441522 15.1205 0.441522 14.8621 0.691058L7.99998 7.55318L1.13786 0.691058C0.873015 0.435272 0.450977 0.442616 0.195191 0.707433C-0.0543447 0.965782 -0.0543447 1.37538 0.195191 1.63373L7.05731 8.49585L0.195191 15.358C-0.0650636 15.6183 -0.0650636 16.0403 0.195191 16.3006C0.455539 16.5609 0.877547 16.5609 1.13786 16.3006L7.99998 9.43853L14.8621 16.3006C15.1225 16.5609 15.5445 16.5609 15.8048 16.3006C16.065 16.0403 16.065 15.6183 15.8048 15.358L8.94269 8.49585Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_27397_4618">
          <rect width="16" height="16" fill="white" transform="translate(0 0.5)" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default FiSrCross;
