import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import Wrapper from '~/components/common/Wrapper';
import {
  FaArrowRight,
  FaBolt,
  FaBook,
  FaCloud,
  FaCode,
  FaComments,
  FaMagic,
  FaPencilAlt,
  FaPlay,
  FaQuestionCircle,
  FaRocket,
  FaTools
} from 'react-icons/fa';

function GetStartedPage() {
  const { t } = useTranslation('translation');

  const features = [
    {
      icon: <FaBook className="text-blue-400" />,
      title: t('getStarted.features.docs.title', 'Developer Documentation'),
      description: t('getStarted.features.docs.description', 'Learn how to use Biela.dev with easy-to-follow guides, tips, and best practices. Perfect for beginners and experienced developers alike!'),
      cta: t('getStarted.features.docs.cta', 'Explore Developer Docs'),
      color: "from-blue-500/20 to-blue-600/20"
    },
    {
      icon: <FaComments className="text-purple-400" />,
      title: t('getStarted.features.support.title', 'Feedback and Support'),
      description: t('getStarted.features.support.description', 'Need help or have feedback? Contact support and help improve Biela.dev!'),
      cta: t('getStarted.features.support.cta', 'Send Feedback'),
      color: "from-purple-500/20 to-purple-600/20"
    },
    {
      icon: <FaRocket className="text-green-400" />,
      title: t('getStarted.features.platform.title', 'Platform Features'),
      description: t('getStarted.features.platform.description', 'Discover the powerful tools Biela.dev offers to help you create websites and apps effortlessly. Let AI do the coding for you!'),
      cta: t('getStarted.features.platform.cta', 'Explore Features'),
      color: "from-green-500/20 to-green-600/20"
    }
  ];

  const steps = [
    {
      icon: <FaCode />,
      title: t('getStarted.guide.steps.setup.title', 'Instantly Set Up Your Project'),
      description: t('getStarted.guide.steps.setup.description', 'Get your development environment ready in seconds')
    },
    {
      icon: <FaMagic />,
      title: t('getStarted.guide.steps.generate.title', 'Generate Full-Stack Code with AI'),
      description: t('getStarted.guide.steps.generate.description', 'Let AI write production-ready code for you')
    },
    {
      icon: <FaBolt />,
      title: t('getStarted.guide.steps.features.title', 'Instant Feature Generation'),
      description: t('getStarted.guide.steps.features.description', 'Add complex features with simple prompts')
    },
    {
      icon: <FaPencilAlt />,
      title: t('getStarted.guide.steps.editor.title', 'No-Code & Low-Code Editor'),
      description: t('getStarted.guide.steps.editor.description', 'Modify your app visually or through code')
    },
    {
      icon: <FaTools />,
      title: t('getStarted.guide.steps.optimize.title', 'Optimize & Test in Real-Time'),
      description: t('getStarted.guide.steps.optimize.description', 'Ensure your app performs perfectly')
    },
    {
      icon: <FaCloud />,
      title: t('getStarted.guide.steps.deploy.title', 'Deploy with One Click'),
      description: t('getStarted.guide.steps.deploy.description', 'Go live instantly with automated deployment')
    }
  ];

  const faqs = [
    {
      question: t('getStarted.faq.questions.what.question', 'What is Biela.dev?'),
      answer: t('getStarted.faq.questions.what.answer', 'Biela.dev is an AI-powered platform that helps you create websites and apps—even if you don\'t know how to code. It automates the entire development process, from writing code to deployment.')
    },
    {
      question: t('getStarted.faq.questions.experience.question', 'Do I need coding experience to use Biela.dev?'),
      answer: t('getStarted.faq.questions.experience.answer', 'No! Biela.dev is designed for both beginners and experienced developers. You can build with AI assistance or customize the generated code as needed.')
    },
    {
      question: t('getStarted.faq.questions.projects.question', 'What types of projects can I create?'),
      answer: t('getStarted.faq.questions.projects.answer', 'You can build websites, web apps, mobile apps, SaaS platforms, e-commerce stores, admin dashboards, and more.')
    },
    {
      question: t('getStarted.faq.questions.edit.question', 'Can I edit the code generated by Biela.dev?'),
      answer: t('getStarted.faq.questions.edit.answer', 'Yes! Biela.dev allows you to customize the AI-generated code or use the no-code')
    },
    {
      question: t('getStarted.faq.questions.deployment.question', 'How does deployment work?'),
      answer: t('getStarted.faq.questions.deployment.answer', 'With one click, Biela.dev deploys your project, making it live and ready to use. No need for manual server setup!')
    },
    {
      question: t('getStarted.faq.questions.pricing.question', 'Is Biela.dev free to use?'),
      answer: t('getStarted.faq.questions.pricing.answer', 'Biela.dev offers a free plan with basic features. For more advanced tools and resources, you can upgrade to a premium plan.')
    },
    {
      question: t('getStarted.faq.questions.integrations.question', 'Can I integrate third-party tools or databases?'),
      answer: t('getStarted.faq.questions.integrations.answer', 'Yes! Biela.dev supports integrations with popular databases (MongoDB, Firebase, Supabase) and third-party APIs.')
    },
    {
      question: t('getStarted.faq.questions.help.question', 'Where can I get help if I\'m stuck?'),
      answer: t('getStarted.faq.questions.help.answer', 'You can check out our Developer Documentation, Quick Start Guide, or reach out to Support through our help center.')
    }
  ];

  return (
    <Wrapper handleSendMessage={() => {}}>
      <div className="min-h-screen bg-[#0B0E14] text-white">
        <div className="container mx-auto px-8 py-12 max-w-[1600px] space-y-24 pt-30">
          {/* Hero Section */}
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="text-center space-y-6">
            <h1 className="text-4xl font-light">{t('getStarted.title', 'Explore How Biela.dev Works')}</h1>
            <p className="text-xl text-gray-400 font-light max-w-3xl mx-auto">{t('getStarted.description', 'Build Your App in Minutes with Biela.dev. Our AI automates the entire development process, from setup to deployment. Here\'s how our AI builds your app effortlessly!')}</p>
          </motion.div>

          {/* Main Features Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`bg-gradient-to-r ${feature.color} rounded-xl p-6 relative overflow-hidden flex flex-col h-full`}
              >
                <div className="absolute -bottom-8 -right-8 text-[200px] opacity-5">{feature.icon}</div>
                <div className="relative z-10 flex flex-col flex-grow">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="p-3 bg-black/20 rounded-xl">{feature.icon}</div>
                    <h3 className="text-xl font-light">{feature.title}</h3>
                  </div>
                  <p className="text-gray-300 font-light mb-6 flex-grow">{feature.description}</p>
                  <button className="flex items-center gap-2 bg-black/20 hover:bg-black/30 px-4 py-2 rounded-lg transition-colors mt-auto max-w-fit">
                    {feature.cta}
                    <FaArrowRight className="text-sm" />
                  </button>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Quick Start Video Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-purple-500/20 to-purple-600/20 rounded-xl p-8 relative overflow-hidden"
          >
            <div className="flex items-center gap-6">
              <div className="flex-1">
                <h2 className="text-2xl font-light mb-4">{t('getStarted.video.title', 'Quick Start Video')}</h2>
                <p className="text-gray-300 font-light mb-6">{t('getStarted.video.description', 'Watch how Biela.dev builds for you—effortless app & web creation!')}</p>
                <button className="flex items-center gap-2 bg-purple-500 hover:bg-purple-600 px-6 py-3 rounded-lg transition-colors">
                  <FaPlay />
                  <span className="font-light">{t('getStarted.video.cta', 'Watch Tutorial')}</span>
                </button>
              </div>
              <div className="w-[600px] h-[337.5px] bg-black/40 rounded-xl flex items-center justify-center">
                <FaPlay className="text-4xl text-white/50" />
              </div>
            </div>
          </motion.div>

          {/* Quick Start Guide */}
          <div className="space-y-8">
            <h2 className="text-2xl font-light text-center">{t('getStarted.guide.title', 'Quick Start Guide')}</h2>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {steps.map((step, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-[#1f293780] rounded-xl p-6"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="p-3 bg-green-500/20 rounded-xl text-green-400">{step.icon}</div>
                    <div>
                      <div className="text-sm text-gray-400 font-light">Step {index + 1}</div>
                      <h3 className="font-light">{step.title}</h3>
                    </div>
                  </div>
                  <p className="text-gray-400 font-light">{step.description}</p>
                </motion.div>
              ))}
            </div>
          </div>

          {/* FAQ Section */}
          <div className="space-y-8">
            <h2 className="text-2xl font-light text-center">{t('getStarted.faq.title', 'Frequently Asked Questions')}</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {faqs.map((faq, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-[#1f293780] rounded-xl p-6"
                >
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-purple-500/20 rounded-lg">
                      <FaQuestionCircle className="text-purple-400" />
                    </div>
                    <div>
                      <h3 className="font-light mb-2">{faq.question}</h3>
                      <p className="text-gray-400 font-light">{faq.answer}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* CTA Section */}
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="text-center space-y-6">
            <h2 className="text-2xl font-light">{t('getStarted.cta.title', 'Ready to Get Started?')}</h2>
            <p className="text-gray-400 font-light">{t('getStarted.cta.description', 'Create your first project with Biela.dev and experience the future of development.')}</p>
            <button className="bg-green-500 hover:bg-green-600 px-8 py-3 rounded-lg transition-colors inline-flex items-center gap-2">
              <FaRocket />
              <span className="font-light">{t('getStarted.cta.button', 'Create New Project')}</span>
            </button>
          </motion.div>
        </div>
      </div>
    </Wrapper>
  );
}

export default GetStartedPage;
