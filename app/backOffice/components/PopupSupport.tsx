import { FaSave, FaTimes } from 'react-icons/fa';
import { useRef } from 'react';

export function PopupSupport({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  const modalRef = useRef<HTMLDivElement>(null);

  const handleClickOutside = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/45" onClick={handleClickOutside}>
      <div ref={modalRef} className="bg-[#111727] text-white p-6 rounded-xl w-full max-w-md shadow-lg relative">

        <button
          onClick={onClose}
          className="absolute top-7 right-6 bg-[#111727]"
        >
          <FaTimes />
        </button>

        <h2 className="text-xl font-light flex border-b border-gray-600 pb-5 -mx-6 px-6">Support Contact</h2>

        <p className="mt-4 text-gray-400 flex">Your Problem</p>
        <textarea
          placeholder="Describe your problem"
          className="mt-2 w-full bg-[#202938] text-white px-4 py-2 rounded-lg border border-[#374151] focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none h-20"
        />

        <div className="flex justify-end space-x-2 border-t border-gray-600 -mx-6 px-6 pt-5 mt-4">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-[#202938] text-white rounded-lg hover:bg-gray-600 transition"
          >
            Cancel
          </button>
          <button className="px-4 py-2 bg-purple-600 text-white rounded-lg flex items-center gap-2 hover:bg-purple-500 transition">
            <FaSave />
            Send
          </button>
        </div>
      </div>
    </div>
  );
}
