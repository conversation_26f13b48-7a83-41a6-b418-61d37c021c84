import React from 'react';
import { Path, Svg } from '@react-pdf/renderer';

const BankIcon = () => (
  <Svg width="12" height="13" viewBox="0 0 12 13">
    <Path d="M1 4.78454C1 4.18645 1.24119 3.81991 1.74032 3.54214L3.79494 2.39872C4.87155 1.79957 5.40986 1.5 6 1.5C6.59014 1.5 7.12845 1.79957 8.20506 2.39872L10.2597 3.54214C10.7588 3.81991 11 4.18645 11 4.78454C11 4.94672 11 5.02781 10.9823 5.09447C10.8892 5.44472 10.5719 5.5 10.2654 5.5H1.73464C1.42813 5.5 1.11076 5.44472 1.01771 5.09447C1 5.02781 1 4.94672 1 4.78454Z" stroke="#FDFDFD" strokeWidth="0.58" />
    <Path d="M5.99795 3.99994H6.00244" stroke="#FDFDFD" strokeLinecap="round" strokeLinejoin="round" />
    <Path d="M2 5.5V9.75M4 5.5V9.75" stroke="#FDFDFD" strokeWidth="0.58" />
    <Path d="M8 5.49997V9.74997M10 5.49997V9.74997" stroke="#FDFDFD" strokeWidth="0.58" />
    <Path d="M9.5 9.75H2.5C1.67157 9.75 1 10.4216 1 11.25C1 11.3881 1.11193 11.5 1.25 11.5H10.75C10.8881 11.5 11 11.3881 11 11.25C11 10.4216 10.3284 9.75 9.5 9.75Z" stroke="#FDFDFD" strokeWidth="0.58" />
  </Svg>);

export { BankIcon };
