import { ClipPath, Defs, G, Path, Rect, Svg } from '@react-pdf/renderer';

const <PERSON><PERSON><PERSON><PERSON><PERSON> = () => (
  <Svg width="114" height="39" viewBox="0 0 114 39">
    <Defs>
      <ClipPath id="clip0">
        <Rect width="114" height="38.7477" fill="white"/>
      </ClipPath>
    </Defs>
    <G clipPath="url(#clip0)">
      <Path d="M45.922 14.2235V7.15992H46.3434V8.76464C46.3434 9.0276 46.5556 9.23988 46.8186 9.23988H51.7627C53.2232 9.23988 54.1294 10.2014 54.1294 11.7475C54.1294 13.2937 53.2232 14.2235 51.7627 14.2235H45.922ZM46.8186 9.66125C46.5556 9.66125 46.3434 9.87353 46.3434 10.1365V13.3269C46.3434 13.5899 46.5556 13.8022 46.8186 13.8022H51.7627C52.9254 13.8022 53.708 12.9816 53.708 11.7586C53.708 10.5357 52.908 9.66125 51.7627 9.66125H46.8186Z" fill="#16A349"/>
      <Path d="M56.3962 14.2235V9.22879H56.8176V14.2235H56.3962ZM56.3962 7.59239V7.15992H56.8176V7.59239H56.3962Z" fill="#16A349"/>
      <Path d="M61.4631 14.223C60.0026 14.223 59.0964 13.2741 59.0964 11.747C59.0964 10.2199 60.0026 9.2393 61.4631 9.2393H64.9371C66.3676 9.2393 67.2927 10.2246 67.2927 11.747V11.9466H60.1134C59.9582 11.9466 59.8125 12.0226 59.7237 12.1493C59.635 12.2761 59.6129 12.4392 59.6667 12.585C59.9455 13.3469 60.6172 13.8016 61.4631 13.8016H67.3038V14.223H61.4631ZM61.4631 9.66068C60.6156 9.66068 59.9614 10.1058 59.6683 10.882C59.6129 11.0278 59.6335 11.1925 59.7222 11.3208C59.8109 11.4492 59.9566 11.5252 60.1134 11.5252H66.2868C66.4325 11.5252 66.5688 11.4587 66.659 11.3446C66.7493 11.2305 66.7826 11.0816 66.7493 10.9407C66.5688 10.1739 65.8401 9.6591 64.9371 9.6591H61.4631V9.66068Z" fill="#16A349"/>
      <Path d="M70.0902 7.15992H69.6688V14.2235H70.0902V7.15992Z" fill="#16A349"/>
      <Path d="M74.5076 14.2243C73.5318 14.2243 72.369 13.9851 72.369 12.8477C72.369 11.7103 73.5318 11.5155 74.5076 11.5155H78.4378C78.9511 11.5155 80.155 11.5155 80.155 10.5824C80.155 9.64936 78.9954 9.64936 78.4378 9.64936H72.7175V9.22798H78.4378C79.8572 9.22798 80.5764 9.68421 80.5764 10.5824C80.5764 10.6173 80.5795 10.6521 80.5875 10.6838V14.2227H74.5076V14.2243ZM74.5076 11.9368C73.95 11.9368 72.7904 11.9368 72.7904 12.8699C72.7904 13.8029 73.9927 13.8029 74.5076 13.8029H79.6908C79.9538 13.8029 80.1661 13.5907 80.1661 13.3277V12.2489C80.1661 12.0984 80.0948 11.9574 79.9744 11.8671C79.892 11.8054 79.7922 11.7737 79.6908 11.7737C79.6449 11.7737 79.599 11.78 79.553 11.7943C79.2362 11.8909 78.8719 11.9368 78.4394 11.9368H74.5092H74.5076Z" fill="#16A349"/>
      <Path d="M83.2439 13.8019H82.8225V14.2233H83.2439V13.8019Z" fill="#16A349"/>
      <Path d="M87.6498 14.2235C86.1892 14.2235 85.2831 13.2746 85.2831 11.7475C85.2831 10.2205 86.1892 9.23988 87.6498 9.23988H92.5938C92.8568 9.23988 93.0691 9.0276 93.0691 8.76464V7.15992H93.4904V14.2235H87.6498ZM87.6387 9.66125C86.4997 9.66125 85.7045 10.5246 85.7045 11.7586C85.7045 12.9927 86.4823 13.8022 87.6387 13.8022H92.5827C92.8457 13.8022 93.058 13.5899 93.058 13.3269V10.1365C93.058 9.87353 92.8457 9.66125 92.5827 9.66125H87.6387Z" fill="#16A349"/>
      <Path d="M98.124 14.223C96.6634 14.223 95.7573 13.2741 95.7573 11.747C95.7573 10.2199 96.6634 9.2393 98.124 9.2393H101.598C103.028 9.2393 103.954 10.2246 103.954 11.747V11.9466H96.7743C96.6191 11.9466 96.4734 12.0226 96.3846 12.1493C96.2959 12.2761 96.2738 12.4392 96.3276 12.585C96.6064 13.3469 97.2781 13.8016 98.124 13.8016H103.965V14.223H98.124ZM98.124 9.66068C97.2765 9.66068 96.6223 10.1058 96.3292 10.882C96.2738 11.0278 96.2943 11.1925 96.3831 11.3208C96.4718 11.4492 96.6175 11.5252 96.7743 11.5252H102.948C103.093 11.5252 103.23 11.4587 103.32 11.3446C103.41 11.2305 103.444 11.0816 103.41 10.9407C103.23 10.1739 102.501 9.6591 101.598 9.6591H98.124V9.66068Z" fill="#16A349"/>
      <Path d="M108.824 14.2227L105.603 9.22798H106.086L108.995 13.6841C109.082 13.8188 109.232 13.8996 109.392 13.8996H109.394C109.556 13.8996 109.705 13.8156 109.792 13.6794L112.635 9.22798H113.131L109.947 14.2227H108.822H108.824Z" fill="#16A349"/>
      <Path d="M39.4297 27.5944C39.4265 27.6039 39.4233 27.615 39.4186 27.6245C39.3758 27.7227 38.3556 30.0656 35.7656 32.4529C34.2464 33.8533 32.4991 34.9748 30.576 35.7843C28.1744 36.795 25.4878 37.3209 22.5904 37.3463C22.4621 37.3463 22.356 37.2433 22.3544 37.115C22.3544 36.9867 22.4573 36.8805 22.5857 36.8789C28.9903 36.8219 33.1074 34.262 35.4329 32.1234C37.9564 29.8027 38.9798 27.4629 38.9893 27.4391C39.04 27.3203 39.1778 27.2665 39.295 27.3172C39.4043 27.3631 39.4582 27.4835 39.4281 27.5928L39.4297 27.5944Z" fill="#16A349"/>
      <Path d="M20.8598 36.9565C20.8313 37.0627 20.7299 37.1355 20.6159 37.126C19.0112 36.9977 17.4666 36.4258 17.4017 36.4021C17.2813 36.3561 17.2195 36.2231 17.2655 36.1011C17.3098 35.9807 17.4445 35.9189 17.5664 35.9649C17.5823 35.9712 19.1157 36.5383 20.6539 36.6619C20.7822 36.6714 20.8788 36.7838 20.8678 36.9137C20.8678 36.9296 20.8646 36.9438 20.8598 36.9581V36.9565Z" fill="#16A349"/>
      <Path d="M40.9174 23.8285C40.5689 25.0911 40.1316 26.0764 40.111 26.1223C40.0588 26.2396 39.921 26.2934 39.8037 26.2412C39.6865 26.1889 39.6326 26.0511 39.6849 25.9338C39.6913 25.9212 40.2568 24.6412 40.6053 23.1695C40.6338 23.0428 40.7605 22.9668 40.8857 22.9969C41.0108 23.027 41.0885 23.1521 41.0584 23.2773C41.014 23.4658 40.9665 23.6511 40.9158 23.8301L40.9174 23.8285Z" fill="#16A349"/>
      <Path d="M31.1087 2.71251C31.1024 2.73469 31.0929 2.75528 31.0802 2.77587C31.0121 2.88518 30.8679 2.91686 30.7586 2.84874C30.7491 2.84241 30.2438 2.53667 28.6929 2.04717C28.5693 2.00757 28.5028 1.87609 28.5408 1.75411C28.5804 1.63055 28.7103 1.56243 28.8339 1.60204C30.4703 2.11846 30.9867 2.44004 31.0089 2.45429C31.0976 2.51132 31.1356 2.61746 31.1087 2.71409V2.71251Z" fill="#16A349"/>
      <Path d="M5.68294 22.2623C5.65917 22.3478 5.58947 22.4144 5.49601 22.4302C5.36928 22.4508 5.24889 22.3653 5.22671 22.2385C4.65801 18.839 5.14117 15.9163 5.64492 14.0629C6.19144 12.051 6.86786 10.8582 6.89638 10.8091C6.95974 10.6966 7.10231 10.6586 7.21479 10.7219C7.32726 10.7853 7.36528 10.9279 7.30191 11.0404C7.29241 11.0578 6.62232 12.2459 6.09164 14.2007C5.60215 16.005 5.13483 18.8517 5.68769 22.1609C5.69403 22.1958 5.69086 22.229 5.68294 22.2607V22.2623Z" fill="#16A349"/>
      <Path d="M27.0944 1.38966C27.0611 1.51005 26.9375 1.58292 26.8156 1.5544C26.7807 1.54648 23.904 0.884319 20.2066 1.5053C16.8023 2.07717 11.9818 3.8894 8.35101 9.27068C8.27814 9.37681 8.13398 9.40533 8.02626 9.33404C7.91854 9.26276 7.89161 9.11702 7.9629 9.0093C9.6088 6.56974 11.6286 4.63553 13.9683 3.26051C15.8408 2.15954 17.9191 1.41342 20.1464 1.04115C23.9356 0.407497 26.8013 1.07125 26.9217 1.09976C27.0469 1.12986 27.1245 1.255 27.0944 1.38015C27.0944 1.38332 27.0944 1.38649 27.0928 1.38807L27.0944 1.38966Z" fill="#16A349"/>
      <Path d="M36.1892 4.42563C37.6466 5.03393 38.5954 6.57687 38.624 8.10238C38.6636 8.93247 38.2691 9.63423 37.8699 10.3851C37.7416 10.8429 38.144 11.4322 38.6509 11.3213C39.8992 11.0679 41.1982 11.2057 42.1692 12.0643C43.3462 13.1193 43.8801 14.9743 43.4128 16.56C42.5764 19.6554 39.2782 21.2776 36.5155 22.8142C36.4743 22.849 36.4299 22.8807 36.3808 22.9092L28.56 27.34C28.1672 27.5618 27.6697 27.424 27.4464 27.0327C27.223 26.6398 27.3608 26.1424 27.7537 25.919L34.613 22.0332C36.5963 20.8039 39.6093 19.5984 40.8655 17.6388C41.9981 16.0563 41.2631 13.688 39.5285 12.9308C38.0236 12.2734 36.3682 13.0195 34.9139 13.8607C34.2549 14.1474 33.0177 15.3513 32.43 14.3359C31.8835 13.3474 33.3884 12.9245 33.9714 12.5268C35.3813 11.6936 36.129 11.1376 36.5456 9.85759C36.9226 8.47782 36.1242 6.84617 34.8632 6.20777C33.4027 5.46006 31.6997 6.32183 30.2725 7.14082C30.2725 7.14082 30.2709 7.14082 30.2693 7.14399C30.2313 7.16617 30.1932 7.18834 30.1552 7.20894L29.7592 7.43388L26.0571 9.53126C25.6642 9.75304 25.1652 9.61522 24.9435 9.22394C24.7217 8.83108 24.8595 8.33208 25.2508 8.1103L29.4297 5.74204C29.4566 5.7262 29.4851 5.71352 29.5121 5.70085C31.6887 4.45573 33.9223 3.46565 36.1876 4.4288L36.1892 4.42563Z" fill="#16A349"/>
      <Path d="M30.1552 7.20812C30.0221 7.28574 29.8906 7.36178 29.7623 7.43624V7.43307L30.1552 7.20812Z" fill="#16A349"/>
      <Path d="M31.3637 19.9166L9.43786 32.9397C9.1052 33.1377 8.6759 33.0284 8.47788 32.6957C8.27511 32.3551 8.39709 31.91 8.74718 31.7215L31.1895 19.6092C31.275 19.5633 31.3827 19.595 31.4287 19.6805C31.4746 19.7645 31.4445 19.869 31.3637 19.9166Z" fill="#16A349"/>
      <Path d="M19.7698 17.5174L5.77719 26.0559C5.44769 26.257 5.01523 26.1525 4.81404 25.823C4.61286 25.4935 4.71741 25.061 5.04691 24.8599C5.04849 24.8519 19.5876 17.237 19.6034 17.2244C19.7856 17.1325 19.9567 17.3907 19.7698 17.5158V17.5174Z" fill="#16A349"/>
      <Path d="M25.8296 18.7534L1.05547 33.3844C0.722804 33.5808 0.293506 33.4699 0.0970743 33.1372C-0.10411 32.7966 0.017868 32.3531 0.366376 32.1662L25.6506 18.4365C25.7393 18.389 25.8502 18.4207 25.8993 18.5094C25.9468 18.5965 25.9151 18.7043 25.8312 18.7534H25.8296Z" fill="#16A349"/>
      <Path d="M30.2688 7.14208C30.2578 7.14842 30.2467 7.15476 30.2372 7.16109L30.1548 7.20703C30.1928 7.18485 30.2308 7.16268 30.2688 7.14208Z" fill="#16A349"/>
      <Path d="M26.7255 28.244L14.7242 34.5077C14.4596 34.6391 14.2379 34.2669 14.4929 34.099L26.0348 27.0243C26.3643 26.8215 26.7968 26.926 26.998 27.2555C27.2103 27.5993 27.0835 28.0571 26.7255 28.2425V28.244Z" fill="#16A349"/>
      <Path d="M24.0704 10.4698L15.7822 14.6313C15.6666 14.6899 15.5256 14.6423 15.467 14.5267C15.4131 14.419 15.4512 14.2875 15.551 14.2225L23.3813 9.25156C23.7076 9.04404 24.1417 9.14067 24.3492 9.467C24.5567 9.79333 24.4601 10.2274 24.1338 10.4349C24.1147 10.4476 24.0926 10.4587 24.072 10.4698H24.0704Z" fill="#16A349"/>
      <Path d="M13.6125 0.861863C13.2386 0.758895 13.02 0.373953 13.123 9.91821e-05C13.02 0.373953 12.6335 0.592562 12.2612 0.48801C12.6351 0.590978 12.8537 0.977505 12.7491 1.34977C12.8521 0.975921 13.2386 0.757311 13.6109 0.861863H13.6125Z" fill="#16A349"/>
      <Path d="M19.7597 6.85521C19.2464 6.71263 18.947 6.18354 19.088 5.67028C18.9454 6.18354 18.4163 6.48294 17.9031 6.34195C18.4163 6.48452 18.7157 7.01362 18.5747 7.52687C18.7173 7.01362 19.2464 6.71422 19.7597 6.85521Z" fill="#16A349"/>
      <Path d="M11.1553 14.3312C10.642 14.1886 10.3426 13.6595 10.4836 13.1462C10.3411 13.6595 9.81196 13.9589 9.29871 13.8179C9.81196 13.9605 10.1114 14.4896 9.97038 15.0028C10.1129 14.4896 10.642 14.1902 11.1553 14.3312Z" fill="#16A349"/>
      <Path d="M20.8043 34.5033C20.4352 34.4019 20.2182 34.0186 20.3211 33.6495C20.2197 34.0186 19.8364 34.2356 19.4673 34.1326C19.8364 34.234 20.0534 34.6174 19.9504 34.9865C20.0518 34.6174 20.4352 34.4004 20.8043 34.5033Z" fill="#16A349"/>
      <Path d="M28.8948 33.1298C28.2437 32.9492 27.8619 32.2759 28.0425 31.6248C27.8619 32.2759 27.1887 32.6577 26.5376 32.4771C27.1887 32.6577 27.5704 33.3309 27.3899 33.982C27.5704 33.3309 28.2437 32.9492 28.8948 33.1298Z" fill="#16A349"/>
      <Path d="M36.2443 26.9426C35.7216 26.7984 35.4143 26.2566 35.56 25.7339C35.4158 26.2566 34.8741 26.564 34.3513 26.4182C34.8741 26.5624 35.1814 27.1041 35.0357 27.6269C35.1798 27.1041 35.7216 26.7968 36.2443 26.9426Z" fill="#16A349"/>
      <Path d="M34.8848 37.9855C34.3035 37.8239 33.9629 37.2235 34.1229 36.6422C33.9613 37.2235 33.3609 37.5641 32.7795 37.4041C33.3609 37.5657 33.7015 38.1661 33.5415 38.7475C33.7031 38.1661 34.3035 37.8255 34.8848 37.9855Z" fill="#16A349"/>
      <Path d="M9.89062 29.4522C9.64062 29.383 9.38185 29.5296 9.31266 29.7796C9.24347 30.0296 9.39005 30.2884 9.64006 30.3576C9.89006 30.4268 10.1488 30.2802 10.218 30.0302C10.2872 29.7802 10.1406 29.5214 9.89062 29.4522Z" fill="#16A349"/>
      <Path d="M4.62294 27.6831C4.38347 27.6168 4.13562 27.7572 4.06934 27.9967C4.00307 28.2362 4.14347 28.484 4.38293 28.5503C4.6224 28.6166 4.87025 28.4762 4.93653 28.2367C5.0028 27.9972 4.8624 27.7494 4.62294 27.6831Z" fill="#16A349"/>
    </G>
  </Svg>
);

export {
  BielaLogo
}
