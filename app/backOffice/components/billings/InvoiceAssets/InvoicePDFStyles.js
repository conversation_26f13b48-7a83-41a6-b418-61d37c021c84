import { StyleSheet } from '@react-pdf/renderer';

export const styles = StyleSheet.create({
  page: {
    backgroundColor: '#0A0F1C',
    color: '#FFFFFF',
    padding: 30,
    position: 'relative',
    fontFamily: 'Manrope',
  },
  backgroundDecoration1: {
    position: 'absolute',
    top: 0,
    left: -20,
    width: '30%',
    height: '40%',
  },
  backgroundDecoration2: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: '40%',
    height: '40%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 40,
  },
  logo: {
    width: 120,
    height: 40,
  },
  headerRight: {
    flexDirection: 'row',
    gap: 20,
  },
  headerItem: {
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  headerLabel: {
    fontSize: 10,
    marginBottom: 5,
    fontWeight: 300,
  },
  headerValue: {
    fontSize: 12,
    fontWeight: 400,
  },
  section: {
    marginBottom: 30,
  },
  sectionIconContainer: {
    padding: 6,
    backgroundColor: '#28FF7A',
    opacity: 0.1,
    borderRadius: 6,
  },
  sectionTitleLabel: {
    fontWeight: 400,
    fontSize: 20,
    marginLeft: 6,
  },
  sectionTitle: {
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionIcon: {
    width: 16,
    height: 16,
    marginRight: 8,
  },
  addressLine: {
    fontSize: 10,
    marginBottom: 3,
    color: '#FFFFFF',
    fontWeight: 400,
  },
  bankDetails: {
    marginTop: 10,
  },
  bankDetailsLine: {
    fontSize: 10,
    marginBottom: 3,
    color: '#FFFFFF',
    fontWeight: 400,
  },
  tableContainer: {
    marginBottom: 20,
  },
  tableHeader: {
    flexDirection: 'row',
    paddingBottom: 8,
    marginBottom: 8,
    borderBottomWidth: 0.5,
    borderBottomColor: '#374151',
    borderBottomStyle: 'solid',
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 8,
    borderBottomWidth: 0.5,
    borderBottomColor: '#1F2937',
    borderBottomStyle: 'solid',
  },
  tableHeaderText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: 300,
  },
  tableCell: {
    fontSize: 10,
    color: 'white',
    fontWeight: 400,
  },
  descriptionCol: {
    width: '40%',
  },
  qtyCol: {
    width: '20%',
    textAlign: 'center',
  },
  priceCol: {
    width: '20%',
    textAlign: 'right',
  },
  amountCol: {
    width: '20%',
    textAlign: 'right',
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 20,
  },
  summary: {
    width: '30%',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
    paddingVertical: 4,
    borderBottomWidth: 0.5,
    borderBottomColor: '#374151',
    borderBottomStyle: 'solid',
  },
  summaryLabel: {
    fontSize: 14,
    fontWeight: 400,
  },
  summaryValue: {
    fontSize: 14,
    textAlign: 'right',
    fontWeight: 400,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 5,
    paddingTop: 5,
    paddingVertical: 4,
    // borderTopWidth: 0.5,
    borderTopColor: '#374151',
    borderTopStyle: 'solid',
  },
  totalLabel: {
    fontSize: 14,
    fontWeight: 400,
  },
  totalValue: {
    fontSize: 14,
    fontWeight: 600,
    textAlign: 'left',
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
  },
  contact: {
    flexDirection: 'column',
    marginBottom: 10,
  },
  contactTitle: {
    fontSize: 14,
    marginBottom: 10,
    color: '#FFFFFF',
    flexDirection: 'row',
    alignItems: 'center',
    fontWeight: 600,
  },
  contactLine: {
    fontSize: 10,
    marginBottom: 3,
    color: '#FFFFFF',
    flexDirection: 'row',
    alignItems: 'center',
    fontWeight: 400,
  },
  contactIcon: {
    width: 12,
    height: 12,
    marginRight: 5,
  },
  fromToContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 40,
  },
  fromTo: {
    width: '45%',
  },
});
