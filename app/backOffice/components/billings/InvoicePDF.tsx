import { Document, Font, Image, Page, Text, View } from '@react-pdf/renderer';
import { BielaLogo } from './InvoiceAssets/BielaLogo';
import LeftDecoration from './InvoiceAssets/leftDecoration.png';
import BottomDecoration from './InvoiceAssets/bottomDecoration.png';
import { FromIcon } from './InvoiceAssets/FromIcon';
import { ToIcon } from './InvoiceAssets/ToIcon';
import { GlobeIcon } from './InvoiceAssets/GlobeIcon';
import { LocationPinIcon } from './InvoiceAssets/LocationPinIcon';
import { BankIcon } from './InvoiceAssets/BankIcon';
import { styles } from './InvoiceAssets/InvoicePDFStyles';

Font.register({
  family: 'Manrope',
  fonts: [
    { src: '/fonts/manrope/Manrope-Regular.otf', fontWeight: 'normal' },
    { src: '/fonts/manrope/Manrope-Medium.otf', fontWeight: 'medium' },
    { src: '/fonts/manrope/Manrope-SemiBold.otf', fontWeight: 'semibold' },
    { src: '/fonts/manrope/Manrope-Bold.otf', fontWeight: 'bold' },
  ],
});

const InvoicePDF = ({ invoiceData }) => (
  <Document>
    <Page size="A4" style={styles.page}>
      {/* Background decorations */}
      <Image src={LeftDecoration} style={styles.backgroundDecoration1} />
      <Image src={BottomDecoration} style={styles.backgroundDecoration2} />

      {/* Header */}
      <View style={styles.header}>
        <View>
          <BielaLogo />
        </View>
        <View style={styles.headerRight}>
          <View style={styles.headerItem}>
            <Text style={styles.headerLabel}>Invoice number</Text>
            <Text style={styles.headerValue}>{invoiceData.invoiceNumber}</Text>
          </View>
          <View style={styles.headerItem}>
            <Text style={styles.headerLabel}>Issued</Text>
            <Text style={styles.headerValue}>
              {`${String(new Date(invoiceData.invoiceDate * 1000).getDate()).padStart(2, '0')}.${String(new Date(invoiceData.invoiceDate * 1000).getMonth() + 1).padStart(2, '0')}.${new Date(invoiceData.invoiceDate * 1000).getFullYear()}`}
            </Text>
          </View>
          {/*<View style={styles.headerItem}>*/}
          {/*    <Text style={styles.headerLabel}>Due Date</Text>*/}
          {/*    <Text style={styles.headerValue}>{invoiceData.dueDate}</Text>*/}
          {/*</View>*/}
        </View>
      </View>

      {/* From and To sections */}
      <View style={styles.fromToContainer}>
        <View style={styles.fromTo}>
          <View style={styles.sectionTitle}>
            <View style={styles.sectionIconContainer}>
              <FromIcon />
            </View>
            <Text style={styles.sectionTitleLabel}>From</Text>
          </View>
          <View style={{ gap: 4 }}>
            <View style={{ flexDirection: 'row', gap: 6 }}>
              <GlobeIcon />
              <Text style={styles.addressLine}>Teachmecode Institute</Text>
            </View>
            <View style={{ flexDirection: 'row', gap: 6 }}>
              <LocationPinIcon />
              <Text style={styles.addressLine}>
                404, Building 08, Business Bay, Bay Square, <br />
                Dubai (UAE)
              </Text>
            </View>
          </View>
          <View style={styles.bankDetails}>
            <View style={{ flexDirection: 'row', gap: 6 }}>
              <BankIcon />
              <Text style={{ fontSize: 12, marginBottom: 5, color: '#D1D5DB', fontWeight: 500 }}>Bank Details:</Text>
            </View>
            <Text style={styles.bankDetailsLine}>Account Number: ************ </Text>
            <Text style={styles.bankDetailsLine}>IBAN : AE220570000************ </Text>
            <Text style={styles.bankDetailsLine}>Bank Name : Ajman Bank </Text>
            <Text style={styles.bankDetailsLine}>SwiftCode : AJMNAEAJ</Text>
          </View>
        </View>

        <View style={styles.fromTo}>
          <View style={styles.sectionTitle}>
            <View style={styles.sectionIconContainer}>
              <ToIcon />
            </View>
            <Text style={styles.sectionTitleLabel}>To</Text>
          </View>
          <View style={{ gap: 4 }}>
            <View style={{ flexDirection: 'row', gap: 6 }}>
              <GlobeIcon />
              <Text style={styles.addressLine}>{invoiceData?.user?.name}</Text>
            </View>
            <View style={{ flexDirection: 'row', gap: 6 }}>
              <LocationPinIcon />
              <Text style={styles.addressLine}>{invoiceData?.user?.address ?? invoiceData?.user?.backupAddress}</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Invoice Table */}
      <View style={styles.tableContainer}>
        {/* Table Header */}
        <View style={styles.tableHeader}>
          <View style={styles.descriptionCol}>
            <Text style={styles.tableHeaderText}>Description</Text>
          </View>
          <View style={styles.qtyCol}>
            <Text style={styles.tableHeaderText}>QTY</Text>
          </View>
          {/*<View style={styles.priceCol}>*/}
          {/*  <Text style={styles.tableHeaderText}>Price</Text>*/}
          {/*</View>*/}
          <View style={styles.amountCol}>
            <Text style={styles.tableHeaderText}>Amount</Text>
          </View>
        </View>

        {/* Table Rows */}
        <View style={styles.tableRow}>
          <View style={styles.descriptionCol}>
            <Text style={styles.tableCell}>{invoiceData.invoiceDescription}</Text>
          </View>
          <View style={styles.qtyCol}>
            <Text style={styles.tableCell}>{invoiceData?.quantity}</Text>
          </View>
          {/*<View style={styles.priceCol}>*/}
          {/*  <Text style={styles.tableCell}>${item.price}</Text>*/}
          {/*</View>*/}
          <View style={styles.amountCol}>
            <Text style={styles.tableCell}>${invoiceData?.invoiceAmount}</Text>
          </View>
        </View>
      </View>

      <View style={styles.summaryContainer}>
        <View style={styles.summary}>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Sub Total</Text>
            <Text style={styles.summaryValue}>${invoiceData?.invoiceAmount}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Tax</Text>
            <Text style={styles.summaryValue}>${0}</Text>
          </View>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Total</Text>
            <Text style={styles.totalValue}>${invoiceData?.invoiceAmount}</Text>
          </View>
        </View>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <View style={styles.contact}>
          <View style={styles.contactTitle}>
            <Text style={{ color: '#10B981', fontWeight: 600 }}>Contact</Text>
          </View>
          <View style={styles.contactLine}>
            <Text style={styles.addressLine}><EMAIL></Text>
          </View>
          <View style={styles.contactLine}>
            <Text style={styles.addressLine}>+971 56 389 9888</Text>
          </View>
        </View>
      </View>
    </Page>
  </Document>
);

export { InvoicePDF };
