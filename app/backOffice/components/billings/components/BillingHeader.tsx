import React from 'react';
import { useTranslation } from 'react-i18next';

export const BillingHeader: React.FC = () => {
  const { t } = useTranslation('profile');

  return (
    <div className="text-center space-y-4">
      <h2 className="text-[20px] md:text-4xl font-light">
        {t('modalTitle', 'Choose a Plan or Reload Tokens as Needed')}
      </h2>
      <p className="text-gray-400 font-light text-[16px] md:text-lg">
        {t(
          'modalSubtitle',
          'Pick from our three flexible plans or purchase extra tokens anytime to keep coding without limits.',
        )}
      </p>
    </div>
  );
};
