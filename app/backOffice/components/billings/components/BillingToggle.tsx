import React from 'react';
import { useTranslation } from 'react-i18next';
import { BillingToggleProps } from '~/types/billing';

export const BillingToggle: React.FC<BillingToggleProps> = ({ stripePlans, billingCycle, setBillingCycle }) => {
  const { t } = useTranslation('profile');

  if (stripePlans?.length === 0) return null;

  return (
    <div className="flex justify-center">
      <div className="bg-gray-800/50 rounded-full p-1">
        <button
          onClick={() => setBillingCycle('monthly')}
          className={`px-2 md:px-6 py-2 rounded-full transition-all font-light ${
            billingCycle === 'monthly' ? 'bg-green-500' : 'bg-transparent text-gray-400 hover:text-white'
          }`}
        >
          {t('toggleMonthly', 'Monthly')}
        </button>
        <button
          onClick={() => setBillingCycle('yearly')}
          className={`px-2 md:px-6 py-2 rounded-full transition-all font-light ${
            billingCycle === 'yearly' ? 'bg-green-500' : 'bg-transparent text-gray-400 hover:text-white'
          }`}
        >
          {t('toggleYearly', 'Yearly (save 10%)')}
        </button>
      </div>
    </div>
  );
};
