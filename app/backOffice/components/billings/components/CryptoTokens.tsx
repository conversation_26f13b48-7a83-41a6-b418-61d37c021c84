import React, { useState } from 'react';
import { FaCopy, FaArrowLeft } from 'react-icons/fa';
import BTCIcon from '~/assets/icons/bitcoin-icon.svg';
import ETHIcon from '~/assets/icons/etherium-icon.svg';
import USDTIcon from '~/assets/icons/usdt-icon.svg';
import USDCIcon from '~/assets/icons/usdc-icon.svg';
import QRCode from 'react-qr-code';
import RoundedCorner from '~/assets/icons/roundedCorner.svg';
import { backendApiFetch } from '~/ai/lib/backend-api';
import { AlertCircle } from 'lucide-react';
import { CryptoTokensProps, Currency, WalletData } from '~/types/billing';

const CryptoTokens: React.FC<CryptoTokensProps> = ({ onBack }) => {
  const [walletData, setWalletData] = useState<WalletData>();
  const [currency, setCurrency] = useState<Currency>({
    label: '',
    value: '',
    icon: '',
  });

  const currencies = [
    { label: 'BTC', value: 'BTC', icon: BTCIcon },
    { label: 'ETH', value: 'ETH', icon: ETHIcon },
    { label: 'USDT', value: 'USDT', icon: USDTIcon },
    { label: 'USDC', value: 'USDC', icon: USDCIcon },
  ];

  async function createWallet(request: { currency: string }) {
    try {
      const response = await backendApiFetch(`/payments/0x/create-wallet`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error('Failed to create wallet');
      }

      const data = (await response.json()) as { address: string; currency: string; processingFee: number };
      setWalletData(data);
    } catch (error) {
      console.error(error);
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <button
          onClick={onBack}
          className="p-2 rounded-lg text-green-400 hover:text-green-300 transition-colors bg-transparent"
        >
          <FaArrowLeft className="text-xl" />
        </button>
        <div>
          <h2 className="text-xl font-light text-white">Get More Tokens</h2>
          <p className="text-gray-400 font-light text-md mt-1">
            Deposit using your favorite cryptocurrency and unlock more tokens.
          </p>
        </div>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <label className="text-md text-gray-400 font-light">Select Currency</label>
          <div className="grid grid-cols-4 gap-2">
            {currencies.map((curr) => (
              <button
                key={curr.value}
                onClick={() => {
                  setCurrency(curr);
                  createWallet({ currency: curr.value });
                }}
                className={`p-3 rounded-xl transition-all ${
                  currency.value === curr.value
                    ? 'bg-green-500/20 ring-1 ring-green-500/30'
                    : 'bg-gray-800/50 hover:bg-gray-800/70'
                }`}
              >
                <div className="flex items-center gap-2 justify-center">
                  <img src={curr.icon} alt={curr.label} className="w-5 h-5" />
                  <span className={`font-light ${currency.value === curr.value ? 'text-green-400' : 'text-gray-400'}`}>
                    {curr.label}
                  </span>
                </div>
              </button>
            ))}
          </div>
        </div>

        {walletData && (
          <>
            <div className="space-y-2">
              <label className="text-md font-light text-gray-400">Deposit Address</label>
              <div className="bg-gray-800/50 p-3 rounded-xl">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300 font-mono text-sm truncate">{walletData.address}</span>
                  <button
                    onClick={() => navigator.clipboard?.writeText(walletData.address)}
                    className="flex items-center gap-2 text-green-400 hover:text-green-300 transition-colors bg-transparent"
                  >
                    <FaCopy className="text-sm" />
                  </button>
                </div>
              </div>
            </div>
            <div className="bg-green-900/20 rounded-xl p-4 border border-green-700/50 flex flex-col gap-[8px]">
              <div className={' flex gap-3'}>
                <AlertCircle className="text-green-400 w-5 h-5 mt-0.5" />
                <div className={'text-[16px] font-300'}>
                  You can make the payment on any network supported by the currency
                </div>
              </div>
              <div className={' flex gap-3'}>
                <AlertCircle className="text-green-400 w-5 h-5 mt-0.5" />
                <div className={'text-[16px] font-300'}>Payment processing can take more than 5 minutes</div>
              </div>
              <div className={' flex gap-3'}>
                <AlertCircle className="text-green-400 w-5 h-5 mt-0.5" />
                <div className={'text-[16px] font-300'}>Minimum amount $3 or equivalent</div>
              </div>
              <div className={' flex gap-3'}>
                <AlertCircle className="text-green-400 w-5 h-5 mt-0.5" />
                <div className={'text-[16px] font-300'}>
                  <p className="text-[16px] ">
                    Please note: A deposit{' '}
                    <span className="text-green-400">fee of {walletData.processingFee}% may apply</span>, depending on
                    the currency you choose to deposit.
                  </p>
                </div>
              </div>
            </div>
            <div className="flex justify-center py-4">
              <div className="relative">
                <div className="absolute inset-0 flex">
                  <div className="h-full relative flex flex-col justify-between">
                    <img src={RoundedCorner} alt="" className="w-5 h-5" />
                    <img src={RoundedCorner} alt="" className="w-5 h-5 rotate-270" />
                  </div>
                  <div className="flex-1" />
                  <div className="h-full relative flex flex-col justify-between">
                    <img src={RoundedCorner} alt="" className="w-5 h-5 rotate-90" />
                    <img src={RoundedCorner} alt="" className="w-5 h-5 rotate-180" />
                  </div>
                </div>
                <div className="m-5">
                  <div className="bg-white p-3 rounded-lg">
                    <QRCode value={walletData.address} size={180} bgColor="#FFFFFF" fgColor="#000000" level="L" />
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default CryptoTokens;
