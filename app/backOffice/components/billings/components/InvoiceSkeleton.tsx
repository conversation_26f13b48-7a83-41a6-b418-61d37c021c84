export const InvoiceSkeletonLoader = () => {
  return Array(5)
    .fill(0)
    .map((_, index) => (
      <tr key={`skeleton-${index}`} className={`${index !== 4 ? 'border-b border-green-500/20' : ''}`}>
        <td className="px-6 py-4">
          <div className="h-5 bg-gray-700/70 rounded-md w-28 animate-pulse"></div>
        </td>
        <td className="px-6 py-4">
          <div className="h-5 bg-gray-700/70 rounded-md w-40 animate-pulse"></div>
        </td>
        <td className="px-6 py-4">
          <div className="h-5 bg-gray-700/70 rounded-md w-16 animate-pulse"></div>
        </td>
        <td className="px-6 py-4">
          <div className="h-6 bg-gray-700/70 rounded-full w-20 animate-pulse"></div>
        </td>
        <td className="px-6 py-4">
          <div className="flex items-center justify-end gap-2">
            <div className="w-8 h-8 rounded-lg bg-gray-700/70 animate-pulse"></div>
            <div className="w-8 h-8 rounded-lg bg-gray-700/70 animate-pulse"></div>
          </div>
        </td>
      </tr>
    ));
};
