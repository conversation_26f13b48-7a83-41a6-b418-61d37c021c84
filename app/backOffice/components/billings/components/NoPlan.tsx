import React from 'react';
import { motion } from 'framer-motion';
import { FaLock, FaGem } from 'react-icons/fa';

export const NoPlan: React.FC = () => {
  return (
    <div className="container mx-auto px-4 sm:px-8 pb-8 max-w-[1600px] rounded-xl">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-white min-h-[60vh] flex items-center justify-center"
      >
        <div className="text-center space-y-6 max-w-2xl mx-auto p-8">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
            className="w-24 h-24 mx-auto bg-gradient-to-br from-green-500/20 to-green-600/20 rounded-2xl flex items-center justify-center relative group"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
            <FaLock className="text-4xl text-green-400 group-hover:scale-110 transition-transform duration-300" />
          </motion.div>

          <h2 className="text-3xl font-light bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            Subscription Plans Coming Soon
          </h2>

          <p className="text-gray-400 text-[16px] font-light leading-relaxed">
            Our team is preparing exclusive subscription plans for enhanced capabilities. Stay tuned for premium
            features that will transform your development experience.
          </p>

          <div className="flex items-center justify-center gap-4 text-sm text-gray-500 pt-4">
            <div className="flex items-center gap-2 bg-gray-800/50 px-4 py-2 rounded-full">
              <FaGem className="text-green-400" />
              <span className="text-green-400">Premium Features Incoming</span>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};
