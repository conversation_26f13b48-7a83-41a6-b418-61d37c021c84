import React from 'react';
import { useTranslation } from 'react-i18next';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { PaginationProps } from '~/types/billing';

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  totalRecords,
  pageSize,
  handlePageChange,
  renderPaginationItems,
  loadingInvoices,
}) => {
  const { t } = useTranslation('profile');

  return (
    <div className="mt-6 flex flex-col sm:flex-row items-center justify-between border-t border-green-500/20 pt-4 gap-4">
      <div className="text-sm text-gray-400 order-2 sm:order-1">
        {t('paginationStatus', 'Showing {{from}} to {{to}} of {{total}} results', {
          from: (currentPage - 1) * pageSize + 1,
          to: Math.min(currentPage * pageSize, totalRecords),
          total: totalRecords,
        })}
      </div>

      <div className="flex items-center space-x-1 order-1 sm:order-2">
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1 || loadingInvoices}
          className={`inline-flex items-center justify-center bg-transparent p-2 rounded-md transition-all duration-200 ${
            currentPage === 1
              ? 'text-gray-600 cursor-not-allowed'
              : 'text-gray-400 hover:bg-green-500/20 hover:text-white'
          }`}
          aria-label={t('previousPage', 'Previous page')}
        >
          <ChevronLeftIcon className="w-5 h-5" />
        </button>

        <div className="flex items-center">{renderPaginationItems()}</div>

        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages || loadingInvoices}
          className={`inline-flex items-center justify-center p-2 rounded-md bg-transparent transition-all duration-200 ${
            currentPage === totalPages
              ? 'text-gray-600 cursor-not-allowed'
              : 'text-gray-400 hover:bg-green-500/20 hover:text-white'
          }`}
          aria-label={t('nextPage', 'Next page')}
        >
          <ChevronRightIcon className="w-5 h-5" />
        </button>
      </div>
    </div>
  );
};
