import React from 'react';
import { motion } from 'framer-motion';
import {
  FaCreditCard,
  FaPaypal,
  FaBitcoin,
  FaCcVisa,
  FaCcMastercard,
  FaCcAmex,
  FaCcDiscover,
  FaCcDinersClub,
  FaCcJcb,
} from 'react-icons/fa';
import { PaymentMethodProps } from '~/types/billing';

const PaymentMethod: React.FC<PaymentMethodProps> = ({ method, isSelected, isDisabled, onClick }) => {
  const icons = {
    card: FaCreditCard,
    paypal: FaPaypal,
    crypto: FaBitcoin,
  };

  const labels = {
    card: 'Card',
    paypal: 'PayPal',
    crypto: 'Crypto',
  };

  const Icon = icons[method];

  return (
    <motion.button
      whileHover={isDisabled ? undefined : { scale: 1.02 }}
      whileTap={isDisabled ? undefined : { scale: 0.98 }}
      onClick={isDisabled ? undefined : onClick}
      className={`relative w-full p-4 rounded-xl transition-all ${
        isSelected
          ? 'bg-green-500/20 ring-1 ring-green-500/30'
          : isDisabled
            ? 'bg-gray-800/30 cursor-not-allowed'
            : 'bg-[#0A1730]/80 hover:bg-[#0A1730] ring-1 ring-gray-700/30 hover:ring-gray-600/50'
      }`}
    >
      <div className="flex items-center justify-between gap-3">
        <div className="flex items-center gap-3">
          <Icon className={`text-2xl ${isSelected ? 'text-green-400' : 'text-gray-400'}`} />
          <span className={`font-light ${isSelected ? 'text-green-400' : 'text-gray-400'}`}>{labels[method]}</span>
        </div>
        {method === 'card' && (
          <div className="grid grid-cols-3 sm:grid-cols-6 gap-2 gap-y-0.5 sm:gap-4 w-max">
            <FaCcVisa className="text-2xl" />
            <FaCcMastercard className="text-2xl" />
            <FaCcAmex className="text-2xl" />
            <FaCcDiscover className="text-2xl" />
            <FaCcDinersClub className="text-2xl" />
            <FaCcJcb className="text-2xl" />
          </div>
        )}
      </div>
      {isDisabled && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-xl backdrop-blur-sm">
          <span className="text-sm text-gray-400">Coming Soon</span>
        </div>
      )}
    </motion.button>
  );
};

export default PaymentMethod;
