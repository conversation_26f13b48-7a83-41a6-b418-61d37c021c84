import React from 'react';
import { motion } from 'framer-motion';
import { FaRobot } from 'react-icons/fa';
import { PlanCardProps } from '~/types/billing';
import { AVAILABLE_ICONS, FeatureDescriptions, formatNumber } from '~/backOffice/components/billings/components/helper';
import { JSX } from 'react/jsx-runtime';
import { toNumber } from 'lodash';

export const PlanCard: React.FC<PlanCardProps> = ({ plan, index, billingCycle, onSubscribe }) => {
  const getIcon = (iconName: string): JSX.Element | undefined => {
    const found = AVAILABLE_ICONS.find((item) => item.name === iconName);
    if (found) {
      const IconComponent = found.icon;
      return <IconComponent />;
    }
    return undefined;
  };

  return (
    <motion.div
      className={`w-full ${index === 1 ? 'pt-0' : 'pt-12'}`}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <div
        className={`bg-gradient-to-r rounded-xl ${
          plan?.isPopular ? 'ring-2 ring-green-500' : ''
        } h-full flex flex-col overflow-hidden`}
        style={{
          backgroundImage: `linear-gradient(to right, ${plan.colorFrom}, ${plan.colorTo})`,
        }}
      >
        <div className={`p-6 relative ${index === 1 ? 'py-10' : ''}`}>
          {plan?.isPopular && (
            <div className="absolute top-0 right-0">
              <div className="bg-green-500 px-4 py-1 rounded-bl-lg text-sm font-light">Most Popular</div>
            </div>
          )}

          <div className="z-10 flex items-center gap-4">
            <div className={`p-4 bg-black/20 rounded-xl scale-110 text-2xl`}>{getIcon(plan.icon)}</div>
            <div>
              <h3 className={`font-medium text-[24px]`}>{plan.name}</h3>
              <div className="flex items-baseline gap-1 mt-1">
                <span className={`font-light ${index === 1 ? 'text-[36px]' : 'text-[30px]'}`}>
                  $ {plan.price.toLocaleString('en-US')}
                </span>
                <span className="text-gray-400 text-sm font-light">
                  /{billingCycle === 'monthly' ? 'month' : 'year'}
                </span>
              </div>
              <div className="font-light text-[16px] w-full flex justify-start text-sm">
                {index === 2 && billingCycle === 'yearly'
                  ? 'below $2'
                  : `$${((plan.price / toNumber(plan?.tokenAllocation)) * 1_000_000).toFixed(2)}`}{' '}
                / 1M tokens
              </div>
            </div>
            <div className="absolute -bottom-12 -right-12 text-[200px] opacity-5">{getIcon(plan.icon)}</div>
          </div>
        </div>

        <div className={`p-6 relative z-10 flex-1 flex flex-col ${index === 1 ? 'pb-10' : ''}`}>
          <div className="bg-black/20 rounded-lg px-4 py-3 mb-6">
            <div className="flex items-center gap-2">
              <FaRobot className="text-green-400" />
              <span className="text-green-400 text-[20px] font-normal flex flex-row items-center justify-between w-full">
                {index === 2 && billingCycle === 'yearly' ? 'UNLIMITED' : formatNumber(plan?.tokenAllocation)} Tokens{' '}
                {index === 1 && <div className="text-green-400 text-sm font-normal">+10% BONUS</div>}
              </span>
              <span className="text-gray-400 text-sm font-light">{plan?.period}</span>
            </div>
          </div>

          <div className="flex-1 mb-6">
            {plan?.features?.map((feature, idx) => (
              <div key={idx} className="flex items-center gap-3 text-sm mt-[8px]">
                <div className="w-1.5 h-1.5 rounded-full bg-green-400"></div>
                <span className="font-light text-[16px]">{FeatureDescriptions[feature]}</span>
              </div>
            ))}
          </div>

          <button
            onClick={() => !plan.currentPlan && onSubscribe(plan.stripePlanId, plan.stripePriceId)}
            className={`w-full py-3 rounded-lg transition-colors font-light ${
              plan.currentPlan ? 'bg-gray-700 text-gray-400 cursor-not-allowed' : 'bg-green-500 hover:bg-green-600'
            } ${index === 1 ? 'py-4 text-lg' : ''}`}
          >
            {plan.currentPlan ? 'Current Plan' : 'Upgrade Plan'}
          </button>
        </div>
      </div>
    </motion.div>
  );
};
