import React from 'react';
import { PlanCardSkeletonProps } from '~/types/billing';

export const PlanCardSkeleton: React.FC<PlanCardSkeletonProps> = ({ index }) => {
  return (
    <div className={`relative w-full ${index === 1 ? 'pt-0' : 'pt-8'}`}>
      <div className="bg-gradient-to-r from-gray-500/20 to-gray-600/20 animate-pulse rounded-xl overflow-hidden h-full flex flex-col">
        <div className={`p-5 relative overflow-hidden ${index === 1 ? 'py-8' : ''}`}>
          <div className="relative z-10 flex items-center gap-3">
            <div className={`p-3 bg-black/20 rounded-xl h-[35px] w-[35px] ${index === 1 ? 'scale-105' : ''}`}></div>
            <div className="space-y-2">
              <div className={`h-5 w-28 bg-black/20 rounded ${index === 1 ? 'h-6' : ''}`}></div>
              <div className="flex items-baseline gap-1">
                <div className={`h-7 w-16 bg-black/20 rounded ${index === 1 ? 'h-8' : ''}`}></div>
                <div className="h-3 w-6 bg-black/20 rounded"></div>
              </div>
            </div>
          </div>
        </div>

        <div className={`p-5 relative z-10 flex-1 flex flex-col ${index === 1 ? 'pb-8' : ''}`}>
          <div className="bg-black/20 rounded-lg px-4 py-3 mb-5 h-10"></div>
          <div className="space-y-3 flex-1 mb-5">
            {[1, 2, 3, 4].map((_, idx) => (
              <div key={idx} className="flex items-center gap-3">
                <div className="w-1.5 h-1.5 rounded-full bg-black/20"></div>
                <div className="h-3 w-36 bg-black/20 rounded"></div>
              </div>
            ))}
          </div>
          <div className={`w-full h-10 bg-black/20 rounded-lg ${index === 1 ? 'h-12' : ''}`}></div>
        </div>
      </div>
    </div>
  );
};
