import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { EyeIcon } from 'lucide-react';
import { ArrowDownTrayIcon } from '@heroicons/react/16/solid';
import { Invoice, RecentInvoicesProps } from '~/types/billing';
import { fetchInvoices, generateInvoicePDF } from '~/lib/stores/billing';
import { pdf } from '@react-pdf/renderer';
import { InvoicePDF } from '../InvoicePDF';
import { InvoiceSkeletonLoader } from './InvoiceSkeleton';
import { Pagination } from '~/backOffice/components/billings/components/Pagination';

export const RecentInvoices: React.FC<RecentInvoicesProps> = ({
  setSelectedInvoice,
  setShowInvoiceModal,
  setNotLoadedInvoices,
}) => {
  const { t } = useTranslation('profile');
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loadingInvoices, setLoadingInvoices] = useState<boolean>(true);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(5);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalRecords, setTotalRecords] = useState<number>(0);

  useEffect(() => {
    const loadInvoices = async () => {
      setLoadingInvoices(true);
      try {
        const paginatedData = await fetchInvoices(currentPage, pageSize);
        setInvoices(paginatedData.data);
        setTotalPages(paginatedData.pagination.totalPages);
        setTotalRecords(paginatedData.pagination.total);
        setCurrentPage(paginatedData.pagination.currentPage);
      } catch (error) {
        console.error('Error fetching invoices:', error);
      } finally {
        setLoadingInvoices(false);
        setNotLoadedInvoices(false);
      }
    };

    void loadInvoices();
  }, [currentPage, pageSize]);

  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  const handleInvoicePreview = async (id: string, paymentType: 'subscription' | 'topup'): Promise<void> => {
    try {
      const data = await generateInvoicePDF(id, paymentType);
      setSelectedInvoice(data);
      setShowInvoiceModal(true);
    } catch (error) {
      console.error('Error generating invoice preview:', error);
    }
  };

  const handleDownloadInvoice = async (invoiceId: string, paymentType: 'subscription' | 'topup'): Promise<void> => {
    try {
      const data = await generateInvoicePDF(invoiceId, paymentType);

      const blob = await pdf(<InvoicePDF invoiceData={data} />).toBlob();
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `${data.invoiceNumber}.pdf`;
      link.click();

      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading invoice:', error);
    }
  };

  const renderPaginationItems = () => {
    const pageNumbers: (number | string)[] = [];

    if (totalPages <= 7) {
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      pageNumbers.push(1);

      if (currentPage <= 3) {
        pageNumbers.push(2, 3, 4, '...', totalPages);
      } else if (currentPage >= totalPages - 2) {
        pageNumbers.push('...', totalPages - 3, totalPages - 2, totalPages - 1, totalPages);
      } else {
        pageNumbers.push('...', currentPage - 1, currentPage, currentPage + 1, '...', totalPages);
      }
    }

    return pageNumbers.map((pageNumber, index) => {
      if (pageNumber === '...') {
        return (
          <span key={`ellipsis-${index}`} className="px-3 py-2 text-gray-500">
            ...
          </span>
        );
      }

      const page = pageNumber as number;
      return (
        <button
          key={`page-${page}`}
          onClick={() => handlePageChange(page)}
          className={`px-3 py-2 rounded-md transition-all duration-200 bg-transparent ${
            currentPage === page
              ? 'bg-green-500 text-white font-medium'
              : 'text-gray-400 hover:bg-green-500/20 hover:text-white'
          }`}
        >
          {page}
        </button>
      );
    });
  };

  return (
    <div className="bg-[#0A1730] backdrop-blur-sm rounded-xl p-6 shadow-xl border border-green-500/20">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h3 className="text-2xl font-light mb-2">{t('recentInvoicesTitle', 'Recent Invoices')}</h3>
          <p className="text-gray-400 text-[16px] font-light">
            {t('recentInvoicesDescription', 'Track your billing history and download invoices')}
          </p>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full table-auto border-separate border-spacing-0">
          <thead>
            <tr>
              <th className="text-left px-6 py-4 text-[16px] text-gray-300 font-medium rounded-tl-lg bg-green-500/10 border-b border-green-500/20">
                {t('tableHeaderDate', 'Date')}
              </th>
              <th className="text-left px-6 py-4 text-[16px] text-gray-300 font-medium bg-green-500/10 border-b border-green-500/20">
                {t('tableHeaderDescription', 'Description')}
              </th>
              <th className="text-left px-6 py-4 text-[16px] text-gray-300 font-medium bg-green-500/10 border-b border-green-500/20">
                {t('tableHeaderAmount', 'Amount')}
              </th>
              <th className="text-left px-6 py-4 text-[16px] text-gray-300 font-medium bg-green-500/10 border-b border-green-500/20">
                {t('tableHeaderStatus', 'Status')}
              </th>
              <th className="text-right px-6 py-4 text-[16px] text-gray-300 font-medium rounded-tr-lg bg-green-500/10 border-b border-green-500/20">
                {t('tableHeaderActions', 'Actions')}
              </th>
            </tr>
          </thead>
          <tbody>
            {loadingInvoices ? (
              <InvoiceSkeletonLoader />
            ) : invoices?.length > 0 ? (
              invoices.map((invoice, index) => (
                <tr
                  key={invoice.id}
                  className={`
                    hover:bg-green-500/5 transition-colors
                    ${index !== invoices.length - 1 ? 'border-b border-green-500/20' : ''}
                  `}
                >
                  <td className="px-6 py-4 font-light whitespace-nowrap">
                    {new Date(invoice.created * 1000).toLocaleDateString('en-US', {
                      month: 'long',
                      day: 'numeric',
                      year: 'numeric',
                    })}
                  </td>
                  <td className="px-6 py-4 font-light">{invoice.description}</td>
                  <td className="px-6 py-4 font-light">${invoice.amount_paid.toFixed(2)}</td>
                  <td className="px-6 py-4">
                    <span
                      className={`
                        px-3 py-1 rounded-full text-[15px] font-medium
                        ${
                          invoice.status.toLowerCase() === 'paid' || invoice.status.toLowerCase() === 'succeeded'
                            ? 'bg-green-400/10 text-green-400'
                            : 'bg-yellow-400/10 text-yellow-400'
                        }
                      `}
                    >
                      {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center justify-end gap-2">
                      <button
                        onClick={() => handleInvoicePreview(invoice.id, invoice?.type)}
                        className="inline-flex items-center justify-center w-8 h-8 rounded-lg bg-gray-700/50 text-gray-400 hover:text-white hover:bg-green-500/20 transition-colors group"
                        title={t('actionPreviewInvoice', 'Preview Invoice')}
                      >
                        <EyeIcon className="w-5 h-5 group-hover:scale-110 transition-transform" />
                      </button>
                      <button
                        onClick={() => handleDownloadInvoice(invoice.id, invoice.type)}
                        className="inline-flex items-center justify-center w-8 h-8 rounded-lg bg-gray-700/50 text-gray-400 hover:text-white hover:bg-green-500/20 transition-colors group"
                        title={t('actionDownloadInvoice', 'Download Invoice')}
                      >
                        <ArrowDownTrayIcon className="w-5 h-5 group-hover:scale-110 transition-transform" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="px-6 py-8 text-center text-gray-400">
                  {t('noInvoicesFound', 'No invoices found')}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalRecords}
          pageSize={pageSize}
          handlePageChange={handlePageChange}
          renderPaginationItems={renderPaginationItems}
          loadingInvoices={loadingInvoices}
        />
      )}
    </div>
  );
};
