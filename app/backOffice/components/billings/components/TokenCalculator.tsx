import React, { useState } from 'react';
import { FaCalculator, FaGem, FaTimes, FaPercent, FaCog, FaArrowDown } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import PaymentMethod from './PaymentMethod';
import { toNumber } from 'lodash';
import { useTranslation } from 'react-i18next';
// Keep import for future use
// import CryptoTokens from './CryptoTokens';

interface TokenCalculatorProps {
  showTokenCalculator: boolean;
  setShowTokenCalculator: (show: boolean) => void;
  activeTopUp: {
    rate: number;
    volumeDiscounts: Array<{
      threshold: string;
      discount: number;
    }>;
    bulkPurchase: {
      minimum: string;
      maximum: string;
    };
  } | null;
  onPurchase: (tokens: string) => void;
}

type Unit = 'Millions' | '';

const TokenCalculator: React.FC<TokenCalculatorProps> = ({
  showTokenCalculator,
  setShowTokenCalculator,
  activeTopUp,
  onPurchase,
}) => {
  const [customTokens, setCustomTokens] = useState<string>('');
  const [selectedUnit, setSelectedUnit] = useState<Unit>('Millions');
  const [error, setError] = useState<string>('');
  // Default to card payment only
  const [paymentMethod, setPaymentMethod] = useState<'card'>('card');

  const calculateDiscount = (amount: number): number => {
    if (!activeTopUp?.volumeDiscounts) return 0;

    const applicableDiscount = activeTopUp.volumeDiscounts
      .sort((a, b) => Number(b.threshold) - Number(a.threshold))
      .find((discount) => amount >= Number(discount.threshold));

    return applicableDiscount ? applicableDiscount.discount : 0;
  };

  const calculateTokenPrice = (tokens: string | number): number => {
    const tokenAmount = Number(tokens);
    if (!tokenAmount) return 0;

    const basePrice = (activeTopUp && (tokenAmount * activeTopUp.rate) / 1000000) || 0;
    const discount = calculateDiscount(tokenAmount);
    return basePrice * (1 - discount / 100);
  };

  const convertToRawNumber = (value: string, unit: Unit): number => {
    const numValue = Number(value);

    switch (unit) {
      case 'Millions':
        return numValue * 1000000;
      default:
        return numValue;
    }
  };

  const calculateMinimumTokensForPrice = (targetPrice: number): number => {
    if (!activeTopUp?.rate) return 0;

    let left = 1;
    let right = 1000000000;
    let result = right;

    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      const price = calculateTokenPrice(mid);

      if (price >= targetPrice) {
        result = mid;
        right = mid - 1;
      } else {
        left = mid + 1;
      }
    }

    return result;
  };

  const validateTokenAmount = (amount: string, unit: Unit) => {
    if (!amount) return false;

    const rawAmount = convertToRawNumber(amount, unit);
    const price = calculateTokenPrice(rawAmount);
    const minTokensForDollar = calculateMinimumTokensForPrice(1);
    const minimumTokens = Math.max(1000000, minTokensForDollar); // 1 Million minimum or enough for $1
    const maximumTokens = 1000000000; // 1 Billion maximum

    if (rawAmount < minimumTokens) {
      setError(t('minimumTokenError', {
        minTokens: formatNumber(minimumTokens),
        defaultValue: `Minimum token purchase is ${formatNumber(minimumTokens)}`
      }));

      return false;
    }

    if (activeTopUp?.bulkPurchase.minimum && activeTopUp?.bulkPurchase.maximum) {
      const min = Math.max(Number(activeTopUp.bulkPurchase.minimum), minimumTokens);
      const max = Math.max(Number(activeTopUp.bulkPurchase.maximum), maximumTokens);

      if (rawAmount < min) {
        setError(t('minimumBulkPurchaseError', {
          amount: formatNumber(min),
          defaultValue: `Minimum token purchase is ${formatNumber(min)}`
        }));

        return false;
      }

      if (max && rawAmount > max) {
        setError(t('maximumBulkPurchaseError', {
          amount: formatNumber(max),
          defaultValue: `Maximum token purchase is ${formatNumber(max)}`
        }));

        return false;
      }
    }

    setError('');

    return true;
  };

  const { t } = useTranslation('profile');

  const handleTokenChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setCustomTokens(value);
    if (value) validateTokenAmount(value, selectedUnit);
  };

  const handleUnitChange = (unit: Unit) => {
    if (customTokens) {
      const currentRawValue = convertToRawNumber(customTokens, selectedUnit);
      let newValue = currentRawValue;

      if (unit === 'Millions') {
        newValue = currentRawValue / 1000000;
      } else {
        newValue = currentRawValue;
      }

      setCustomTokens(newValue.toString());
      setSelectedUnit(unit);
      validateTokenAmount(newValue.toString(), unit);
    } else {
      setSelectedUnit(unit);
    }
  };

  const formatNumber = (num: string | number): string => {
    if (!num) return '0';

    const number = typeof num === 'string' ? parseFloat(num) : num;

    if (number < 1000000) {
      return Math.ceil(number * 10) / 10 + '';
    }

    const millions = number / 1000000;
    const formattedMillions = (Math.ceil(millions * 10) / 10).toFixed(1);

    // Check if the number is exactly 1.0 million
    if (formattedMillions === '1.0') {
      return `${formattedMillions} ${t('million', 'Million')}`;
    }

    return `${formattedMillions} ${t('millions', 'Millions')}`;
  };

  const handlePurchase = () => {
    if (validateTokenAmount(customTokens, selectedUnit)) {
      const rawAmount = convertToRawNumber(customTokens, selectedUnit);
      onPurchase(rawAmount.toString());
    }
  };

  const getApplicableDiscount = () => {
    const rawAmount = convertToRawNumber(customTokens, selectedUnit);
    return calculateDiscount(rawAmount);
  };

  const renderDiscountTiers = () => {
    if (!activeTopUp?.volumeDiscounts || !customTokens) return null;

    if (activeTopUp.volumeDiscounts.every((discount) => discount.discount === 0)) return null;

    const rawAmount = convertToRawNumber(customTokens, selectedUnit);
    const sortedDiscounts = [...activeTopUp.volumeDiscounts].sort((a, b) => Number(a.threshold) - Number(b.threshold));

    const filteredDiscounts = sortedDiscounts.filter((discount) => discount.discount > 0);
    return (
      <div className="space-y-2">
        <div className="flex items-center gap-2 text-sm text-gray-300 mb-3">
          <div className="p-1.5 rounded-lg bg-green-500/10 ring-1 ring-green-500/20">
            <FaPercent className="text-green-400" />
          </div>
          <span className={'text-[16px] font-light'}>
            {t('availableDiscounts', 'Available Discounts')}
          </span>
        </div>
        <div className="grid grid-cols-1 gap-2">
          {filteredDiscounts.map((discount, index) => {
            const isApplicable = rawAmount >= Number(discount.threshold);
            const isNextTier =
              rawAmount < Number(discount.threshold) &&
              (!filteredDiscounts[index - 1] || rawAmount >= Number(sortedDiscounts[index - 1].threshold));

            return (
              <motion.div
                key={discount.threshold}
                initial={false}
                animate={{ scale: isApplicable ? 1.02 : 1 }}
                className={`p-4 rounded-xl transition-all duration-300 ${
                  isApplicable
                    ? 'bg-gradient-to-br from-green-500/20 to-green-600/10 ring-1 ring-green-500/30'
                    : isNextTier
                      ? 'bg-[#0A1730]/80 ring-1 ring-gray-600/30'
                      : 'bg-[#0A1730]/50'
                }`}
              >
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${isApplicable ? 'bg-green-400' : 'bg-gray-500'}`} />
                    <span className="text-[16px] font-light">{formatNumber(discount.threshold)}+ tokens</span>
                  </div>
                  <span className={`font-light ${isApplicable ? 'text-green-400' : 'text-gray-400'}`}>
                    {discount.discount}% off
                  </span>
                </div>
                {isNextTier && !isApplicable && (
                  <div className="text-xs text-gray-400 mt-2 pl-4">
                    {formatNumber(Number(discount.threshold) - rawAmount)} more tokens needed
                  </div>
                )}
              </motion.div>
            );
          })}
        </div>
      </div>
    );
  };

  // Payment method section modified to only show card as active option
  const renderPaymentMethods = () => (
    <div className="space-y-2">
      <div className="flex items-center gap-2 text-sm text-gray-300 mb-3">
        <div className="p-1.5 rounded-lg bg-green-500/10 ring-1 ring-green-500/20">
          <FaGem className="text-green-400" />
        </div>
        <span className={'text-[16px] font-light'}>
          {t('paymentMethod', 'Payment Method')}
        </span>
      </div>
      <div className="grid grid-cols-1 gap-3">
        <PaymentMethod method="card" isSelected={true} onClick={() => {}} />
        {/* Crypto payment option commented out but preserved for future use
        <PaymentMethod
          method="crypto"
          isSelected={paymentMethod === 'crypto'}
          onClick={() => setPaymentMethod('crypto')}
        />
        */}
      </div>
    </div>
  );

  const renderUnitSelector = () => {
    const value = Number(customTokens);
    const unitLabel = value === 1 || value ===0 ? t('million', 'Million') : t('millions', 'Millions');

    return (
      <div className="absolute right-4 top-1/2 -translate-y-1/2 flex items-center gap-2">
        <div className="relative">
          <select
            value={selectedUnit}
            onChange={(e) => handleUnitChange(e.target.value as Unit)}
            className="appearance-none bg-[#0A1730]/80 text-green-400 px-3 py-2 pr-8 rounded-lg focus:outline-none cursor-pointer border border-gray-700/30 hover:border-gray-600/50 transition-colors"
          >
            <option value="Millions">{unitLabel}</option>
          </select>
          <FaArrowDown className="absolute right-2 top-1/2 -translate-y-1/2 text-green-400 text-xs pointer-events-none" />
        </div>
      </div>
    );
  };

  const renderUnconfiguredState = () => (
    <div className="text-center space-y-8">
      <div className="relative">
        <div className="w-24 h-24 mx-auto bg-green-500/10 rounded-2xl flex items-center justify-center ring-1 ring-green-500/20">
          <FaCog className="text-5xl text-green-400 animate-spin-slow" />
        </div>
      </div>
      <div className="space-y-4">
        <h3 className="text-2xl font-light">
          <span className="bg-gradient-to-r from-green-400 to-emerald-500 bg-clip-text text-transparent">
            {t('tokenTopUpComingSoon', 'Token Top-Up Coming Soon')}
          </span>
        </h3>
        <div className="space-y-3">
          <p className="text-gray-200 font-light max-w-sm mx-auto leading-relaxed">
            {t('finalisingPricing', 'We\'re finalizing our token pricing system to provide you with the best rates and volume discounts.')}
          </p>
          <p className="text-gray-300 font-light max-w-sm mx-auto leading-relaxed">
            {t('getReady', 'Get ready for flexible token packages that will power your development needs.')}
          </p>
        </div>
      </div>
      <div className="pt-4">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setShowTokenCalculator(false)}
          className="px-8 py-3 bg-green-500/10 rounded-xl text-green-400 hover:text-green-300 transition-colors ring-1 ring-green-500/20 hover:ring-green-500/30 hover:bg-green-500/20"
        >
          {t('gotIt', 'Got It')}
        </motion.button>
        <p className="text-gray-400 text-sm mt-3">
          {t('checkBackSoon', 'Check back soon for token purchases')}
        </p>
      </div>
    </div>
  );

  return (
    <AnimatePresence>
      {showTokenCalculator && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4 mt-0!"
        >
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            className="bg-[#0A1730] rounded-2xl p-4 md:p-8 max-w-4xl w-full mx-4 border border-gray-700/30 shadow-2xl backdrop-blur-sm"
          >
            <div className="flex justify-between items-center mb-4 md:mb-8">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-gradient-to-br from-green-500/20 to-green-600/10 rounded-xl ring-1 ring-green-500/30">
                  <FaCalculator className="text-2xl text-green-400" />
                </div>
                <h3 className="text-[18px] md:text-2xl font-light bg-gradient-to-br from-white to-gray-400 bg-clip-text text-transparent">
                  {t('buyExtraTokens', ' Buy Extra Tokens')}
                </h3>
              </div>
              <motion.button
                whileHover={{ scale: 1.1, rotate: 90 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => {
                  setShowTokenCalculator(false);
                  setCustomTokens('');
                  setError('');
                }}
                className="p-2 rounded-lg text-gray-400 hover:text-white transition-all duration-300 bg-transparent"
              >
                <FaTimes className="text-xl" />
              </motion.button>
            </div>

            {!activeTopUp?.rate ? (
              renderUnconfiguredState()
            ) : (
              /* Crypto tokens section commented out but preserved for future use
              paymentMethod === 'crypto' ? (
                <CryptoTokens onBack={() => setPaymentMethod('card')} />
              ) : (
              */
              <div className="space-y-8">
                {renderPaymentMethods()}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 md:gap-8">
                  <div>
                    <div className="flex items-center gap-2 text-sm text-gray-300 mb-3">
                      <div className="p-1.5 rounded-lg bg-green-500/10 ring-1 ring-green-500/20">
                        <FaCalculator className="text-green-400" />
                      </div>
                      <span className={'text-[16px] font-light'}>
                        {t('howManyTokens', 'How many tokens would you like?')}{' '}
                        {activeTopUp?.bulkPurchase.minimum &&
                          toNumber(activeTopUp?.bulkPurchase?.minimum) > 0 &&
                          activeTopUp?.bulkPurchase.maximum &&
                          toNumber(activeTopUp?.bulkPurchase.maximum) > 0 && (
                            <>
                              ({formatNumber(activeTopUp?.bulkPurchase.minimum)} -{' '}
                              {formatNumber(activeTopUp?.bulkPurchase.maximum)})
                            </>
                          )}
                      </span>
                    </div>
                    <div className="relative group">
                      <input
                        type="number"
                        value={customTokens}
                        onChange={handleTokenChange}
                        className={`w-full bg-[#0A1730]/80 backdrop-blur-sm rounded-xl px-5 py-4 text-white focus:outline-none ring-1 ${
                          error ? 'ring-red-500/50' : 'ring-gray-700/30 focus:ring-green-500/50'
                        } font-light transition-all group-hover:ring-gray-600/50`}
                        placeholder={t('placeholderEnterTokens', 'Enter amount of tokens...')}
                      />
                      {renderUnitSelector()}
                    </div>
                    {error && <p className="text-red-400 text-sm mt-2">{error}</p>}
                  </div>

                  {customTokens && <div>{renderDiscountTiers()}</div>}
                </div>

                <AnimatePresence>
                  {customTokens && !error && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      className="bg-[#0A1730]/80 rounded-xl px-6 py-2 md:p-6 ring-1 ring-gray-700/30"
                    >
                      <div className="space-y-4">
                        <div className="flex justify-between items-center pb-4 border-b border-gray-700/30">
                          <span className="text-gray-300 font-light">
                            {t('priceLabel', 'Price:')}
                          </span>
                          <span className="text-2xl text-green-400 font-light max-w-[200px] truncate">
                            ${calculateTokenPrice(convertToRawNumber(customTokens, selectedUnit)).toFixed(2)}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-300 font-light">Tokens:</span>
                          <span className="text-2xl text-green-400 font-light max-w-[200px] truncate text-right">
                            {formatNumber(convertToRawNumber(customTokens, selectedUnit))}
                          </span>
                        </div>
                        {getApplicableDiscount() > 0 && (
                          <div className="flex justify-between items-center pt-2 border-t border-gray-700/30">
                            <span className="text-gray-300 font-light">
                              {t('discountAppliedLabel', 'Discount Applied:')}
                            </span>
                            <span className="text-green-400 font-light">{getApplicableDiscount()}%</span>
                          </div>
                        )}
                        <div className="text-sm text-gray-400 font-light pt-2 flex items-center gap-2">
                          <FaGem className="text-green-500/50" />
                          <span className={'text-[16px] font-light'}>
                             {t('at', 'At')} ${activeTopUp && activeTopUp.rate}  {t('perMillion', 'per million tokens')}
                          </span>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                <motion.button
                  whileHover={{ scale: error || !customTokens ? 1 : 1.02 }}
                  whileTap={{ scale: error || !customTokens ? 1 : 0.98 }}
                  disabled={!!error || !customTokens}
                  className={`w-full py-4 rounded-xl transition-all font-light text-lg ${
                    error || !customTokens
                      ? 'bg-gray-700/50 cursor-not-allowed shadow-none text-gray-400'
                      : 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 shadow-lg shadow-green-500/20'
                  }`}
                  onClick={handlePurchase}
                >
                  {t('purchaseTokens', 'Purchase Tokens')}
                </motion.button>
              </div>
              /*)*/
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default TokenCalculator;
