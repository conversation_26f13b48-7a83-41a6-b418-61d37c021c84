export const formatNumber = (num: string): string => {
  if (!num) return '0';

  const number = parseFloat(num);

  if (number >= 1000000000) {
    return (number / 1000000000).toFixed(1) + 'B';
  }
  if (number >= 1000000) {
    return (number / 1000000).toFixed(1) + 'M';
  }
  if (number >= 1000) {
    return (number / 1000).toFixed(1) + 'K';
  }

  return number.toString();
};

import { FaGem, FaCrown, FaStar, FaRocket, FaAward, FaMedal, FaTrophy } from 'react-icons/fa';
import { FaDiamond } from 'react-icons/fa6';
import { ReactElement } from 'react';

export const AVAILABLE_ICONS: { icon: React.ElementType; name: string }[] = [
  { icon: FaGem, name: 'Gem' },
  { icon: FaCrown, name: 'Crown' },
  { icon: FaStar, name: 'Star' },
  { icon: FaRocket, name: 'Rocket' },
  { icon: FaDiamond, name: '<PERSON>' },
  { icon: Fa<PERSON><PERSON>, name: 'Award' },
  { icon: FaMedal, name: 'Medal' },
  { icon: FaTrophy, name: 'Trophy' },
];

export const FeatureDescriptions: Record<string, string> = {
  BASIC_AI: 'Basic AI Development',
  COMMUNITY_SUPPORT: 'Community Support',
  STANDARD_RESPONSE: 'Standard Response Time',
  BASIC_TEMPLATES: 'Basic Templates',
  ADVANCED_AI: 'Advanced AI Development',
  PRIORITY_SUPPORT: 'Priority Support',
  FAST_RESPONSE: 'Fast Response Time',
  PREMIUM_TEMPLATES: 'Premium Templates',
  TEAM_COLLABORATION: 'Team Collaboration',
  ENTERPRISE_AI: 'Enterprise AI Development',
  SUPPORT_24_7: '24/7 Priority Support',
  INSTANT_RESPONSE: 'Instant Response Time',
  CUSTOM_TEMPLATES: 'Custom Templates',
  ADVANCED_TEAM: 'Advanced Team Features',
  CUSTOM_INTEGRATIONS: 'Custom Integrations',
  BIG_CONTEXT_WINDOW: 'Big Context Window - 5x Larger than the Medium one',
  CODE_CHAT_AI: 'Access to the Code & Chat AI',
  TOKENS_BASIC: 'Enough tokens for approx. 3 presentation websites',
  STANDARD_WEBCONTAINER: 'Dedicated Standard Webcontainer',
  MEDIUM_CONTEXT_WINDOW: 'Medium Context Window - Suitable for a website, a blog, or basic browser game',
  BASIC_SUPPORT: 'Basic Support with Standard Response Time',
  SUPABASE_INTEGRATION: 'Supabase Integration',
  PROJECT_SHARING: 'Share Projects with Your Friends',
  PROJECT_DOWNLOAD: 'Download Your Projects',
  PROJECT_DEPLOYMENT: 'Deployment Feature',
  CUSTOM_DOMAIN: 'Connect Projects to Your Own Domain',
  TOKENS_CREATOR_PLUS: 'Enough tokens for approx. 7 presentation websites',
  ADVANCED_SUPPORT: 'Advanced Support with Faster Response Time',
  PRIORITARY_SUPPORT: 'Dedicated Prioritary Support',
  EARLY_FEATURE_ACCESS: 'Early Access to New Features',
  HUMAN_CTO: 'Dedicated Human CTO for Your Business',
  COMMUNITY_PROMOTION: 'Promote Your Business with Our Community',
};
