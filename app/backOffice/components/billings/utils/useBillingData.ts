import { useState, useEffect } from 'react';
import { Plan, TopUp } from '~/types/billing';
import { fetchPlans, fetchTopUps } from '~/lib/stores/billing';

type BillingCycle = 'monthly' | 'yearly';

export const useBillingData = () => {
  const [billingCycle, setBillingCycle] = useState<BillingCycle>('yearly');
  const [stripePlans, setStripePlans] = useState<Plan[]>([]);
  const [activeTopUp, setActiveTopUp] = useState<TopUp | null>(null);
  const [plansLoading, setPlansLoading] = useState<boolean>(true);
  const [loadingTopups, setLoadingTopups] = useState<boolean>(true);

  useEffect(() => {
    const loadPlans = async (): Promise<void> => {
      try {
        setPlansLoading(true);
        const plans = await fetchPlans(billingCycle);
        setStripePlans(plans);
      } catch (error) {
        console.error('Error loading plans:', error);
      } finally {
        setPlansLoading(false);
      }
    };

    void loadPlans();
  }, [billingCycle]);

  useEffect(() => {
    const loadTopUps = async () => {
      try {
        setLoadingTopups(true);
        const data = await fetchTopUps();
        setActiveTopUp(data);
      } catch (error) {
        console.error('Error fetching top-ups:', error);
      } finally {
        setLoadingTopups(false);
      }
    };

    void loadTopUps();
  }, []);

  return {
    billingCycle,
    setBillingCycle,
    stripePlans,
    plansLoading,
    activeTopUp,
    loadingTopups,
  };
};
