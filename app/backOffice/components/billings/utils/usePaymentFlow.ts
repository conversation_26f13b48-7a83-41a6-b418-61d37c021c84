import { useState, useEffect } from 'react';
import { InvoiceData, UsePaymentFlowProps } from '~/types/billing';
import { createCheckoutSession, fetchLastReceipt } from '~/lib/stores/billing';

export const usePaymentFlow = ({ loadingTopups, plansLoading, notLoadedInvoices }: UsePaymentFlowProps) => {
  const [showInvoiceModal, setShowInvoiceModal] = useState<boolean>(false);
  const [showThankYouModal, setShowThankYouModal] = useState<boolean>(false);
  const [showTokenCalculator, setShowTokenCalculator] = useState<boolean>(false);
  const [selectedInvoice, setSelectedInvoice] = useState<InvoiceData | null>(null);
  const [receiptData, setReceiptData] = useState<any | null>(null);

  const handleTokenPurchase = async (tokens: string): Promise<void> => {
    await createCheckoutSession({
      mode: 'payment',
      tokens,
    });
  };

  useEffect(() => {
    const queryParams = new URLSearchParams(window.location.search);
    const paymentType = queryParams.get('paymentType');

    if (
      paymentType &&
      (paymentType === 'topup' || paymentType === 'subscription') &&
      !loadingTopups &&
      !plansLoading &&
      !notLoadedInvoices
    ) {
      const fetchReceiptData = async () => {
        try {
          const receiptData = await fetchLastReceipt(paymentType);
          setReceiptData(receiptData);
          setShowThankYouModal(true);

          const newUrl =
            window.location.pathname +
            (queryParams.toString()
              ? '?' + queryParams.toString().replace(/paymentType=(topup|subscription)&?/g, '')
              : '');

          window.history.replaceState({ path: newUrl }, '', newUrl);
        } catch (error) {
          console.error('Failed to fetch receipt data:', error);
        }
      };

      void fetchReceiptData();
    }
  }, [loadingTopups, plansLoading, notLoadedInvoices]);

  return {
    showInvoiceModal,
    setShowInvoiceModal,
    showThankYouModal,
    setShowThankYouModal,
    showTokenCalculator,
    setShowTokenCalculator,
    selectedInvoice,
    setSelectedInvoice,
    receiptData,
    handleTokenPurchase,
  };
};
