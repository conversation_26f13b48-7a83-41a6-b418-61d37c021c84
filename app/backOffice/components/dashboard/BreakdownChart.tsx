import React from 'react';

interface Breakdown {
  setup: number;
  components: number;
  pages: number;
  styling: number;
  testing: number;
  database?: number;
  backend?: number;
  [key: string]: number | undefined;
}

interface BreakdownChartProps {
  breakdown: Breakdown;
}

const BreakdownChart: React.FC<BreakdownChartProps> = ({ breakdown }) => {
  const colors: Record<string, string> = {
    setup: '#60A5FA',      // Blue
    components: '#A78BFA', // Purple
    pages: '#F472B6',      // Pink
    styling: '#4ADE80',    // Green
    testing: '#FBBF24',    // Yellow
    database: '#F97316',   // Orange
    backend: '#8B5CF6'     // Violet
  };

  // Calculate the total hours (safely accounting for potential undefined values)
  const totalHours = Object.values(breakdown).reduce((acc, hours) => acc + (hours || 0), 0);

  // Filter out breakdown items with 0 hours and sort them in descending order
  const breakdownItems = Object.entries(breakdown)
    .filter(([_, hours]) => (hours || 0) > 0)
    .sort((a, b) => (b[1] || 0) - (a[1] || 0));

  return (
    <div className="space-y-1.5 sm:space-y-2">
      {breakdownItems.map(([category, hours]) => {
        const hrValue = hours || 0;
        // Use Math.round for integer percentage values (or .toFixed(0) if you prefer strings)
        const percentage = totalHours > 0 ? Math.round((hrValue / totalHours) * 100) : 0;
        return (
          <div key={category}>
            <div className="flex justify-between text-xs mb-0.5 sm:mb-1">
              <span className="capitalize text-xs lg:text-sm text-gray-400 font-light">
                {category}
              </span>
              <span className="text-white text-xs lg:text-sm">
                {hrValue}h ({percentage}%)
              </span>
            </div>
            <div className="w-full bg-black bg-opacity-30 rounded-full h-1.5 sm:h-2">
              <div
                className="h-1.5 sm:h-2 rounded-full"
                style={{
                  width: `${percentage}%`,
                  backgroundColor: colors[category] || '#6B7280'
                }}
              ></div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default BreakdownChart;
