import React from 'react';

type ContestIconButtonProps = {
  icon: React.ReactNode;
  onClick: () => void;
  variant?: "primary" | "warning" | "danger" | "download" | "contest" | "info" | "secondary";
  tooltip?: string;
  customStyleTooltip: any | undefined;
  text?: string;
};

const ContestIconButton: React.FC<ContestIconButtonProps> = ({
  icon,
  onClick,
  customStyleTooltip= undefined,
  variant = "secondary",
  tooltip,
  text,
}) => {
  const getButtonClass = () => {
    switch (variant) {
      case "primary":
        return "project-action-primary";
      case "warning":
        return "project-action-warning";
      case "danger":
        return "project-action-danger pulse-on-hover";
      case "download":
        return "project-action-download";
      case "contest":
        return "project-action-contest";
      case "info":
        return "project-action-info";
      case "secondary":
      default:
        return "project-action-secondary";
    }
  };

  return (
    <button
      className={`${getButtonClass()} group !ml-0 transition-all`}
      onClick={onClick}
      aria-label={tooltip || text}
    >
      <span className="flex items-center justify-center">
        {icon}
        {text && <span className="ml-2 font-light text-sm">{text}</span>}
      </span>

      {tooltip && !text && <div className="custom-tooltip" style={customStyleTooltip}>{tooltip}</div>}
    </button>
  );
};

export default ContestIconButton;
