import React, { useEffect, useState } from 'react';
import { deleteContestProject, fetchContestProjects } from '~/api/projectsApi';
import {
  AlertCircle,
  Award,
  Check,
  ChevronDown,
  ChevronUp,
  ExternalLink,
  Heart,
  Link,
  Shield,
  Trash2,
  Trophy,
  Users
} from 'lucide-react';
import DeleteConfirmationModal from './DeleteConfirmationModal';
import { useTranslation } from 'react-i18next';

type Submission = {
  id: string;
  name: string;
  screenshot: string;
  likes: number;
  projectSlug: string;
  submissions: any[];
  url: string;
};

type ContestStatusProps = {
  updatePublishedProjects: (projectSlug: string, isPublished: boolean) => void;
  refreshContestProjects: number;
};

const ContestStatus: React.FC<ContestStatusProps> = ({ updatePublishedProjects, refreshContestProjects }) => {
  const { t } = useTranslation();
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [conditionsProgress, setConditionsProgress] = useState({ completed: 0, total: 0 });
  const [deleteModalData, setDeleteModalData] = useState<{
    isOpen: boolean;
    projectSlug: string | null;
    projectName: string;
  }>({
    isOpen: false,
    projectSlug: null,
    projectName: '',
  });

  const [isConditionsExpanded, setIsConditionsExpanded] = useState(false);
  const [qualifyingConditions, setQualifyingConditions] = useState({
    basicConditions: [
      { id: 'verified', label: t('verifiedAccount', 'Verified account'), completed: true, icon: Shield },
      {
        id: 'submitted',
        label: t('hasSubmittedAMinimumOfOneProject', 'Has submitted a minimum of one project'),
        completed: true,
        icon: Trophy,
      },
    ],
    topPlacesConditions: [
      {
        id: 'referrals',
        label: t('haveAtLeastActiveReferrals', 'Have at least 3 active referrals', { number: 3 }),
        completed: false,
        icon: Users,
        progress: '1/3',
        actionLabel: t('GoToAffiliateDashBoard', 'Go to affiliate dashboard'),
        actionIcon: Link,
        actionHandler: () => window.open('https://biela.dev/affiliate', '_blank'),
      },
      {
        id: 'liked',
        label: t('LikeOneProjectThatBelongsToAnotherUser', 'Liked one project that belongs to another user'),
        completed: false,
        icon: Heart,
        actionLabel: t('GoToHackathonPage', 'Go to Hackathon page'),
        actionIcon: Award,
        actionHandler: () => window.open('https://biela.dev/hackathon', '_blank'),
      },
    ],
  });

  useEffect(() => {
    const fetchProjectsAndQualifications = async () => {
      try {
        const data = await fetchContestProjects();

        const formattedSubmissions = data.projects.map((project: any) => ({
          id: project.projectId,
          name: project.projectName,
          screenshot: project.screenshotUrl2 || project.screenshotUrl,
          likes: project.likesCount,
          url: project.projectUrl,
          projectSlug: project.projectSlug,
        }));
        setSubmissions(formattedSubmissions);

        const qualifications = data.qualification || {};
        const basicConditions = [
          {
            id: 'verified',
            label: t('verifiedAccount', 'Verified account'),
            completed: qualifications.verified || false,
            icon: Shield,
          },
          {
            id: 'submitted',
            label: t('hasSubmittedAMinimumOfOneProject', 'Has submitted a minimum of one project'),
            completed: qualifications.submit || false,
            icon: Trophy,
          },
        ];

        const referralsProgress = Math.min((qualifications.referrals / 3) * 100, 100);
        const topPlacesConditions = [
          {
            id: 'referrals',
            label: t('haveAtLeastActiveReferrals', 'Have at least 3 active referrals', { number: 3 }),
            completed: qualifications.referrals >= 3,
            icon: Users,
            progress: `${qualifications.referrals}/3`,
            progressBarWidth: `${referralsProgress}%`,
            actionLabel: t('GoToAffiliateDashBoard', 'Go to affiliate dashboard'),
            actionIcon: Link,
            actionHandler: () => window.open('https://biela.dev/affiliate', '_blank'),
          },
          {
            id: 'liked',
            label: t('LikeOneProjectThatBelongsToAnotherUser', 'Liked one project that belongs to another user'),
            completed: qualifications.like || false,
            icon: Heart,
            actionLabel: t('GoToHackathonPage', 'Go to Hackathon page'),
            actionIcon: Award,
            actionHandler: () => window.open('https://biela.dev/hackathon', '_blank'),
          },
        ];

        setQualifyingConditions({ basicConditions, topPlacesConditions });

        const totalConditions = basicConditions.length + topPlacesConditions.length;
        const completedConditions =
          basicConditions.filter((c) => c.completed).length + topPlacesConditions.filter((c) => c.completed).length;

        setConditionsProgress({ completed: completedConditions, total: totalConditions });
      } catch (error) {
        console.error('Error fetching contest projects and qualifications:', error);
      }
    };

    fetchProjectsAndQualifications();
  }, [refreshContestProjects]);

  const openDeleteModal = (projectSlug: string, projectName: string) => {
    setDeleteModalData({
      isOpen: true,
      projectSlug,
      projectName,
    });
  };

  const closeDeleteModal = () => {
    setDeleteModalData({
      isOpen: false,
      projectSlug: null,
      projectName: '',
    });
  };

  const confirmDelete = async () => {
    if (!deleteModalData.projectSlug) {
      return;
    }

    try {
      await deleteContestProject(deleteModalData.projectSlug);
      setSubmissions((prevSubmissions) =>
        prevSubmissions.filter((submission) => submission.projectSlug !== deleteModalData.projectSlug),
      );
      updatePublishedProjects(deleteModalData.projectSlug, false);
      closeDeleteModal();
    } catch (error) {
      console.error('Error deleting project:', error);
      alert('Failed to delete the project. Please try again.');
    }
  };

  const submissionProgress = (submissions.length / 3) * 100;

  const toggleConditionsExpansion = () => {
    setIsConditionsExpanded(!isConditionsExpanded);
  };

  return (
    <div className="mb-8 bg-gradient-to-br from-purple-900/50 to-indigo-900/50 border border-purple-600/30 rounded-xl p-3 sm:p-4 md:p-6 shadow-lg relative overflow-hidden">
      {/* Enhanced decorative background elements */}
      <div className="absolute -top-24 -right-24 w-48 h-48 bg-purple-500/20 rounded-full blur-3xl"></div>
      <div className="absolute -bottom-16 -left-16 w-40 h-40 bg-indigo-500/20 rounded-full blur-3xl"></div>
      <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-blue-500/10 rounded-full blur-2xl"></div>

      {/* Enhanced header with trophy icon */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 sm:mb-6 relative">
        <div className="flex items-center mb-3 sm:mb-0">
          <div className="mr-3 sm:mr-4 bg-gradient-to-br from-purple-500/30 to-indigo-500/30 p-2 sm:p-2.5 rounded-lg shadow-md border border-purple-500/20">
            <Trophy size={20} className="text-purple-300" strokeWidth={1.5} />
          </div>
          <div>
            <h2 className="text-lg sm:text-xl font-light text-white mb-0.5 sm:mb-1">
              {t('VibeCodingHackathonStatus', 'Vibe Coding Hackathon Status')}
            </h2>
            <p className="text-purple-200/90 font-light text-sm sm:text-base">
              {t('ShowcaseYourBestWork', 'Showcase your best work')}
            </p>
          </div>
        </div>

        <div className="flex flex-col items-start sm:items-end w-full sm:w-auto">
          <span className="bg-gradient-to-r from-purple-600/30 to-indigo-600/30 text-white px-3 py-1 sm:px-3.5 sm:py-1.5 rounded-full text-sm sm:text-base font-light mb-1.5 border border-purple-500/20 shadow-sm">
            {submissions.length}/3 {t('submissions', 'submissions')}
          </span>
          <div className="w-full sm:w-36 h-2 bg-[#334155]/40 rounded-full overflow-hidden shadow-inner">
            <div
              className="h-full bg-gradient-to-r from-purple-400 to-indigo-400 rounded-full"
              style={{ width: `${submissionProgress}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Qualifying conditions section */}
      <div className="mb-4 sm:mb-6 bg-gradient-to-b from-[#1E293B] to-[#1E293B]/90 rounded-xl border border-purple-500/30 shadow-md relative overflow-hidden">
        <div className="absolute top-0 right-0 w-40 h-40 bg-purple-500/10 rounded-full blur-3xl pointer-events-none"></div>
        <div className="absolute bottom-0 left-0 w-40 h-40 bg-indigo-500/10 rounded-full blur-3xl pointer-events-none"></div>

        <div
          className={`relative z-10 flex items-center justify-between cursor-pointer transition-all duration-300 flex-wrap gap-2 ${
            isConditionsExpanded ? 'p-4 sm:p-5 border-b border-purple-500/20' : 'p-3 sm:p-3.5'
          }`}
          onClick={toggleConditionsExpansion}
        >
          <div className="flex items-center">
            <div
              className={`flex-shrink-0 bg-gradient-to-br from-purple-500/20 to-indigo-500/20 rounded-lg shadow-md border border-purple-500/20 transition-all ${
                isConditionsExpanded ? 'mr-3 sm:mr-4 p-1.5 sm:p-2' : 'mr-2 sm:mr-3 p-1 sm:p-1.5'
              }`}
            >
              <Shield size={isConditionsExpanded ? 16 : 14} className="text-purple-300" strokeWidth={1.5} />
            </div>
            <h3
              className={`font-semibold transition-all ${isConditionsExpanded ? 'text-lg sm:text-xl' : 'text-sm sm:text-base'}`}
            >
              <span className="bg-clip-text font-light text-transparent bg-gradient-to-r from-purple-200 to-indigo-200 drop-shadow-sm">
                {t('qualifyConditions', 'Qualifying Conditions')}
              </span>
            </h3>
          </div>

          <div className="flex items-center">
            <div
              className={`bg-gradient-to-r from-purple-600/20 to-indigo-600/20 text-white rounded-full text-sm sm:text-base font-light border border-purple-500/20 shadow-sm transition-all ${
                isConditionsExpanded
                  ? 'mr-2 sm:mr-3 px-2 sm:px-3 py-0.5 sm:py-1 text-xs sm:text-sm'
                  : 'mr-1 sm:mr-2 px-2 sm:px-2.5 py-0.5 text-xs sm:text-sm'
              }`}
            >
              {conditionsProgress.completed}/{conditionsProgress.total} {t('completed', 'completed')}
            </div>

            <button
              className={`bg-[#334155]/30 hover:bg-[#334155]/40 rounded-full transition-colors ${
                isConditionsExpanded ? 'p-1.5 sm:p-2' : 'p-1 sm:p-1.5'
              }`}
              aria-label={
                isConditionsExpanded
                  ? t('collapseQualifyConditions', 'Collapse qualifying conditions')
                  : t('expandQualifyConditions', 'Expand qualifying conditions')
              }
            >
              {isConditionsExpanded ? (
                <ChevronUp size={16} className="text-purple-300" strokeWidth={1.5} />
              ) : (
                <ChevronDown size={14} className="text-purple-300" strokeWidth={1.5} />
              )}
            </button>
          </div>
        </div>

        {isConditionsExpanded && (
          <div className="p-4 sm:p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
              {/* Basic qualifying conditions */}
              <div className="bg-gradient-to-br from-[#0F172A]/60 to-[#0F172A]/40 backdrop-blur-sm p-4 sm:p-5 rounded-xl border border-purple-500/20 shadow-md relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-transparent pointer-events-none"></div>
                <h4 className="text-sm sm:text-base font-light text-purple-200 mb-3 sm:mb-4 flex items-center">
                  <div className="h-1.5 w-1.5 bg-purple-300 rounded-full mr-2 sm:mr-2.5"></div>
                  {t('basicParticipation', 'Basic Participation')}
                </h4>
                <ul className="space-y-3 sm:space-y-4">
                  {qualifyingConditions.basicConditions.map((condition) => (
                    <li key={condition.id} className="group">
                      <div
                        className={`flex items-center max-[400px]:flex-col max-[400px]:items-start flex-wrap gap-2 px-3 sm:px-4 py-2.5 sm:py-3.5 rounded-xl transition-all duration-300 ${
                          condition.completed
                            ? 'bg-gradient-to-r from-[#10B981]/10 to-[#10B981]/5 border-l-[3px] border-[#10B981] shadow-md'
                            : 'bg-[#334155]/20 hover:bg-[#334155]/30'
                        }`}
                      >
                        <div
                          className={`flex-shrink-0 p-1.5 sm:p-2 rounded-full mr-3 sm:mr-4 transition-all duration-300 ${
                            condition.completed
                              ? 'bg-[#10B981]/20 ring-2 ring-[#10B981]/40 shadow-md'
                              : 'bg-[#334155]/30'
                          }`}
                        >
                          {condition.completed ? (
                            <Check size={14} className="text-[#10B981]" strokeWidth={2} />
                          ) : (
                            <condition.icon size={14} className="text-[#F8FAFC]" strokeWidth={1.5} />
                          )}
                        </div>
                        <span
                          className={`flex-1 text-sm sm:text-base font-light ${
                            condition.completed ? 'text-white' : 'text-white/90'
                          }`}
                        >
                          {condition.label}
                        </span>
                        <span
                          className={`text-xs sm:text-sm font-light rounded-full px-2 sm:px-3 py-0.5 sm:py-1 ml-2 sm:ml-3 max-[400px]:ml-0 ${
                            condition.completed
                              ? 'bg-[#10B981]/20 text-[#10B981] border border-[#10B981]/30 shadow-sm'
                              : 'bg-[#334155]/40 text-white/80 border border-[#334155]/30'
                          }`}
                        >
                          {condition.completed ? t('complete', 'Complete') : t('incomplete', 'Incomplete')}
                        </span>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
              {/* Top 3 places qualifying conditions */}
              <div className="bg-gradient-to-br from-[#0F172A]/60 to-[#0F172A]/40 backdrop-blur-sm p-4 sm:p-5 rounded-xl border border-indigo-500/20 shadow-md relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-transparent pointer-events-none"></div>

                <h4 className="text-sm sm:text-base font-light text-indigo-200 mb-3 sm:mb-4 flex items-center">
                  <div className="h-1.5 w-1.5 bg-indigo-300 rounded-full mr-2 sm:mr-2.5"></div>
                  {t('forTop3Places', 'For Top 3 Places')}
                </h4>

                <ul className="space-y-3 sm:space-y-4">
                  {qualifyingConditions.topPlacesConditions.map((condition) => (
                    <li key={condition.id} className="group">
                      <div
                        className={`flex flex-col rounded-xl transition-all duration-300 ${
                          condition.completed
                            ? 'bg-[#10B981]/10 border-l-[3px] border-[#10B981]'
                            : 'bg-gradient-to-r from-[#334155]/20 to-[#334155]/10 hover:from-[#334155]/25 hover:to-[#334155]/15 shadow-md'
                        }`}
                      >
                        <div className="flex items-center px-3 sm:px-4 py-2.5 sm:py-3.5 flex-wrap gap-2 max-[400px]:flex-col max-[400px]:items-start">
                          <div
                            className={`flex-shrink-0 p-1.5 sm:p-2 rounded-full mr-3 sm:mr-4 transition-all duration-300 ${
                              condition.completed
                                ? 'bg-[#10B981]/20 ring-2 ring-[#10B981]/40 shadow-md'
                                : 'bg-[#334155]/30'
                            }`}
                          >
                            {condition.completed ? (
                              <Check size={14} className="text-[#10B981]" strokeWidth={2} />
                            ) : (
                              <condition.icon size={14} className="text-white" strokeWidth={1.5} />
                            )}
                          </div>

                          <span
                            className={`flex-1 text-sm sm:text-base font-light ${
                              condition.completed ? 'text-white' : 'text-white'
                            }`}
                          >
                            {condition.label}
                          </span>

                          {/* Progress indicator */}
                          {condition.progress && (
                            <div className="flex flex-col items-end flex-1 w-[100%]">
                              <span className="text-xs sm:text-sm font-light bg-indigo-600/20 px-2 sm:px-3 py-0.5 sm:py-1 rounded-full text-indigo-200 mb-1 sm:mb-1.5 border border-indigo-500/30 shadow-sm">
                                {condition.progress}
                              </span>
                              <div className="sm:w-20 w-[100%] h-1.5 bg-[#334155]/40 rounded-full overflow-hidden shadow-inner">
                                <div
                                  className="h-full bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full"
                                  style={{ width: condition.progressBarWidth }}
                                ></div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Contest cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
        {submissions.map((submission) => (
          <div
            key={submission.id}
            className="bg-gradient-to-b from-[#1E293B] to-[#1E293B]/90 cursor-pointer rounded-xl overflow-hidden border border-purple-500/30 flex flex-col relative group transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/20 hover:border-purple-500/40"
          >
            <div className="h-24 sm:h-28 overflow-hidden relative">
              <div className="absolute inset-0 bg-gradient-to-t from-[#1E293B] to-transparent z-10"></div>
              <img
                src={submission.screenshot}
                alt={submission.name}
                className="w-full h-full object-none object-top transition-transform duration-500 group-hover:scale-110"
              />
              <button
                className="absolute top-2 right-2 bg-[#EF4444]/90 text-white p-1.5 rounded-full z-20 shadow-lg transition-all duration-200 hover:bg-[#EF4444] hover:scale-105"
                onClick={(e) => {
                  e.stopPropagation();
                  openDeleteModal(submission.projectSlug, submission.name);
                }}
                aria-label={t('deleteSubmission', 'Delete submission')}
              >
                <Trash2 size={14} strokeWidth={1.5} />
              </button>
            </div>
            <div className="flex flex-col p-3 sm:p-3.5 relative z-20">
              <h3 className="font-light text-white text-sm sm:text-base mb-2 truncate">{submission.name}</h3>
              <div className="flex justify-between items-center">
                <div className="flex items-center text-[#F59E0B] bg-[#F59E0B]/20 px-2 sm:px-2.5 py-1 sm:py-1.5 rounded-full border border-[#F59E0B]/20 shadow-sm">
                  <Heart size={12} strokeWidth={1.5} className="mr-1 sm:mr-1.5" fill="rgba(245, 158, 11, 0.3)" />
                  <span className="text-sm sm:text-base font-light">{submission.likes}</span>
                </div>
                <a
                  href={submission.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-purple-300 hover:text-purple-200 transition-colors flex items-center text-xs sm:text-sm bg-purple-500/10 hover:bg-purple-500/20 px-2 sm:px-2.5 py-1 sm:py-1.5 rounded-full border border-purple-500/20"
                  onClick={(e) => e.stopPropagation()}
                >
                  <span className="mr-1 sm:mr-1.5">{t('view', 'View')}</span>
                  <ExternalLink size={10} strokeWidth={1.5} />
                </a>
              </div>
            </div>
          </div>
        ))}
      </div>

      {submissions.length < 3 && (
        <div className="mt-4 sm:mt-5 text-center text-white text-sm sm:text-base bg-[#334155]/30 rounded-lg p-3 sm:p-3.5 border border-[#334155]/40 shadow-inner">
          <p className="inline-flex items-center justify-center font-light text-sm sm:text-base gap-2 w-auto">
            <AlertCircle size={14} className="text-purple-300 flex-shrink-0" strokeWidth={1.5} />
            <span className="inline-block">
              {t(
                'submitMoreProjectsToIncreaseYourChangesOfWining',
                'Submit more projects to increase your chances of winning!',
              )}
            </span>
          </p>
        </div>
      )}

      <DeleteConfirmationModal
        isOpen={deleteModalData.isOpen}
        projectName={deleteModalData.projectName}
        onCancel={closeDeleteModal}
        onConfirm={confirmDelete}
      />
    </div>
  );
};

export default ContestStatus;
