import React from 'react';
import { AlertTriangle } from 'lucide-react';
import { Trans, useTranslation } from 'react-i18next';

type DeleteConfirmationModalProps = {
  isOpen: boolean;
  projectName: string;
  onCancel: () => void;
  onConfirm: () => void;
};

const DeleteConfirmationModal: React.FC<DeleteConfirmationModalProps> = ({
  isOpen,
  projectName,
  onCancel,
  onConfirm,
}) => {
  const { t } = useTranslation();

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop with blur effect */}
      <div className="absolute inset-0 bg-[#0F172A]/80 backdrop-blur-sm" onClick={onCancel}></div>

      {/* Modal content */}
      <div className="relative z-10 w-full max-w-md bg-[#1E293B] rounded-xl shadow-2xl border border-[#334155] overflow-hidden">
        <div className="p-6">
          <div className="flex flex-col items-center">
            {/* Warning icon */}
            <div className="w-16 h-16 mb-6 text-[#F59E0B]">
              <AlertTriangle size={64} strokeWidth={1.5} />
            </div>

            <h3 className="text-xl font-medium text-[#F8FAFC] mb-3 text-center">
              {t('removeFromHackathon', 'Remove from Hackathon?')}
            </h3>

            <p className="text-[#94A3B8] text-center mb-6">
              <Trans
                i18nKey="removeFromHackathonDescription"
                ns="translation"
                values={{
                  project: projectName,
                }}
                components={{
                  project: <span className="text-[#F8FAFC] font-medium" />,
                }}
              />
            </p>

            <div className="flex items-center space-x-4 w-full">
              <button
                onClick={onCancel}
                className="flex-1 py-2 bg-transparent border border-[#334155] rounded-lg text-[#94A3B8] hover:text-[#F8FAFC] hover:bg-[#334155]/20 transition-colors"
              >
                {t('cancel', 'Cancel')}
              </button>
              <button
                onClick={onConfirm}
                className="flex-1 py-2 bg-[#EF4444] text-white rounded-lg hover:bg-[#EF4444]/90 transition-colors"
              >
                {t('settings.deployment.DomainSettings.remove', 'Remove')}
              </button>
            </div>
          </div>
        </div>

        {/* Close button */}
        <button
          onClick={onCancel}
          className="absolute bg-transparent top-4 right-4 text-[#94A3B8] hover:text-[#F8FAFC] transition-colors"
          aria-label="Close modal"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
    </div>
  );
};

export default DeleteConfirmationModal;
