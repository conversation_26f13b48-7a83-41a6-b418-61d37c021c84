import React from 'react';

const variantClasses = {
  primary:
    'flex items-center rounded-lg transition-all duration-300 relative bg-gradient-to-r from-[#10B981] to-[#0EA271] text-white py-1.5 px-3 font-normal shadow-md hover:shadow-lg hover:from-[#0EA271] hover:to-[#10B981] hover:scale-105',
  warning:
    'flex items-center rounded-lg transition-all duration-300 relative bg-[#F59E0B]/10 text-[#F59E0B] p-1.5 border border-[#F59E0B]/30 hover:bg-[#F59E0B]/20 hover:border-[#F59E0B] hover:scale-105',
  danger:
    'flex items-center rounded-lg transition-all duration-300 relative bg-[#EF4444]/10 text-[#EF4444] p-1.5 border border-[#EF4444]/30 hover:bg-[#EF4444]/20 hover:border-[#EF4444] hover:scale-105 pulse-on-hover',
  download:
    'flex items-center rounded-lg transition-all duration-300 relative bg-[#0B1931] text-white py-1.5 px-2.5 shadow-md hover:bg-[#152A4A] hover:shadow-lg hover:scale-105',
  secondary:
    'flex items-center rounded-lg transition-all duration-300 relative bg-[#334155]/20 text-[#94A3B8] p-1.5 border border-[#334155]/30 backdrop-blur-sm hover:bg-[#334155]/40 hover:text-[#F8FAFC] hover:border-[#334155] hover:scale-105',
};

const IconButton = ({ icon, onClick, variant = 'secondary', tooltip, text, ...rest }) => {

  const buttonClasses = variantClasses[variant] || variantClasses.secondary;

  const extraStyles =
    variant === 'download'
      ? {
        backgroundImage:
          'linear-gradient(45deg, rgba(255,255,255,0.05) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.05) 50%, rgba(255,255,255,0.05) 75%, transparent 75%, transparent)',
        backgroundSize: '8px 8px',
      }
      : {};

  return (
    <button
      className={`${buttonClasses} group`}
      onClick={onClick}
      aria-label={tooltip || text}
      style={extraStyles}
      {...rest}
    >
      <span className="flex items-center justify-center">
        {icon}
        {text && <span className="ml-2 font-normal text-sm">{text}</span>}
      </span>
    </button>
  );
};

export default IconButton;
