import React from 'react';
import {
  FaChartPie,
  FaCheckCircle,
  FaCode,
  FaCubes,
  FaInfoCircle,
  FaLayerGroup,
  FaProjectDiagram,
  FaTools
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

const ProjectInfoCard = ({ projectInfo }) => {
  const { t } = useTranslation();
  return (
    <div className="bg-gradient-to-r from-blue-500/20 to-blue-600/20 bg-opacity-70 rounded-lg p-4 sm:p-5 relative overflow-hidden">
      <h3 className="font-light text-base lg:text-lg text-white mb-3 sm:mb-4">
        {t('projectInfo.information', 'Project Information')}
      </h3>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
        <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4">
          <div className="flex items-center mb-1 sm:mb-2">
            <FaProjectDiagram className="text-purple-400 mr-2" />
            <span className="text-xs lg:text-base text-gray-400 font-light">
              {t('projectInfo.type', 'Project Type')}
            </span>
          </div>
          <span className="text-white font-medium text-sm sm:text-base">{projectInfo.estimations.projectType}</span>
        </div>

        <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4">
          <div className="flex items-center mb-1 sm:mb-2">
            <FaLayerGroup className="text-blue-400 mr-2" />
            <span className="text-xs lg:text-base text-gray-400 font-light">
              {t('projectInfo.complexity', 'Complexity')}
            </span>
          </div>
          {/*<span className="text-white font-medium text-sm sm:text-base">{projectInfo.complexity}</span>*/}
          <span className="text-white font-medium text-sm sm:text-base">
            {projectInfo.estimations.projectComplexity}
          </span>
        </div>

        <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4">
          <div className="flex items-center mb-1 sm:mb-2">
            <FaCubes className="text-green-400 mr-2" />
            <span className="text-xs lg:text-base text-gray-400 font-light">
              {t('projectInfo.components', 'Components')}
            </span>
          </div>
          <span className="text-white font-medium text-sm sm:text-base">
            {projectInfo.estimations.uniqueComponentCount}
          </span>
        </div>

        <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4">
          <div className="flex items-center mb-1 sm:mb-2">
            <FaCode className="text-yellow-400 mr-2" />
            <span className="text-xs lg:text-base text-gray-400 font-light">
              {t('projectInfo.features', 'Features')}
            </span>
          </div>
          <span className="text-white font-medium text-sm sm:text-base">{projectInfo.estimations.featureCount}</span>
        </div>
      </div>

      {/* Confidence Score and Estimation Uncertainty side by side */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mt-3 sm:mt-4 mb-3 sm:mb-4">
        {/* Confidence Score Section */}
        <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4">
          <div className="flex items-center mb-3">
            <FaCheckCircle className="text-blue-400 mr-2" />
            <span className="text-xs lg:text-base text-gray-400 font-light">
              {t('projectInfo.confidenceScore', 'Confidence Score')}
            </span>
          </div>
          <div className="flex items-center">
            {/*<span className="text-white font-semibold text-lg">{projectInfo.confidenceScore}%</span>*/}
            <span className="text-white font-semibold text-lg">{projectInfo.estimations.confidenceScore} %</span>
            <div className="ml-3 flex-grow h-2 bg-gray-700 rounded-full overflow-hidden">
              <div
                className="h-full bg-blue-500 rounded-full"
                style={{ width: `${projectInfo.estimations.confidenceScore}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Estimation Uncertainty - Changed to left align */}
        <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4 flex items-center">
          <div className="w-full">
            <div className="flex items-center mb-3">
              <FaChartPie className="text-purple-400 mr-2" />
              <span className="text-xs lg:text-base text-gray-400 font-light">
                {t('projectInfo.estimationUncertainty', 'Estimation Uncertainty')}
              </span>
            </div>
            <div className="flex items-center justify-start">
              <span className="text-white font-semibold text-lg">{projectInfo.estimations.rangeOfUncertainty}</span>
            </div>
          </div>
        </div>
      </div>

      <div>
        <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4 w-full">
          <div className="flex items-center mb-2">
            <FaTools className="text-orange-400 mr-2" />
            <h4 className="text-xs lg:text-base text-gray-400 font-light">
              {t('projectInfo.keyTechnologies', 'Key Technologies')}
            </h4>
          </div>
          <div className="flex flex-wrap gap-2">
            {projectInfo.estimations.keyTechnologies.map((tech, index) => (
              <span key={index} className="px-2 py-1 bg-navy rounded-md text-sm text-blue-300 border border-blue-800">
                {tech}
              </span>
            ))}
          </div>
        </div>
      </div>

      {/* Background Icon - Only keeping the bottom-right one */}
      <div className="absolute -bottom-8 -right-8 text-[150px] opacity-5">
        <FaInfoCircle />
      </div>
    </div>
  );
};

export default ProjectInfoCard;
