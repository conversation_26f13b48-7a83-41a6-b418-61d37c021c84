import React, { useMemo, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import {
  FaBolt,
  FaChartLine,
  FaChevronDown,
  FaChevronUp,
  FaClock,
  FaDollarSign,
  FaMoneyBillWave,
  FaRobot,
  Fa<PERSON><PERSON>s,
  FaUserTie
} from 'react-icons/fa';
import { BsGearFill, BsLightningChargeFill } from 'react-icons/bs';
import { useTranslation } from 'react-i18next';
import { Estimations, Project } from '~/api/projectsApi';
import { intervalToDuration } from 'date-fns';
import { buildStyles, CircularProgressbar } from 'react-circular-progressbar';
import 'react-circular-progressbar/dist/styles.css';
import '~/components/styles/index.scss';
import BreakdownChart from '~/backOffice/components/dashboard/BreakdownChart';
import ProjectInfoCard from '~/backOffice/components/dashboard/ProjectInfoCard';

type Metrics = {
  estimatedCost: {
    traditional: Project['estimatedCostTraditional'];
    tokens: Project['timeSpent'];
  };
  timeMetrics: {
    traditional: Project['estimatedTimeTraditional'];
    actual: Project['timeSpentAi'];
  };

  estimations:{
    confidenceScore: Project['estimations.confidenceScore'];
    estimatedCostTraditional: Estimations['estimations.estimatedCostTraditional'];
    estimatedTimeTraditional: Estimations['estimations.estimatedTimeTraditional'];
    estimatedNumberOfDevelopers: Estimations['estimaations.estimatedNumberOfDevelopers'];
    recommendedDeveloperLevel: Estimations['estimations.recommendedDeveloperLevel'];
    timeToMarket: Estimations['estimations.timeToMarket'];
    maintenanceCostPercentage: Estimations['estimations.maintenanceCostPercentage'];
    projectType: Estimations['estimations.projectType'];
    projectComplexity: Estimations['estimations.projectComplexity'];
    uniqueComponentCount: Estimations['estimations.uniqueComponentCount'];
    featureCount: Estimations['estimations.featureCount'];
    rangeOfUncertainty: Estimations['estimations.rangeOfUncertainty'];
    keyTechnologies: Estimations['keyTechnologies'];
    breakdown: Estimations['breakdown'];
  }
};



type ProjectMetricsProps = {
  metrics: Metrics;
  hideIcon: (e: number | undefined) => void;
  index: number;
};

function ProjectMetrics({ metrics, hideIcon,index, projectInfo}: ProjectMetricsProps) {
  const { t } = useTranslation('translation');
  const [showComparison, setShowComparison] = useState(false);
  const actualTime = useMemo(() => {
    const duration = intervalToDuration({ start: 0, end: metrics.timeMetrics.actual * 1000 });
    const accumulatedDays = (duration.days ?? 0) + (duration.years ?? 0) * 365 + (duration.months ?? 0) * 30;
    const accumulatedDaysString = accumulatedDays > 0 ? `${accumulatedDays}d ` : '';
    const hoursString = (duration.hours ?? 0 > 0) ? `${duration.hours ?? 0}h ` : '';
    const minutesString = `${duration.minutes ?? 0}m `;
    const secondsString = `${duration.seconds ?? 0}s`;
    return `${accumulatedDaysString}${hoursString}${minutesString}${secondsString}`;
  }, [metrics.timeMetrics.actual]);

  const savings = useMemo(() => {
    const costSavings =
      ((metrics.estimatedCost.traditional - metrics.estimatedCost.tokens) / metrics.estimatedCost.traditional) * 100;
    const timeSavings =
      (((metrics.timeMetrics.traditional * 60 * 60) - metrics.timeMetrics.actual) / (metrics.timeMetrics.traditional * 60 * 60)) * 100;
    return { costSavings, timeSavings };
  }, [
    metrics.estimatedCost.traditional,
    metrics.estimatedCost.tokens,
    metrics.timeMetrics.traditional,
    metrics.timeMetrics.actual,
  ]);


  // Convert savings to percentage strings.
  const costSavingsPercentage = savings.costSavings.toFixed(2);
  const timeSavingsPercentage = Math.min(100, savings.timeSavings).toFixed(2);

  const costSavingsNumber = parseFloat(costSavingsPercentage);
  const safeCostSavingsValue = Number.isFinite(costSavingsNumber) ? costSavingsNumber : 0;
  const safeCostSavingsText = Number.isFinite(costSavingsNumber) ? `${costSavingsPercentage}%` : '...';

  const timeSavingsNumber = parseFloat(timeSavingsPercentage);
  const safeTimeSavingsValue = Number.isFinite(timeSavingsNumber) ? timeSavingsNumber : 0;
  const safeTimeSavingsText = Number.isFinite(timeSavingsNumber) ? `${timeSavingsPercentage}%` : '...';

  // Button to toggle comparison section.
  const ComparisonButton = () => (
    <button
      onClick={() => {
        if (showComparison) {
          hideIcon(undefined); // închidere – scoate indexul
        } else {
          hideIcon(index); // deschidere – adaugă indexul
        }
        setShowComparison(!showComparison);
      }}
      className="w-full bg-[#222d40] hover-lines backdrop-blur-sm rounded-xl px-3 py-2 lg:px-4 text-left transition-all border border-gray-700/50 flex items-start md:items-center flex-wrap md:flex-nowrap justify-start md:justify-between"
    >
      <div className="flex items-center gap-3">
        <div className="p-2 lg:p-3 bg-black/20 rounded-xl text-purple-400">
          <FaChartLine className="text-base lg:text-xl" />
        </div>
        <div>
          <h3 className="font-light text-sm md:text-base lg:text-lg">{t('developmentComparison', 'Development Comparison')}</h3>
          <p className="text-xs lg:text-base text-gray-400 font-light hidden md:block">{t('traditionalVsAI', 'Traditional vs AI')}</p>
        </div>
      </div>
      <div className="flex items-center text-gray-400 space-x-6 w-full md:w-fit justify-between p-2 md:p-0">
        <div className="flex flex-nowrap items-center gap-2">
          {/* Dev Cost */}
          <div className="flex flex-col min-w-[77px] items-center px-2 py-1 bg-white/[0.02] rounded-md border border-white/[0.05] backdrop-blur-sm hover:bg-white/[0.04] transition-all duration-300 min-w-[70px]">
            <div className="flex items-center gap-1 mb-0.5">
              <div className={`w-1.5 h-1.5 rounded-full bg-red-400`}></div>
              <span className="text-white/60 text-[10px] font-light tracking-wide uppercase ">Dev Cost</span>
            </div>

            <div className="flex items-center gap-0.5">
              <span className="text-red-400 text-xs">$</span>
              <AnimatePresence mode="wait">
                <motion.span
                  key={metrics.estimations.estimatedCostTraditional.toLocaleString()}
                  initial={{ y: -8, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: 8, opacity: 0 }}
                  transition={{ duration: 0.3, ease: "easeOut" }}
                  className="text-white font-medium text-sm"
                >
                  {metrics.estimations.estimatedCostTraditional.toLocaleString()}
                </motion.span>
              </AnimatePresence>
            </div>

            <div className="flex items-center gap-0.5 mt-0.5">
              <div className="w-2 h-2 rounded-full bg-white/10 flex items-center justify-center">
                <div className="w-1 h-1 rounded-full bg-orange-400"></div>
              </div>

              <span className="text-white/40 text-[9px] font-light">{metrics.estimations.estimatedTimeTraditional}h</span>
            </div>
          </div>

          {/* Linie embosată */}
          <div className="w-px h-8 bg-white/10"></div>

          {/* Biela.dev */}
          <div className="flex flex-col min-w-[77px] items-center px-2 py-1 bg-white/[0.02] rounded-md border border-white/[0.05] backdrop-blur-sm hover:bg-white/[0.04] transition-all duration-300 min-w-[70px]">
            <div className="flex items-center gap-1 mb-0.5">
              <div className={`w-1.5 h-1.5 rounded-full bg-[#4ADE80]`}></div>
              <span className="text-white/60 text-[10px] font-light tracking-wide uppercase">Biela.dev</span>
            </div>

            <div className="flex items-center gap-0.5">
              <span className="text-[#4ADE80] text-xs">$</span>
              <AnimatePresence mode="wait">
                <motion.span
                  key={metrics.estimatedCost.tokens.toFixed(2).toLocaleString()}
                  initial={{ y: -8, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: 8, opacity: 0 }}
                  transition={{ duration: 0.3, ease: "easeOut" }}
                  className="text-white font-medium text-sm"
                >
                  {metrics.estimatedCost.tokens.toFixed(2).toLocaleString()}
                </motion.span>
              </AnimatePresence>
            </div>

            <div className="flex items-center gap-0.5 mt-0.5">
              <div className="w-2 h-2 rounded-full bg-white/10 flex items-center justify-center">
                <div className="w-1 h-1 rounded-full bg-orange-400"></div>
              </div>

              <span className="text-white/40 text-[9px] font-light">{actualTime}</span>
            </div>
          </div>

          {/* Linie embosată */}
          <div className="w-px h-8 bg-white/10"></div>
          {/* Profit */}
          <div className="flex flex-col min-w-[77px] items-center px-2 py-1 bg-white/[0.02] rounded-md border border-white/[0.05] backdrop-blur-sm hover:bg-white/[0.04] transition-all duration-300 min-w-[70px]">
            <div className="flex items-center gap-1 mb-0.5">
              <div className={`w-1.5 h-1.5 rounded-full bg-[#4ADE80]`}></div>
              <span className="text-white/60 text-[10px] font-light tracking-wide uppercase">Profit</span>
            </div>

            <div className="flex items-center gap-0.5">
              <span className="text-[#4ADE80] text-xs">$</span>
              <AnimatePresence mode="wait">
                <motion.span
                  key={Math.floor(metrics.estimations.estimatedCostTraditional - metrics.estimatedCost.tokens).toLocaleString('en-US').replace(/,/g, ',')}
                  initial={{ y: -8, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: 8, opacity: 0 }}
                  transition={{ duration: 0.3, ease: "easeOut" }}
                  className="text-white font-medium text-sm"
                >
                  {Math.floor(metrics.estimations.estimatedCostTraditional - metrics.estimatedCost.tokens).toLocaleString('en-US').replace(/,/g, ',')}
                </motion.span>
              </AnimatePresence>
            </div>

            <div className="flex items-center gap-0.5 mt-0.5">
              <div className="w-2 h-2 rounded-full bg-white/10 flex items-center justify-center">
                <div className="w-1 h-1 rounded-full bg-orange-400"></div>
              </div>
              <span className="text-white/40 text-[9px] font-light">{metrics.estimations.estimatedTimeTraditional - Math.floor(metrics.timeMetrics.actual / 3600)}h</span>
            </div>
          </div>
        </div>
        {showComparison ? <FaChevronUp className="text-gray-400" /> : <FaChevronDown className="text-gray-400" />}
      </div>
    </button>
  );

  return (
    <div className="space-y-3 lg:space-y-4">
      <ComparisonButton />

      <AnimatePresence>
        {showComparison && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="grid grid-cols-1 lg:grid-cols-[2fr_1fr_2fr] gap-4 sm:gap-4">
              {/* NEW TRADITIONAL CARD*/}
              <div className="bg-gradient-to-r from-red-500/20 to-red-600/20  rounded-lg p-4 sm:p-5 h-full flex flex-col relative overflow-hidden">
                <div className="flex items-center mb-4">
                  <FaUserTie className="text-red-300 mr-3 text-xl" />
                  <div>
                    <h3 className="font-light text-base lg:text-lg text-white">{t('traditional', 'Traditional')}</h3>
                    <p className="text-xs lg:text-base text-gray-400 font-light">
                      {t('standardApproach', 'Standard Approach')}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-3 sm:mb-4">
                  <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4">
                    <div className="flex items-center mb-2">
                      <FaDollarSign className="text-red-300 mr-2" />
                      <span className="text-xs lg:text-base text-gray-400 font-light">
                        {t('developmentCost', 'Development Cost')}
                      </span>
                    </div>
                    <div className="text-lg sm:text-xl font-bold text-white">
                      $ {metrics.estimations.estimatedCostTraditional}
                    </div>
                  </div>

                  <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4">
                    <div className="flex items-center mb-2">
                      <FaClock className="text-red-300 mr-2" />
                      <span className="text-xs lg:text-base text-gray-400 font-light">
                        {t('developmentTime', 'Development Time')}
                      </span>
                    </div>
                    <div className="text-lg sm:text-xl font-bold text-white">
                      {metrics.estimations.estimatedTimeTraditional}h
                    </div>
                  </div>
                </div>

                <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4 mb-3 sm:mb-4">
                  <div className="flex items-center justify-between mb-2 sm:mb-3">
                    <span className="text-xs lg:text-base text-gray-400 font-light">
                      {t('projectMetrics.teamComposition', 'Team Composition')}
                    </span>
                    <span className="text-white font-semibold text-sm sm:text-base">
                      {metrics.estimations.estimatedNumberOfDevelopers} Developer
                      {metrics.estimations.estimatedNumberOfDevelopers > 1 ? 's' : ''}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs lg:text-base text-gray-400 font-light">Developer Level</span>
                    <span className="text-white font-semibold text-sm sm:text-base">
                      {metrics.estimations.recommendedDeveloperLevel}
                    </span>
                  </div>
                </div>

                <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4 mb-3 sm:mb-4 flex-grow">
                  <h4 className="font-light text-base lg:text-lg text-white mb-2 sm:mb-3">
                    {t('projectMetrics.hoursBreakdown', 'Hours Breakdown')}
                  </h4>
                  <BreakdownChart breakdown={metrics.estimations.breakdown} />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4">
                    <div className="flex flex-col">
                      <span className="text-xs lg:text-base text-gray-400 font-light">
                        {t('projectMetrics.timeToMarket', 'Time to Market')}
                      </span>
                      <span className="text-white font-semibold text-sm sm:text-base">
                        {metrics.estimations.timeToMarket}
                      </span>
                    </div>
                  </div>

                  <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4">
                    <div className="flex flex-col">
                      <span className="text-xs lg:text-base text-gray-400 font-light">
                        {t('projectMetrics.maintenance', 'Maintenance')}
                      </span>
                      <span className="text-white font-semibold text-sm sm:text-base">
                        {metrics.estimations.maintenanceCostPercentage}%/year
                      </span>
                    </div>
                  </div>
                </div>

                {/* Background Icon */}
                <div className="absolute -bottom-8 -right-8 text-[150px] opacity-5">
                  <FaUsers />
                </div>
              </div>
              {/*END OF NEW TRADITIONAL CARD*/}

              {/* Middle Column – Savings Cards */}
              <div className="flex flex-col h-full">
                {/* Cost Savings Card */}
                <div
                  className="bg-gradient-to-r from-green-500/20 to-green-600/20 rounded-lg p-4 sm:p-5 relative overflow-hidden mb-3 sm:mb-5"
                  style={{ paddingTop: '1.5rem', paddingBottom: '1.25rem' }}
                >
                  <div className="flex items-center mb-3">
                    <FaDollarSign className="text-green-400 mr-3 text-xl" />
                    <h3 className="font-light text-base lg:text-lg text-white">{t('costSavings', 'Cost Savings')}</h3>
                  </div>
                  <div className="flex items-center justify-center my-2">
                    <div className="w-20 h-20 sm:w-24 sm:h-24">
                      <CircularProgressbar
                        value={safeCostSavingsValue}
                        text={safeCostSavingsText}
                        styles={buildStyles({
                          textSize: '16px',
                          pathColor: '#4ADE80',
                          textColor: '#FFFFFF',
                          trailColor: 'rgba(255, 255, 255, 0.1)',
                          fontFamily: 'Manrope, sans-serif',
                        })}
                      />
                    </div>
                  </div>
                  <div className="absolute -bottom-8 -right-8 text-[150px] opacity-5">
                    <FaMoneyBillWave />
                  </div>
                </div>
                {/* Time Saved Card */}
                <div
                  className="bg-gradient-to-r from-blue-500/20 to-blue-600/20 rounded-lg p-4 sm:p-5 flex-grow relative overflow-hidden flex flex-col items-center justify-center"
                  style={{ paddingTop: '1.5rem', paddingBottom: '2rem' }}
                >
                  <div className="flex items-center mb-3 w-full">
                    <BsLightningChargeFill className="text-blue-400 mr-3 text-xl" />
                    <h3 className="font-light text-base lg:text-lg text-white">{t('timeSaved', 'Time Saved')}</h3>
                  </div>
                  <div className="flex-grow flex items-center justify-center w-full py-4">
                    <div className="w-20 h-20 sm:w-24 sm:h-24">
                      <CircularProgressbar
                        value={safeTimeSavingsValue}
                        text={safeTimeSavingsText}
                        styles={buildStyles({
                          textSize: '16px',
                          pathColor: '#60A5FA',
                          textColor: '#FFFFFF',
                          trailColor: 'rgba(255, 255, 255, 0.1)',
                          fontFamily: 'Manrope, sans-serif',
                        })}
                      />
                    </div>
                  </div>
                  <div className="absolute -bottom-8 -right-8 text-[150px] opacity-5">
                    <FaBolt />
                  </div>
                </div>
              </div>

              {/*BIELA NEW AI CARD*/}
              <div className="bg-gradient-to-r from-green-500/20 to-green-600/20 rounded-lg p-4 sm:p-5 h-full flex flex-col relative overflow-hidden">
                <div className="flex items-center mb-4">
                  <BsGearFill className="text-green-400 mr-3 text-xl" />
                  <div>
                    <h3 className="font-light text-base lg:text-lg text-white">{t('bielaDevAI', 'Biela.dev AI')}</h3>
                    <p className="text-xs lg:text-base text-gray-400 font-light">{t('nextGenDevelopment', 'Next-Gen Development')}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-3 sm:mb-4">
                  <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4">
                    <div className="flex items-center mb-2">
                      <FaDollarSign className="text-green-400 mr-2" />
                      <span className="text-xs lg:text-base text-gray-400 font-light">{t('developmentCost', 'Development Cost')}</span>
                    </div>
                    <div className="text-lg sm:text-xl font-bold text-white">
                      $ {metrics.estimatedCost.tokens.toFixed(2)}
                    </div>
                  </div>

                  <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4">
                    <div className="flex items-center mb-2">
                      <FaClock className="text-green-400 mr-2" />
                      <span className="text-xs lg:text-base text-gray-400 font-light">{t('developmentTime', 'Development Time')}</span>
                    </div>
                    <div className="text-lg sm:text-xl font-bold text-white">{actualTime}</div>
                  </div>
                </div>

                <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4 mb-3 sm:mb-4">
                  <div className="flex items-center justify-between mb-2 sm:mb-3">
                    <span className="text-xs lg:text-base text-gray-400 font-light">
                      {t('projectMetrics.teamComposition', 'Team Composition')}
                    </span>
                    <span className="text-white font-semibold text-sm sm:text-base">
                      {t('projectMetrics.aiPowered', 'AI-Powered')}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs lg:text-base text-gray-400 font-light">
                      {t('projectMetrics.developerLevel', 'Developer Level')}
                    </span>
                    <span className="text-white font-semibold text-sm sm:text-base">
                      {t('projectMetrics.nextGenAI', 'Next-Generation AI')}
                    </span>
                  </div>
                </div>

                <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4 mb-3 sm:mb-4 flex-grow">
                  <h4 className="font-light text-base lg:text-lg text-white mb-2 sm:mb-3">
                    {t('projectMetrics.keyBenefits', 'Key Benefits')}
                  </h4>
                  <ul className="space-y-1 sm:space-y-2 text-xs lg:text-base text-gray-400 font-light">
                    <li className="flex items-center">
                      <span className="text-green-400 mr-2">✓</span>
                      {t('projectMetrics.instantDevelopment', 'Instant Development')}
                    </li>
                    <li className="flex items-center">
                      <span className="text-green-400 mr-2">✓</span>
                      {t('projectMetrics.noMaintenanceCosts', 'No Maintenance Costs')}
                    </li>
                    <li className="flex items-center">
                      <span className="text-green-400 mr-2">✓</span>
                      {t('projectMetrics.highConfidence', 'High Confidence')}
                    </li>
                    <li className="flex items-center">
                      <span className="text-green-400 mr-2">✓</span>
                      {t('projectMetrics.productionReadyCode', 'Production-Ready Code')}
                    </li>
                  </ul>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4">
                    <div className="flex flex-col">
                      <span className="text-xs lg:text-base text-gray-400 font-light">
                        {t('projectMetrics.timeToMarket', 'Time to Market')}
                      </span>
                      <span className="text-white font-semibold text-sm sm:text-base">
                        {t('projectMetrics.immediate', 'Immediate')}
                      </span>
                    </div>
                  </div>

                  <div className="bg-black bg-opacity-20 rounded-lg p-3 sm:p-4">
                    <div className="flex flex-col">
                      <span className="text-xs lg:text-base text-gray-400 font-light">
                        {t('projectMetrics.uncertainty', 'Uncertainty')}
                      </span>
                      <span className="text-white font-semibold text-sm sm:text-base">
                        {t('projectMetrics.minimal', 'Minimal')}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Background Icon */}
                <div className="absolute -bottom-8 -right-8 text-[150px] opacity-5">
                  <FaRobot />
                </div>
              </div>
              {/* END BIELA NEW AI CARD*/}
            </div>

            {/*PROJECT INFO*/}
            <div className="mt-4 sm:mt-4">
              <ProjectInfoCard
                projectInfo={{
                  estimations: {
                    confidenceScore: metrics.estimations.confidenceScore,
                    projectType: metrics.estimations.projectType,
                    projectComplexity: metrics.estimations.projectComplexity,
                    uniqueComponentCount: metrics.estimations.uniqueComponentCount,
                    featureCount: metrics.estimations.featureCount,
                    rangeOfUncertainty: metrics.estimations.rangeOfUncertainty,
                    keyTechnologies: metrics.estimations.keyTechnologies,
                  },
                }}
              ></ProjectInfoCard>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

export default ProjectMetrics;
