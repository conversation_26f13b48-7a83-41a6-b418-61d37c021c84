import { useEffect, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON><PERSON>,
  FaExclamationT<PERSON>gle,
  FaFolder,
  FaFolderPlus,
  FaGlobe,
  FaLayerGroup,
  FaMobile,
  FaPlus,
  FaSpinner,
  FaTimes,
  FaTrash
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { FaPencil } from 'react-icons/fa6';
import { createFolder, deleteFolder, fetchProjects, getFolders, updateFolderName } from '~/api/projectsApi';
import { Tooltip as ReactTooltip } from 'react-tooltip';

type Project = {
  id: number;
  title: string;
  createdAt: string;
  projectSlug: string;
  type: string;
};

type ProjectsTypeFilterProps = {
  projects: Project[];
  onFilterChange: (filteredProjects: Project[]) => void;
  showNewFolderInput: boolean;
  setShowNewFolderInput: (value: boolean) => void;
  selectedType: string;
  setSelectedType: (type: string) => void;
  selectedFolder: (value: string) => void;
  refreshProjects: () => void;
};

type FolderType = {
  userId: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  projectIds: string[];
  _id: string;
  __v: number;
};

const colors = ['bg-[#302253]', 'bg-[#1a3835]', 'bg-[#3a352e]', 'bg-[#2e3a35]', 'bg-[#352e3a]'];

const getIconByType = (type: string) => {
  switch (type.toLowerCase()) {
    case 'all':
      return FaLayerGroup;
    case 'web projects':
      return FaGlobe;
    case 'mobile apps':
      return FaMobile;
    case 'api':
      return FaCode;
    default:
      return FaFolder;
  }
};

function ProjectsTypeFilter({
  projects,
  onFilterChange,
  showNewFolderInput,
  setShowNewFolderInput,
  selectedFolder,
  refreshProjects,
}: ProjectsTypeFilterProps) {
  const { t } = useTranslation('translation');
  const [deleteConfirmationModal, setDeleteConfirmationModal] = useState<{
    open: boolean;
    folder: string | null;
    id: string;
  }>({
    open: false,
    folder: null,
    id: '',
  });

  const [selectedType, setSelectedType] = useState<string>('all');
  const [newFolderName, setNewFolderName] = useState<string>('');
  const [userCreatedFolders, setUserCreatedFolders] = useState<FolderType[]>([]);
  // Modificare: stocăm _id-ul folderului în editare
  const [editingFolder, setEditingFolder] = useState<string | null>(null);
  const [editedFolderName, setEditedFolderName] = useState<string>('');
  const [deletingFolder, setDeletingFolder] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [creatingFolderProcess, setCreatingFolderProcess] = useState(false);
  const [savingFolderProcess, setSavingFolderProcess] = useState(false);
  const [deletingFolderProcess, setDeletingFolderProcess] = useState(false);
  const [deleteSuccess, setDeleteSuccess] = useState(false);
  const [allProjectsCount, setAllProjectsCount] = useState<number>(0);

  const fetchFolders = async () => {
    try {
      const res = await getFolders();
      setUserCreatedFolders(res);
    } catch (error) {
      console.error('Error fetching folders:', error);
    }
  };

  const fetchAllProjectsCount = async () => {
    try {
      const data = await fetchProjects(undefined, 0, 1);
      setAllProjectsCount(data.totalItems);
    } catch (error) {
      console.error('Error fetching all projects count:', error);
      setAllProjectsCount(0);
    }
  };

  useEffect(() => {
    setLoading(true);
    Promise.all([fetchFolders(), fetchAllProjectsCount()]).finally(() => setLoading(false));
  }, []);

  // Refresh folders when projects prop changes (e.g., after adding a project to a folder)
  useEffect(() => {
    fetchFolders();
    fetchAllProjectsCount();
  }, [projects, refreshProjects]);

  const handleRequestDeleteFolder = (folderName: string, id: string) => {
    setDeleteConfirmationModal({ open: true, folder: folderName, id });
    setDeleteSuccess(false);
  };

  const handleConfirmDeleteFolder = async () => {
    if (deleteConfirmationModal.folder) {
      setDeletingFolderProcess(true);
      try {
        await deleteFolder(deleteConfirmationModal.id);
        await fetchFolders();
        await fetchAllProjectsCount();
        await refreshProjects();
        setDeleteSuccess(true);
        setTimeout(() => {
          setDeleteConfirmationModal({ open: false, folder: null, id: '' });
          setSelectedType('all');
          selectedFolder('all');
          setDeleteSuccess(false);
        }, 1000);
        // setDeleteConfirmationModal({ open: false, folder: null, id: '' });
        // setSelectedType("all");
        // selectedFolder("all");
      } catch (error) {
        console.error('Error deleting folder:', error);
        setDeleteSuccess(false);
      } finally {
        setDeletingFolderProcess(false);
      }
    }
  };

  const handleCancelDeleteFolder = () => {
    setDeleteConfirmationModal({ open: false, folder: null, id: '' });
    setDeleteSuccess(false);
  };

  const handleFilterChange = (type: string) => {
    if (editingFolder) return;
    setSelectedType(type);
    selectedFolder(type);
  };

  const handleCreateNewFolder = async () => {
    if (newFolderName && !userCreatedFolders.some((folder) => folder.name === newFolderName)) {
      setCreatingFolderProcess(true);
      await createFolder(newFolderName)
        .then(async (res) => {
          if (res.userId) {
            await fetchFolders();
            await fetchAllProjectsCount();
            await refreshProjects();
            setNewFolderName('');
            setShowNewFolderInput(false);
          }
        })
        .finally(() => setCreatingFolderProcess(false));
    }
  };

  const handleCancelNewFolder = () => {
    setNewFolderName('');
    setShowNewFolderInput(false);
  };

  // Modificare: primim atât id-ul cât și numele folderului
  const handleEditFolder = (folderId: string, folderName: string) => {
    setEditingFolder(folderId);
    setEditedFolderName(folderName);
  };

  const handleSaveEditedFolder = async () => {
    if (editingFolder && editedFolderName) {
      setSavingFolderProcess(true);
      try {
        await updateFolderName(editingFolder, editedFolderName);
        await fetchFolders();
        await refreshProjects();
        setEditingFolder(null);
        setEditedFolderName('');
      } catch (error) {
        console.error('Error saving edited folder:', error);
      } finally {
        setSavingFolderProcess(false);
      }
    }
  };

  const handleCancelEditFolder = () => {
    setEditingFolder(null);
    setEditedFolderName('');
  };

  return (
    <div className="w-full lg:w-80 space-y-6 lg:-mt-3.5">
      <div>
        <h2 className="text-2xl lg:text-2xl font-light relative">{t('folders', 'Folders')}</h2>
        <p className="text-gray-400 font-light text-sm lg:text-base relative">
          {t('organizeProjects', 'Organize your projects by category')}
        </p>
      </div>
      {showNewFolderInput && (
        <>
          <div className="fixed inset-0 bg-black/45 z-40 sm:hidden"></div>
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className={` ${'fixed inset-0 z-50 md:z-10'} bg-gradient-to-r from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-3 lg:p-4 overflow-hidden  h-[150px] w-full mt-0! top-1/3 md:relative md:w-full md:h-auto md:mx-auto md:rounded-lg md:top-auto`}
          >
            <div className="absolute -bottom-8 -right-8 text-[150px] opacity-5">
              <FaFolderPlus />
            </div>
            <div className="relative z-10">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-1.5 lg:p-2 bg-black/20 rounded-lg">
                  <FaFolderPlus className="text-green-400 text-sm lg:text-base" />
                </div>
                <span className="font-light text-sm lg:text-base">{t('createNewFolder', 'Create a new folder')}</span>
              </div>
              <input
                type="text"
                value={newFolderName}
                onChange={(e) => setNewFolderName(e.target.value)}
                placeholder={t('enterFolderName', 'Enter folder name')}
                className="w-full bg-black/20 rounded-lg px-3 lg:px-4 py-2 lg:py-3 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 font-light text-sm lg:text-base"
              />
              <div className="flex justify-end gap-2 mt-4">
                <button
                  onClick={handleCancelNewFolder}
                  className="px-2 lg:px-3 py-1.5 lg:py-2 bg-black/20 hover:bg-black/30 rounded-lg transition-colors font-light flex items-center gap-2 text-sm lg:text-base"
                >
                  <FaTimes />
                  {t('cancel', 'Cancel')}
                </button>
                <button
                  onClick={handleCreateNewFolder}
                  className="px-2 lg:px-3 py-1.5 lg:py-2 bg-green-500 hover:bg-green-600 rounded-lg transition-colors font-light flex items-center gap-2 text-sm lg:text-base"
                  disabled={creatingFolderProcess}
                >
                  {creatingFolderProcess ? <FaSpinner className="animate-spin" /> : <FaPlus />}
                  {t('save', 'Save')}
                </button>
              </div>
            </div>
          </motion.div>
        </>
      )}

      <div className="relative">
        <motion.button
          key="all"
          onClick={() => handleFilterChange('all')}
          className={`w-full bg-[#1b2a53] rounded-xl p-4 text-left transition-all hover:scale-[1.02] ${
            selectedType === 'all' ? 'ring-2 ring-green-500' : ''
          } relative overflow-hidden border border-white/5`}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <div className="absolute -bottom-4 -right-4 w-24 h-24 flex items-center justify-center">
            <FaLayerGroup className="w-full h-full opacity-5" />
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="p-2 bg-black/20 rounded-lg">
                <FaLayerGroup className="text-xl" />
              </div>
              <span className="font-light">{t('all', 'All')}</span>
            </div>
          </div>
        </motion.button>
        <div className="bg-black/70 px-2.5 py-1 rounded-full text-sm font-light absolute -top-2.5 -right-2.5">
          {allProjectsCount}
        </div>
      </div>
      {loading ? (
        <div className="flex justify-center items-center">
          <FaSpinner className="animate-spin text-green-400 text-3xl" />
        </div>
      ) : (
        userCreatedFolders.map((folder, index) => {
          const Icon = getIconByType('folder');
          return (
            <div key={`${folder.name}-${index}`} className="rounded-xl relative">
              <motion.div
                onClick={() => handleFilterChange(folder._id)}
                className={`group cursor-pointer w-full ${colors[index % colors.length]} backdrop-blur-sm rounded-xl p-4 text-left transition-all hover:scale-[1.02] ${
                  selectedType === folder._id ? 'ring-2 ring-green-500' : ''
                } relative overflow-hidden border border-white/5`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="absolute -bottom-4 -right-4 w-24 h-24 flex items-center justify-center">
                  <Icon className="w-full h-full opacity-5" />
                </div>
                <div className="flex justify-between relative" style={{ zIndex: '999' }}>
                  <div className="flex items-center gap-2">
                    <div className="p-2 bg-black/20 rounded-lg">
                      <Icon className="text-xl" />
                    </div>
                    {editingFolder === folder._id ? (
                      <input
                        type="text"
                        value={editedFolderName}
                        onChange={(e) => setEditedFolderName(e.target.value)}
                        className="bg-black/20 rounded-lg w-[150px] px-3 py-1 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 font-light text-sm"
                      />
                    ) : deletingFolder === folder.name ? (
                      <span className="font-light">{t('deleteFolder', 'Delete folder')}?</span>
                    ) : (
                      <span className="font-light lg:max-w-[135px] lg:truncate">{folder.name}</span>
                    )}
                  </div>
                  <div className="flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pl-2">
                    {editingFolder === folder._id ? (
                      <>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSaveEditedFolder();
                          }}
                          className="p-1.5 lg:p-2 bg-green-500/80 hover:bg-green-700/80 rounded-lg transition-colors group"
                          data-tooltip-id="project-tooltip1"
                          data-tooltip-content={t('save', 'Save')}
                          disabled={savingFolderProcess}
                        >
                          {savingFolderProcess ? (
                            <FaSpinner className="animate-spin text-gray-400" />
                          ) : (
                            <FaCheck className="text-gray-400 group-hover:text-white" />
                          )}
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleCancelEditFolder();
                          }}
                          className="p-1.5 lg:p-2 bg-red-500/80 hover:bg-red-700/80 rounded-lg transition-colors group"
                          data-tooltip-id="project-tooltip1"
                          data-tooltip-content={t('cancel', 'Cancel')}
                        >
                          <FaTimes className="text-gray-400 group-hover:text-white transition-colors text-sm lg:text-base" />
                        </button>
                        <ReactTooltip id="project-tooltip1" place="top" effect="solid" delayShow={0} />
                      </>
                    ) : (
                      <>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditFolder(folder._id, folder.name);
                          }}
                          className="p-1.5 lg:p-2 bg-yellow-500/80 hover:bg-yellow-700/80 rounded-lg transition-colors group"
                          data-tooltip-id="project-tooltip2"
                          data-tooltip-content={t('editName', 'Edit Name')}
                        >
                          <FaPencil className="text-gray-400 group-hover:text-white transition-colors text-sm lg:text-base" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRequestDeleteFolder(folder.name, folder._id);
                          }}
                          className="p-1.5 lg:p-2 bg-red-500/80 hover:bg-red-700/80 rounded-lg transition-colors group"
                          data-tooltip-id="project-tooltip2"
                          data-tooltip-content={t('deleteFolder', 'Delete Folder')}
                        >
                          <FaTrash className="text-gray-400 group-hover:text-white transition-colors text-sm lg:text-base" />
                        </button>
                        <ReactTooltip id="project-tooltip2" place="top" effect="solid" delayShow={0} />
                      </>
                    )}
                  </div>
                </div>
              </motion.div>
              {editingFolder !== folder._id && deletingFolder !== folder.name && folder.projectIds.length > 0 && (
                <div className="bg-black/70 px-2.5 py-1 rounded-full text-sm font-light absolute -top-2.5 -right-2.5">
                  {folder.projectIds.length}
                </div>
              )}
            </div>
          );
        })
      )}
      <motion.button
        key="create-folder"
        onClick={() => setShowNewFolderInput(true)}
        className={`w-full bg-[#17373f] bg-opacity-50 backdrop-blur-sm rounded-xl p-4 text-left transition-all hover:scale-[1.02] ${
          selectedType === 'create-folder' ? 'ring-2 ring-green-500' : ''
        } relative overflow-hidden border border-white/5`}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <div className="absolute -bottom-4 -right-4 w-24 h-24 flex items-center justify-center">
          <FaFolderPlus className="w-full h-full opacity-5" />
        </div>
        <div className="relative z-10 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="p-2 bg-black/20 rounded-lg">
              <FaFolderPlus className="text-xl" />
            </div>
            <span className="font-light">{t('createNewFolder', 'Create a new folder')}</span>
          </div>
        </div>
      </motion.button>
      <AnimatePresence>
        {deleteConfirmationModal.open && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={handleCancelDeleteFolder}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-900 rounded-2xl max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6 border-b border-gray-800 flex justify-between items-center">
                <h3 className="text-xl font-light text-red-400">{t('confirmDeleteFolder', 'Confirm Delete Folder')}</h3>
                <button
                  onClick={handleCancelDeleteFolder}
                  className="p-2 hover:bg-gray-800 rounded-full transition-colors bg-transparent"
                >
                  <FaTimes className="text-gray-400" />
                </button>
              </div>

              <div className="p-6">
                <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4 mb-4">
                  <div className="flex items-start gap-3">
                    <FaExclamationTriangle className="text-red-400 mt-1" />
                    <p className="text-gray-300">
                      {t('deleteFolderWarning', {
                        folderName: deleteConfirmationModal.folder || 'this folder',
                      })}
                    </p>
                  </div>
                </div>

                {deleteSuccess ? (
                  <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      <FaCheck className="text-green-400" />
                      <p className="text-gray-300">{t('folderDeletedSuccessfully', 'Folder deleted successfully')}</p>
                    </div>
                  </div>
                ) : deleteConfirmationModal.folder ? (
                  <div className="bg-gray-800/50 rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 rounded-lg bg-gray-800 flex items-center justify-center flex-shrink-0">
                        <FaFolder className="text-gray-400 text-xl" />
                      </div>
                      <div>
                        <div className="font-medium text-white">{deleteConfirmationModal.folder}</div>
                        <div className="text-sm text-gray-400">
                          {userCreatedFolders.find((f) => f._id === deleteConfirmationModal.id)?.projectIds.length || 0}{' '}
                          projects
                        </div>
                      </div>
                    </div>
                  </div>
                ) : null}
              </div>

              <div className="p-6 border-t border-gray-800 flex justify-end gap-3">
                <button
                  onClick={handleCancelDeleteFolder}
                  className={`px-4 py-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors text-white ${deletingFolderProcess ? 'opacity-50 cursor-not-allowed' : ''}`}
                  disabled={deletingFolderProcess}
                >
                  {t('cancel', 'Cancel')}
                </button>
                <button
                  onClick={handleConfirmDeleteFolder}
                  className={`px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors text-white flex items-center gap-2 ${deletingFolderProcess || deleteSuccess ? 'opacity-50 cursor-not-allowed' : ''}`}
                  disabled={deletingFolderProcess || deleteSuccess}
                >
                  {deletingFolderProcess ? (
                    <FaSpinner className="animate-spin" />
                  ) : (
                    <>
                      <FaTrash />
                      <span>{t('confirm', 'Confirm')}</span>
                    </>
                  )}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

export default ProjectsTypeFilter;