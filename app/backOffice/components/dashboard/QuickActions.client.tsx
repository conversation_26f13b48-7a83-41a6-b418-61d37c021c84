import { useChatHistory } from '~/ai/lib/persistence';
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  FaComments,
  FaDesktop,
  FaFileAlt,
  FaFolderPlus,
  FaLightbulb,
  FaPlus,
  FaRocket,
  FaUpload,
} from 'react-icons/fa';
import ProjectImport from '~/backOffice/components/project/ProjectImportModal';
import AddDatabase from '~/backOffice/components/project/AddDatabaseModal';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Message } from 'ai';
import ChatImportModal from '../project/ChatImportModal';

const iconComponents = {
  plus: FaPlus,
  comments: FaComments,
  upload: FaUpload,
  folderPlus: FaFolderPlus,
  rocket: FaRocket,
  file: FaFileAlt,
  lightbulb: FaLightbulb,
  desktop: FaDesktop,
};

type Action = {
  id: string;
  iconName: keyof typeof iconComponents;
  label: string;
  subtitle: string;
  bg: string;
  iconColor: string;
  link?: string;
  blur?: boolean;
};

// Chat Data
type ChatData = {
  messages?: Message[]; // Standard biela format
  description?: string; // Optional description
};

type QuickActionsProps = {
  actions?: Action[];
  setShowNewFolderInput: (value: boolean) => void;
  importChat?: (description: string, messages: Message[]) => Promise<void>;
};

function QuickActions({ actions, setShowNewFolderInput }: QuickActionsProps) {
  const { t } = useTranslation('translation');
  const navigate = useNavigate();
  const [modalType, setModalType] = useState<'import' | 'addDatabase' | 'chatImport' | null>(null);
  const { importChat } = useChatHistory();
  const getIcon = (iconName: keyof typeof iconComponents) => {
    const IconComponent = iconComponents[iconName];
    return IconComponent ? <IconComponent /> : null;
  };

  // Trigger File Input:
  const triggerFileInputClick = (inputId: string) => {
    const fileInput = document.getElementById(inputId);
    fileInput?.click();
  };

  // Handle ImportChat:
  const handleImportChat = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) {
      toast.error('No file selected.');
      return;
    }
    if (!importChat) {
      console.log('Import chat undefined');
      return;
    }
    if (file && importChat) {
      try {
        const reader = new FileReader();
        reader.onload = async (e) => {
          console.log('File read finished:', e.target?.result);
          try {
            const content = e.target?.result as string;
            const data = JSON.parse(content) as ChatData;
            // Standard format
            if (Array.isArray(data.messages)) {
              await importChat(data.description || 'Imported Chat', data.messages);
              return;
            }
            toast.error('Invalid chat file format');
          } catch (error: unknown) {
            if (error instanceof Error) {
              toast.error('Failed to parse chat file: ' + error.message);
            } else {
              toast.error('Failed to parse chat file');
            }
          }
        };
        reader.onerror = () => toast.error('Failed to read chat file');
        reader.readAsText(file);
      } catch (error) {
        toast.error(error instanceof Error ? error.message : 'Failed to import chat');
      }
      e.target.value = ''; // Reset file input
    } else {
      toast.error('Something went wrong');
    }
  };

  const handleActionClick = (action: Action) => {
    if (action.link) {
      navigate(action.link);
      return;
    }

    switch (action.id) {
      case 'get-started':
        navigate('/get-started');
        break;
      case 'create-chat':
        window.location.href = '/';
        break;
      case 'upload':
        setModalType('import');
        break;
      case 'import-chat':
        setModalType('chatImport');
        break;

      case 'new-folder':
        setShowNewFolderInput(true);
        break;
      default:
        break;
    }
  };

  const defaultActions: Action[] = [
    {
      id: 'create-chat',
      iconName: 'plus',
      label: t('createProject', 'Create New Project'),
      subtitle: t('createProjectSub', 'Start from scratch'),
      bg: 'bg-transparent',
      iconColor: 'text-[#34d399]',
    },
    {
      id: 'upload',
      iconName: 'upload',
      label: t('uploadProject', 'Upload Project'),
      subtitle: t('uploadProjectSub', 'Import existing project'),
      bg: 'bg-transparent',
      iconColor: 'text-[#a78bfa]',
    },
    {
      id: 'import-chat',
      iconName: 'comments',
      label: t('importChat', 'Import Chat'),
      subtitle: t('importChatSub', 'Import existing Chat'),
      bg: 'bg-transparent',
      iconColor: 'text-[#facc15]',
    },
    {
      id: 'new-folder',
      iconName: 'folderPlus',
      label: t('createFolder', 'Create a new folder'),
      subtitle: t('createFolderSub', 'Organize your projects'),
      bg: 'bg-transparent',
      iconColor: 'text-[#2dd4bf]',
    },
    {
      id: 'get-started',
      iconName: 'plus',
      label: t('getStartedTitle', 'Get Started'),
      subtitle: t('getStartedSub', 'Explore how Biela.dev works'),
      bg: 'bg-transparent',
      iconColor: 'text-[#60a5fa]',
      blur: true,
    },
  ];

  const actionsToRender = actions?.length ? actions : defaultActions;

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 !mt-12">
      {actionsToRender.map((action, index) => (
        <motion.button
          key={index}
          onClick={() => handleActionClick(action)}
          className={`bg-[#80808036] rounded-xl p-6 text-left transition-all hover:scale-[1.02] relative overflow-hidden ${
            action.blur ? 'blur-sm pointer-events-none' : ''
          } ${action.id === 'create-chat' && 'text-white shadow-md shadow-green-500/50'}`}
        >
          <div className="absolute -bottom-8 -right-8 text-[100px] opacity-5">{getIcon(action.iconName)}</div>
          <div className="absolute inset-0 bg-gradient-to-br from white/[0.04] to-transparent" />
          <div className="absolute -bottom-24 -right-24 w-48 h-48 bg-white/[0.02] rounded-full blur-2xl" />
          <div className="absolute -top-24 -left-24 w-48 h-48 bg-white/[0.02] rounded-full blur-2xl" />

          <div className="relative z-10">
            <div className="flex items-center gap-4">
              <div className={`${action.iconColor} text-xl`}>{getIcon(action.iconName)}</div>
              <div>
                <p className="font-light text-white">{action.label}</p>
                <p className="text-sm text-gray-400 font-light">{action.subtitle}</p>
              </div>
            </div>
          </div>
        </motion.button>
      ))}

      {modalType === 'import' && (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          <div className="absolute inset-0" onClick={() => setModalType(null)} />
          <ProjectImport closeModal={() => setModalType(null)} />
        </div>
      )}
      {modalType === 'addDatabase' && (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          <div className="absolute inset-0" onClick={() => setModalType(null)} />
          <AddDatabase
            closeModal={() => setModalType(null)}
            handleAddDatabaseClick={() => console.log('Upload Project Clicked')}
          />
        </div>
      )}

      {/* Input For Import Chat */}
      {modalType === 'chatImport' && importChat && (
        <ChatImportModal closeModal={() => setModalType(null)} importChat={importChat} />
      )}
    </div>
  );
}

export default QuickActions;
