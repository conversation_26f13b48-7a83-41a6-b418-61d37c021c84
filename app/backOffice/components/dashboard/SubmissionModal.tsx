import React, { useEffect, useState } from 'react';
import { AlertTriangle } from 'lucide-react';
import { useTranslation } from 'react-i18next';

type SubmissionModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onViewContest: () => void;
  onRetry: () => void;
  type?: 'publish' | 'refresh';
  state?: 'loading' | 'error' | 'success';
  projectName?: string;
};

const SubmissionModal: React.FC<SubmissionModalProps> = ({
  isOpen,
  onClose,
  onViewContest,
  onRetry,
  type = 'publish',
  state = 'loading',
  projectName = 'Project',
}) => {
  const { t } = useTranslation();
  const [loadingState, setLoadingState] = useState({
    isLoading: true,
    currentStep: 0,
    isComplete: false,
  });

  const loadingMessages = {
    publish: [
      t('loadingMessages.publish.0', 'Getting project information...'),
      t('loadingMessages.publish.1', 'Making a screenshot...'),
      t('loadingMessages.publish.2', 'Generating summary and description...'),
    ],
    refresh: [
      t('loadingMessages.refresh.0', 'Retrieving current submission data...'),
      t('loadingMessages.refresh.1', 'Updating project screenshot...'),
      t('loadingMessages.refresh.2', 'Refreshing project details...'),
    ],
  };

  const errorMessages = {
    publish: t('errorMessages.publish', {
      projectName,
      defaultValue: `Unable to publish "${projectName}" to the contest. There was an issue processing your request.`,
    }),
    refresh: t('errorMessages.refresh', {
      projectName,
      defaultValue: `Unable to refresh "${projectName}" submission. The server couldn't update your project information.`,
    }),
  };

  useEffect(() => {
    if (isOpen && state === 'loading') {
      const intervals = [3000, 6000, 9000];
      const messages = loadingMessages[type] || loadingMessages.publish;

      if (loadingState.currentStep < messages.length) {
        setTimeout(() => {
          setLoadingState((prev) => ({
            ...prev,
            currentStep: prev.currentStep + 1,
          }));
        }, intervals[loadingState.currentStep]);
      }

      if (loadingState.currentStep === messages.length - 1) {
        setTimeout(() => {
          setLoadingState((prev) => ({
            ...prev,
            isLoading: false,
            isComplete: true,
          }));
        }, 2500);
      }
    }
  }, [isOpen, loadingState.currentStep, state, type]);

  useEffect(() => {
    if (!isOpen) {
      setTimeout(() => {
        setLoadingState({
          isLoading: true,
          currentStep: 0,
          isComplete: false,
        });
      }, 300);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const messages = loadingMessages[type] || loadingMessages.publish;
  const errorMessage = errorMessages[type] || errorMessages.publish;

  const getTitle = () => {
    return type === 'refresh'
      ? t('actions.refresh.title', 'Refreshing Submission')
      : t('actions.publish.title', 'Publishing to Contest');
  };

  const getSuccessMessage = () => {
    return type === 'refresh'
      ? t('actions.refresh.successMessage', 'Your project submission has been successfully updated.')
      : t('actions.publish.successMessage', 'Your project has been submitted to the contest.');
  };

  const getButtonText = () => {
    return type === 'refresh'
      ? t('actions.refresh.buttonText', 'View Updated Submission')
      : t('actions.publish.buttonText', 'View in Contest Page');
  };

  const renderContent = () => {
    if (state === 'loading') {
      return (
        <>
          <div className="w-16 h-16 mb-6">
            <svg
              className="animate-spin w-full h-full text-[#10B981]"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </div>
          <h3 className="text-xl font-medium text-[#F8FAFC] mb-2">{getTitle()}</h3>
          <p className="text-[#94A3B8] text-center min-h-[24px]">
            {messages[Math.min(loadingState.currentStep, messages.length - 1)]}
          </p>
        </>
      );
    } else if (state === 'error') {
      return (
        <>
          <div className="w-16 h-16 mb-6 text-[#EF4444]">
            <AlertTriangle size={64} strokeWidth={1.5} />
          </div>
          <h3 className="text-xl font-medium text-[#F8FAFC] mb-3 text-center">{t('error', 'Error')}</h3>
          <p className="text-[#94A3B8] text-center mb-6">{errorMessage}</p>
          <div className="flex flex-col space-y-3 w-full">
            <button
              onClick={onRetry}
              className="w-full py-2.5 bg-gradient-to-r from-[#10B981] to-[#059669] text-white rounded-lg font-medium hover:from-[#059669] hover:to-[#10B981] transition-all"
            >
              {t('tryAgain', 'Try Again')}
            </button>
            <button
              onClick={onClose}
              className="w-full py-2.5 bg-[#334155]/30 text-[#F8FAFC] rounded-lg hover:bg-[#334155]/40 transition-colors"
            >
              {t('abortButton', 'Cancel')}
            </button>
          </div>
        </>
      );
    } else {
      return (
        <>
          <div className="w-16 h-16 mb-6 text-[#10B981]">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          </div>
          <h3 className="text-xl font-medium text-[#F8FAFC] mb-3">{t('succes', 'Success!')}</h3>
          <p className="text-[#94A3B8] text-center mb-6">{getSuccessMessage()}</p>
          <button onClick={onViewContest} className="project-action-contest w-full justify-center py-2.5">
            {getButtonText()}
          </button>
        </>
      );
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="absolute inset-0 bg-[#0F172A]/80 backdrop-blur-sm"
        onClick={state !== 'loading' ? onClose : undefined}
      ></div>
      <div className="relative z-10 w-full max-w-md bg-[#1E293B] rounded-xl shadow-2xl border border-[#334155] overflow-hidden">
        <div className="p-6">
          <div className="flex flex-col items-center">{renderContent()}</div>
        </div>
        {state !== 'loading' && (
          <button
            onClick={onClose}
            className="absolute top-4 right-4 bg-transparent text-[#94A3B8] hover:text-[#F8FAFC] transition-colors"
            aria-label="Close modal"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        )}
      </div>
    </div>
  );
};

export default SubmissionModal;
