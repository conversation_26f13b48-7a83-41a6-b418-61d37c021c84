import React, { useLayoutEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

interface TooltipProps {
  anchor: React.RefObject<HTMLElement>;
  open: boolean;
  children: React.ReactNode;
  widthClass?: string; // e.g. "w-64"
}

export function Tooltip({ anchor, open, children, widthClass = 'w-64' }: TooltipProps) {
  const [pos, setPos] = useState<{ top: number; left: number }>({ top: 0, left: 0 });
  const tooltipRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    if (anchor.current && tooltipRef.current) {
      const rect = anchor.current.getBoundingClientRect();
      const tooltipRect = tooltipRef.current.getBoundingClientRect();

      setPos({
        top: rect.top + window.scrollY - tooltipRect.height - 8,
        left: rect.right + window.scrollX - tooltipRect.width,
        // assumes Tailwind spacing: w-64 → 16rem → 256px
      });
    }
  }, [anchor, open, widthClass, children]);

  if (!open) return null;

  return createPortal(
    <div
      ref={tooltipRef}
      style={{ position: 'absolute', top: pos.top, left: pos.left, zIndex: 10000 }}
      className={`bg-[#1A1F2E] rounded-lg border border-white/10 p-3 ${widthClass} transition-opacity`}
    >
      {children}
    </div>,
    document.body
  );
}
