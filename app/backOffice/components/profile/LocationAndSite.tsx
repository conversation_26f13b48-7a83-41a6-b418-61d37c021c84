import React, { useState } from 'react';
import {
  FaArrowRight,
  FaExclamationTriangle,
  FaEye,
  FaEyeSlash,
  FaGithub,
  FaGlobe,
  FaGoogle,
  FaMapMarkerAlt,
  FaTimes
} from 'react-icons/fa';
import { RiLockPasswordFill } from 'react-icons/ri';
import { useTranslation } from 'react-i18next';
import { countries } from '~/backOffice/components/profile/languageList';
import { AuthSectionProps, InputFieldProps, IUserGeneral, SectionProps } from '~/backOffice/types/profile';
import { motion } from 'framer-motion';
import { IUserData } from '~/routes/profile';
import { handleGitHubAuth, handleGoogleAuth } from '~/ai/lib/stores/user/user';

type LocationAndSiteProps = IUserGeneral & {
  userData: IUserData;
  errors: Partial<Record<keyof IUserData, string>>;
  handleBlur: (field: keyof IUserData) => void;
  handleVerify: (field: 'email' | 'phone') => void;
  isEditing: boolean;
  handleConfirmDeleteAccount: () => void;
  handleSave: () => void;
  password: string;
  setPassword: (value: string) => void;
  openDeleteModal: boolean;
  setOpenDeleteModal: (value: boolean) => void;
  handleChangeContactInfo: (field: keyof IUserData, value: string) => void;
  currentPassword: string;
  setCurrentPassword: (value: string) => void;
  newPassword: string;
  setNewPassword: (value: string) => void;
  repeatNewPassword: string;
  setRepeatNewPassword: (value: string) => void;
  isEditingPassword: boolean;
  isUpdatingPassword: boolean;
  handleToggleEditPassword: () => void;
  handleUpdatePassword: () => Promise<void>;
};

const LocationAndSite: React.FC<LocationAndSiteProps> = ({
   handleChange,
   isEditing,
   errors,
   handleBlur,
   handleVerify,
   userData,
   handleConfirmDeleteAccount,
   handleSave,
   password,
   setPassword,
   openDeleteModal,
   setOpenDeleteModal,
   handleChangeContactInfo,
   currentPassword,
   setCurrentPassword,
   newPassword,
   setNewPassword,
   repeatNewPassword,
   setRepeatNewPassword,
   isEditingPassword,
   isUpdatingPassword,
   handleToggleEditPassword,
   handleUpdatePassword,
 }) => {
  const { t } = useTranslation('profile');

  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showRepeatPassword, setShowRepeatPassword] = useState(false);

  const togglePasswordVisibility = (field: 'current' | 'new' | 'repeat') => {
    if (field === 'current') {
      setShowCurrentPassword(!showCurrentPassword);
    }

    if (field === 'new') {
      setShowNewPassword(!showNewPassword);
    }

    if (field === 'repeat') {
      setShowRepeatPassword(!showRepeatPassword);
    }
  };

  const getFlagUrl = (countryName: string) => {
    const country = countries.find((c) => c.name === countryName);

    if (country) {
      return `https://cdn.jsdelivr.net/gh/hjnilsson/country-flags@latest/svg/${country.code.toLowerCase()}.svg`;
    }

    return '';
  };

  return (
    <div className="space-y-8">
      {userData?.country && userData?.country !== '' && (
        <Section title={t('location', 'Location')} icon={FaMapMarkerAlt}>
          <div className="flex items-center gap-3">
            <img src={getFlagUrl(userData.country)} alt={userData.country} className="w-8 h-8" />
            <span className="font-light text-white">{userData.country}</span>
          </div>
        </Section>
      )}

      <Section title="Site" icon={FaGlobe}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <InputField
            label={t('instagram', 'Instagram')}
            type="url"
            value={userData.instagram}
            onChange={(value) => handleChange('instagram', value)}
            onBlur={() => handleBlur('instagram')}
            error={errors.instagram}
            disabled={!isEditing}
          />
          <InputField
            label={t('twitterX', 'Twitter/X')}
            type="url"
            value={userData.twitter}
            onChange={(value) => handleChange('twitter', value)}
            onBlur={() => handleBlur('twitter')}
            error={errors.twitter}
            disabled={!isEditing}
          />
          <InputField
            label={t('linkedin', 'LinkedIn')}
            type="url"
            value={userData.linkedin}
            onChange={(value) => handleChange('linkedin', value)}
            onBlur={() => handleBlur('linkedin')}
            error={errors.linkedin}
            // readOnly
            disabled={!isEditing}
          />
          <InputField
            label={t('youtube', 'YouTube')}
            type="url"
            value={userData.youtube}
            onChange={(value) => handleChange('youtube', value)}
            onBlur={() => handleBlur('youtube')}
            error={errors.youtube}
            // readOnly
            disabled={!isEditing}
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <InputField
            label={t('tiktok', 'TikTok')}
            type="url"
            value={userData.tiktok}
            onChange={(value) => handleChange('tiktok', value)}
            onBlur={() => handleBlur('tiktok')}
            error={errors.tiktok}
            // readOnly
            disabled={!isEditing}
          />
          <InputField
            label={t('facebook', 'Facebook')}
            type="url"
            value={userData.facebook}
            onChange={(value) => handleChange('facebook', value)}
            onBlur={() => handleBlur('facebook')}
            error={errors.facebook}
            // readOnly
            disabled={!isEditing}
          />
        </div>
        <InputField
          label={t('personalWebsite', 'Personal Website')}
          type="url"
          value={userData.website}
          onChange={(value) => handleChange('website', value)}
          onBlur={() => handleBlur('website')}
          error={errors.website}
          // readOnly
          disabled={!isEditing}
        />
      </Section>


      {userData?.provider === 0 && <AuthSection platform="Github" icon={FaGithub} connected={true} />}

      {userData?.provider === 1 && (
        <AuthSection platform="Google" email={userData.email} icon={FaGoogle} connected={true} />
      )}

      <Section title={t('ChangePassword', 'Change Password')} icon={RiLockPasswordFill}>
        <button
          onClick={isEditingPassword ? handleUpdatePassword : handleToggleEditPassword}
          disabled={isUpdatingPassword}
          className={`absolute top-[-25px] right-2 px-2 lg:px-4 py-1 lg:py-2 min-w-[70px] text-base flex justify-center bg-green-500 text-white rounded-lg shadow-lg cursor-pointer transition duration-300`}
        >
          {isUpdatingPassword ? t('Updating', 'Updating') : isEditingPassword ? t('Save', 'Save') : t('Edit', 'Edit')}
        </button>

        <div className="space-y-2 mb-4">
          <label className="text-[#9CA3AF] text-sm font-light">{t('CurrentPassword', 'Current Password')}</label>
          <div className="relative">
            <input
              type={showCurrentPassword ? 'text' : 'password'}
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
              disabled={!isEditingPassword || isUpdatingPassword}
              className="w-full bg-[#********] border border-[#374151] rounded-lg px-4 py-3 text-white font-light focus:outline-none focus:ring-2 focus:ring-[#22C55E] transition-all pr-10"
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility('current')}
              className="absolute inset-y-0 right-0 pr-3 bg-transparent flex items-center text-gray-400"
              disabled={!isEditingPassword || isUpdatingPassword}
            >
              {showCurrentPassword ? <FaEye className="w-5 h-5" /> : <FaEyeSlash className="w-5 h-5" />}
            </button>
          </div>
        </div>

        <div className="space-y-2 mb-4">
          <label className="text-[#9CA3AF] text-sm font-light">{t('NewPassword', 'New Password')}</label>
          <div className="relative">
            <input
              type={showNewPassword ? 'text' : 'password'}
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              disabled={!isEditingPassword || isUpdatingPassword}
              className="w-full bg-[#********] border border-[#374151] rounded-lg px-4 py-3 text-white font-light focus:outline-none focus:ring-2 focus:ring-[#22C55E] transition-all pr-10"
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility('new')}
              className="absolute inset-y-0 right-0 pr-3 bg-transparent flex items-center text-gray-400 hover:text-white focus:outline-none"
              disabled={!isEditingPassword || isUpdatingPassword}
            >
              {showNewPassword ? <FaEye className="w-5 h-5" /> : <FaEyeSlash className="w-5 h-5" />}
            </button>
          </div>
        </div>

        <div className="space-y-2">
          <label className="text-[#9CA3AF] text-sm font-light">{t('RepeatNewPassword', 'Repeat New Password')}</label>
          <div className="relative">
            <input
              type={showRepeatPassword ? 'text' : 'password'}
              value={repeatNewPassword}
              onChange={(e) => setRepeatNewPassword(e.target.value)}
              disabled={!isEditingPassword || isUpdatingPassword}
              className="w-full bg-[#********] border border-[#374151] rounded-lg px-4 py-3 text-white font-light focus:outline-none focus:ring-2 focus:ring-[#22C55E] transition-all pr-10"
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility('repeat')}
              className="absolute inset-y-0 right-0 pr-3 bg-transparent flex items-center text-gray-400 hover:text-white focus:outline-none"
              disabled={!isEditingPassword || isUpdatingPassword}
            >
              {showRepeatPassword ? <FaEye className="w-5 h-5" /> : <FaEyeSlash className="w-5 h-5" />}
            </button>
          </div>
        </div>
      </Section>

      <Section title={t('accountDeletion', 'Account Deletion')} icon={FaExclamationTriangle} className="bg-[#DC26261A]">
        <p className="text-[#9CA3AF] font-light">{t('deleteAccountWarning', 'In case of deletion you\'ll remove all your projects and personal data. Also you\'ll lose control of projects in your organizations.')}</p>
        <button
          onClick={() => setOpenDeleteModal(true)}
          className="bg-[#EF4444] py-[12px] px-[24px] hover:bg-[#DC2626] rounded-lg transition-colors font-light text-white"
        >
          {t('deleteAccount', 'Delete Account')}
        </button>
        {/* Confirmation Modal */}
        {openDeleteModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setOpenDeleteModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-r from-[#1f293780] to-[#********] shadow-md  rounded-2xl max-w-md w-full overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6 border-b border-gray-800 flex justify-between items-center">
                <h3 className="text-2xl font-light text-gray-200">Confirm Delete Account</h3>
                <button
                  onClick={() => setOpenDeleteModal(false)}
                  className="p-2 bg-gray-800 hover:bg-red-600 rounded-full transition-colors"
                >
                  <FaTimes className="text-white" />
                </button>
              </div>
              <div className="p-6">
                <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4 mb-4">
                  <div className="flex items-start gap-3">
                    {/* <FaExclamationTriangle className="text-red-400 mt-1" /> */}
                    <p className="text-gray-300">
                      Are you sure you want to delete this account? This action cannot be undone.
                    </p>
                  </div>
                </div>

                {/* Password Input as InputField */}
                <div className="space-y-2 mb-4">
                  <label className="text-[#9CA3AF] text-sm font-light">Enter your password to confirm:</label>
                  <input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full bg-[#********] border border-[#374151] rounded-lg px-4 py-3 text-white font-light focus:outline-none focus:ring-2 focus:ring-[#22C55E] transition-all"
                  />
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end gap-3">
                  <button
                    onClick={() => setOpenDeleteModal(false)}
                    className="bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded-lg text-white transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleConfirmDeleteAccount}
                    className={`bg-red-600 hover:bg-red-500 px-4 py-2 rounded-lg text-white transition-colors ${!password ? 'cursor-not-allowed opacity-50' : ''}`}
                    disabled={!password}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </Section>
    </div>
  );
};

const Section: React.FC<SectionProps> = ({ title, icon: Icon, children, className }) => (
  <div
    className={`bg-gradient-to-r from-[#1f293780] to-[#********] rounded-xl p-6 relative overflow-hidden hover:border hover:border-green-500 ${className} border-[#191429] border-1 `}
  >
    <div className="absolute -bottom-8 -right-8 text-[150px] opacity-5">
      <Icon className={'text-[#e5e7eb]'} />
    </div>
    <div className="relative z-10 space-y-6">
      <h3 className="text-xl font-light text-white">{title}</h3>
      {children}
    </div>
  </div>
);

interface InputFieldProps {
  label: string
  type: string
  value: string
  onChange?: (val: string) => void
  disabled?: boolean
  className?: string
  onBlur?: () => void
  error?: string
}

const InputField: React.FC<InputFieldProps> = ({label, type, value, onChange, disabled, className = '', onBlur, error }) => (
  <div className={`${className} space-y-2`}>
    <label className="text-[#9CA3AF] text-sm font-light">{label}</label>
    <input
      type={type}
      value={value}
      disabled={disabled}
      onChange={onChange ? (e) => onChange(e.target.value) : undefined}
      onBlur={onBlur}
      className={
        'w-full bg-[#********] border border-[#374151] rounded-lg px-4 py-3 text-white font-light focus:outline-none focus:ring-2 focus:ring-[#22C55E] transition-all'
      }
    />
    {error && (
      <p className="text-red-500 text-sm">
        {error}
      </p>
    )}
  </div>
);

const AuthSection: React.FC<AuthSectionProps> = ({ platform, icon: Icon, connected, email }) => {
  const { t } = useTranslation('profile');
  return (
    <Section title={platform} icon={Icon} className={''}>
      {connected ? (
        <div className="flex items-center justify-between">
          <div className={'flex items-center gap-2 group'}>
            <Icon className="text-white text-[20px]" />
            <span className="text-[#9CA3AF]">
              {t('connectAccount', { platform, defaultValue: 'Connect your {{platform}} account' })}
            </span>
          </div>
          <span className="text-[#4ADE80]">{t('connected', 'Connected')}</span>
        </div>
      ) : (
        <div
        onClick={() => {
          const state = crypto.randomUUID();
          if (platform === 'GitHub') {
            handleGitHubAuth();
          } else {
            handleGoogleAuth(state);
          }
        }}

          className="auth-link cursor-pointer"
        >
          <div className={'flex items-center justify-between mt-[24px]'}>
            <div className={'flex items-center gap-2 group'}>
              <Icon className="text-white text-[20px]" />
              <span className="text-[#9CA3AF]">
                {t('connectAccount', { platform, defaultValue: 'Connect your {{platform}} account' })
              }</span>
            </div>
            <div className={'flex items-center gap-2 group'}>
              <span className={'text-[#9CA3AF] text-[14px]'}>{t('connectNow', 'Connect Now')}</span>
              <FaArrowRight className="text-sm group-hover:translate-x-1 transition-transform text-[#9CA3AF]" />
            </div>
          </div>
        </div>
      )}
    </Section>
  );
};

export default LocationAndSite;
