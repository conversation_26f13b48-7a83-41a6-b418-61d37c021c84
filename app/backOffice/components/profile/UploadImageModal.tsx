import React, { useEffect, useRef, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import Cropper from 'react-cropper';
import 'cropperjs/dist/cropper.css';
import { FaSave, FaTimes } from 'react-icons/fa';

interface UploadImageModalProps {
  image: File | null;
  onClose: () => void;
  onSubmit: (croppedBlob: Blob) => void;
}

export const UploadImageModal: React.FC<UploadImageModalProps> = ({ image, onClose, onSubmit }) => {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const cropperRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    if (image) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(image);
    } else {
      setPreviewUrl(null);
    }
  }, [image]);

  const resetStates = () => {
    setPreviewUrl(null);
    onClose(); // se resetează și în componenta părinte
  };

  const cropImage = () => {
    const cropper = (cropperRef.current as any)?.cropper;
    if (cropper) {
      const canvas = cropper.getCroppedCanvas({ width: 240, height: 240 });
      canvas.toBlob((blob: Blob | null) => {
        if (blob) {
          onSubmit(blob);
          resetStates(); // Resetăm după submit
        }
      }, image?.type || 'image/jpeg');
    }
  };

  if (!image || !previewUrl) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 !mt-0"
        onClick={resetStates}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-gray-900 rounded-xl p-6 w-full max-w-4xl relative "
          onClick={(e) => e.stopPropagation()}
        >
          <form
            onSubmit={(e) => {
              e.preventDefault();
              cropImage();
            }}
          >
            <Cropper
              src={previewUrl}
              className="cropper-container circle-cropper"
              style={{ height: 400, width: '100%' }}
              initialAspectRatio={1}
              aspectRatio={1}
              viewMode={1}
              guides={false}
              cropBoxResizable={false}
              cropBoxMovable
              dragMode="move"
              ref={cropperRef}
            />
            <div className="flex justify-between items-center mt-6 gap-4">
              <button
                type="button"
                className="px-4 py-2 flex items-center gap-2 bg-red-500 text-white rounded hover:bg-red-600"
                onClick={resetStates}
              >
                <FaTimes />
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 flex items-center gap-2 bg-green-500 text-white rounded hover:bg-green-600"
              >
                <FaSave />
                Save
              </button>
            </div>
          </form>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
