export const languages = [
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Español' },
  { value: 'fr', label: 'Français' },
  { value: 'de', label: 'Deutsch' },
  { value: 'it', label: 'Italiano' },
  { value: 'pt', label: 'Português' },
  { value: 'ru', label: 'Русский' },
  { value: 'ro', label: 'Română' },
  { value: 'zh', label: '中文' },
  { value: 'ja', label: '日本語' },
  { value: 'ko', label: '한국어' },
  { value: 'ar', label: 'العربية' },
  { value: 'az', label: 'Azərbaycan' },
];

// Comprehensive countries list for billing
export const countries = [
  { name: 'Afghanistan', code: 'AF', iso2: 'AF', dialCode: '+93', flag: '🇦🇫' },
  { name: 'Albania', code: 'AL', iso2: 'AL', dialCode: '+355', flag: '🇦🇱' },
  { name: 'Algeria', code: 'DZ', iso2: 'DZ', dialCode: '+213', flag: '🇩🇿' },
  { name: 'Andorra', code: 'AD', iso2: 'AD', dialCode: '+376', flag: '🇦🇩' },
  { name: 'Angola', code: 'AO', iso2: 'AO', dialCode: '+244', flag: '🇦🇴' },
  { name: 'Antigua and Barbuda', code: 'AG', iso2: 'AG', dialCode: '+1-268', flag: '🇦🇬' },
  { name: 'Argentina', code: 'AR', iso2: 'AR', dialCode: '+54', flag: '🇦🇷' },
  { name: 'Armenia', code: 'AM', iso2: 'AM', dialCode: '+374', flag: '🇦🇲' },
  { name: 'Australia', code: 'AU', iso2: 'AU', dialCode: '+61', flag: '🇦🇺' },
  { name: 'Austria', code: 'AT', iso2: 'AT', dialCode: '+43', flag: '🇦🇹' },
  { name: 'Azerbaijan', code: 'AZ', iso2: 'AZ', dialCode: '+994', flag: '🇦🇿' },
  { name: 'Bahamas', code: 'BS', iso2: 'BS', dialCode: '+1-242', flag: '🇧🇸' },
  { name: 'Bahrain', code: 'BH', iso2: 'BH', dialCode: '+973', flag: '🇧🇭' },
  { name: 'Bangladesh', code: 'BD', iso2: 'BD', dialCode: '+880', flag: '🇧🇩' },
  { name: 'Barbados', code: 'BB', iso2: 'BB', dialCode: '+1-246', flag: '🇧🇧' },
  { name: 'Belarus', code: 'BY', iso2: 'BY', dialCode: '+375', flag: '🇧🇾' },
  { name: 'Belgium', code: 'BE', iso2: 'BE', dialCode: '+32', flag: '🇧🇪' },
  { name: 'Belize', code: 'BZ', iso2: 'BZ', dialCode: '+501', flag: '🇧🇿' },
  { name: 'Benin', code: 'BJ', iso2: 'BJ', dialCode: '+229', flag: '🇧🇯' },
  { name: 'Bhutan', code: 'BT', iso2: 'BT', dialCode: '+975', flag: '🇧🇹' },
  { name: 'Bolivia', code: 'BO', iso2: 'BO', dialCode: '+591', flag: '🇧🇴' },
  { name: 'Bosnia and Herzegovina', code: 'BA', iso2: 'BA', dialCode: '+387', flag: '🇧🇦' },
  { name: 'Botswana', code: 'BW', iso2: 'BW', dialCode: '+267', flag: '🇧🇼' },
  { name: 'Brazil', code: 'BR', iso2: 'BR', dialCode: '+55', flag: '🇧🇷' },
  { name: 'Brunei', code: 'BN', iso2: 'BN', dialCode: '+673', flag: '🇧🇳' },
  { name: 'Bulgaria', code: 'BG', iso2: 'BG', dialCode: '+359', flag: '🇧🇬' },
  { name: 'Burkina Faso', code: 'BF', iso2: 'BF', dialCode: '+226', flag: '🇧🇫' },
  { name: 'Burundi', code: 'BI', iso2: 'BI', dialCode: '+257', flag: '🇧🇮' },
  { name: 'Cabo Verde', code: 'CV', iso2: 'CV', dialCode: '+238', flag: '🇨🇻' },
  { name: 'Cambodia', code: 'KH', iso2: 'KH', dialCode: '+855', flag: '🇰🇭' },
  { name: 'Cameroon', code: 'CM', iso2: 'CM', dialCode: '+237', flag: '🇨🇲' },
  { name: 'Canada', code: 'CA', iso2: 'CA', dialCode: '+1', flag: '🇨🇦' },
  { name: 'Central African Republic', code: 'CF', iso2: 'CF', dialCode: '+236', flag: '🇨🇫' },
  { name: 'Chad', code: 'TD', iso2: 'TD', dialCode: '+235', flag: '🇹🇩' },
  { name: 'Chile', code: 'CL', iso2: 'CL', dialCode: '+56', flag: '🇨🇱' },
  { name: 'China', code: 'CN', iso2: 'CN', dialCode: '+86', flag: '🇨🇳' },
  { name: 'Colombia', code: 'CO', iso2: 'CO', dialCode: '+57', flag: '🇨🇴' },
  { name: 'Comoros', code: 'KM', iso2: 'KM', dialCode: '+269', flag: '🇰🇲' },
  { name: 'Congo (Congo-Brazzaville)', code: 'CG', iso2: 'CG', dialCode: '+242', flag: '🇨🇬' },
  { name: 'Côte d\'Ivoire', code: 'CI', iso2: 'CI', dialCode: '+225', flag: '🇨🇮' },
  { name: 'Costa Rica', code: 'CR', iso2: 'CR', dialCode: '+506', flag: '🇨🇷' },
  { name: 'Croatia', code: 'HR', iso2: 'HR', dialCode: '+385', flag: '🇭🇷' },
  { name: 'Cuba', code: 'CU', iso2: 'CU', dialCode: '+53', flag: '🇨🇺' },
  { name: 'Cyprus', code: 'CY', iso2: 'CY', dialCode: '+357', flag: '🇨🇾' },
  { name: 'Czechia', code: 'CZ', iso2: 'CZ', dialCode: '+420', flag: '🇨🇿' },
  { name: 'Democratic Republic of the Congo', code: 'CD', iso2: 'CD', dialCode: '+243', flag: '🇨🇩' },
  { name: 'Denmark', code: 'DK', iso2: 'DK', dialCode: '+45', flag: '🇩🇰' },
  { name: 'Djibouti', code: 'DJ', iso2: 'DJ', dialCode: '+253', flag: '🇩🇯' },
  { name: 'Dominica', code: 'DM', iso2: 'DM', dialCode: '+1-767', flag: '🇩🇲' },
  { name: 'Dominican Republic', code: 'DO', iso2: 'DO', dialCode: '+1-809', flag: '🇩🇴' },
  { name: 'Ecuador', code: 'EC', iso2: 'EC', dialCode: '+593', flag: '🇪🇨' },
  { name: 'Egypt', code: 'EG', iso2: 'EG', dialCode: '+20', flag: '🇪🇬' },
  { name: 'El Salvador', code: 'SV', iso2: 'SV', dialCode: '+503', flag: '🇸🇻' },
  { name: 'Equatorial Guinea', code: 'GQ', iso2: 'GQ', dialCode: '+240', flag: '🇬🇶' },
  { name: 'Eritrea', code: 'ER', iso2: 'ER', dialCode: '+291', flag: '🇪🇷' },
  { name: 'Estonia', code: 'EE', iso2: 'EE', dialCode: '+372', flag: '🇪🇪' },
  { name: 'Eswatini', code: 'SZ', iso2: 'SZ', dialCode: '+268', flag: '🇸🇿' },
  { name: 'Ethiopia', code: 'ET', iso2: 'ET', dialCode: '+251', flag: '🇪🇹' },
  { name: 'Fiji', code: 'FJ', iso2: 'FJ', dialCode: '+679', flag: '🇫🇯' },
  { name: 'Finland', code: 'FI', iso2: 'FI', dialCode: '+358', flag: '🇫🇮' },
  { name: 'France', code: 'FR', iso2: 'FR', dialCode: '+33', flag: '🇫🇷' },
  { name: 'Gabon', code: 'GA', iso2: 'GA', dialCode: '+241', flag: '🇬🇦' },
  { name: 'Gambia', code: 'GM', iso2: 'GM', dialCode: '+220', flag: '🇬🇲' },
  { name: 'Georgia', code: 'GE', iso2: 'GE', dialCode: '+995', flag: '🇬🇪' },
  { name: 'Germany', code: 'DE', iso2: 'DE', dialCode: '+49', flag: '🇩🇪' },
  { name: 'Ghana', code: 'GH', iso2: 'GH', dialCode: '+233', flag: '🇬🇭' },
  { name: 'Greece', code: 'GR', iso2: 'GR', dialCode: '+30', flag: '🇬🇷' },
  { name: 'Grenada', code: 'GD', iso2: 'GD', dialCode: '+1-473', flag: '🇬🇩' },
  { name: 'Guatemala', code: 'GT', iso2: 'GT', dialCode: '+502', flag: '🇬🇹' },
  { name: 'Guinea', code: 'GN', iso2: 'GN', dialCode: '+224', flag: '🇬🇳' },
  { name: 'Guinea-Bissau', code: 'GW', iso2: 'GW', dialCode: '+245', flag: '🇬🇼' },
  { name: 'Guyana', code: 'GY', iso2: 'GY', dialCode: '+592', flag: '🇬🇾' },
  { name: 'Haiti', code: 'HT', iso2: 'HT', dialCode: '+509', flag: '🇭🇹' },
  { name: 'Holy See', code: 'VA', iso2: 'VA', dialCode: '+379', flag: '🇻🇦' },
  { name: 'Honduras', code: 'HN', iso2: 'HN', dialCode: '+504', flag: '🇭🇳' },
  { name: 'Hungary', code: 'HU', iso2: 'HU', dialCode: '+36', flag: '🇭🇺' },
  { name: 'Iceland', code: 'IS', iso2: 'IS', dialCode: '+354', flag: '🇮🇸' },
  { name: 'India', code: 'IN', iso2: 'IN', dialCode: '+91', flag: '🇮🇳' },
  { name: 'Indonesia', code: 'ID', iso2: 'ID', dialCode: '+62', flag: '🇮🇩' },
  { name: 'Iran', code: 'IR', iso2: 'IR', dialCode: '+98', flag: '🇮🇷' },
  { name: 'Iraq', code: 'IQ', iso2: 'IQ', dialCode: '+964', flag: '🇮🇶' },
  { name: 'Ireland', code: 'IE', iso2: 'IE', dialCode: '+353', flag: '🇮🇪' },
  { name: 'Israel', code: 'IL', iso2: 'IL', dialCode: '+972', flag: '🇮🇱' },
  { name: 'Italy', code: 'IT', iso2: 'IT', dialCode: '+39', flag: '🇮🇹' },
  { name: 'Kosovo', code: 'XK', iso2: 'XK', dialCode: '+383', flag: '🇽🇰' },
  { name: 'Jamaica', code: 'JM', iso2: 'JM', dialCode: '+1-876', flag: '🇯🇲' },
  { name: 'Japan', code: 'JP', iso2: 'JP', dialCode: '+81', flag: '🇯🇵' },
  { name: 'Jordan', code: 'JO', iso2: 'JO', dialCode: '+962', flag: '🇯🇴' },
  { name: 'Kazakhstan', code: 'KZ', iso2: 'KZ', dialCode: '+7', flag: '🇰🇿' },
  { name: 'Kenya', code: 'KE', iso2: 'KE', dialCode: '+254', flag: '🇰🇪' },
  { name: 'Kiribati', code: 'KI', iso2: 'KI', dialCode: '+686', flag: '🇰🇮' },
  { name: 'Kuwait', code: 'KW', iso2: 'KW', dialCode: '+965', flag: '🇰🇼' },
  { name: 'Kyrgyzstan', code: 'KG', iso2: 'KG', dialCode: '+996', flag: '🇰🇬' },
  { name: 'Laos', code: 'LA', iso2: 'LA', dialCode: '+856', flag: '🇱🇦' },
  { name: 'Latvia', code: 'LV', iso2: 'LV', dialCode: '+371', flag: '🇱🇻' },
  { name: 'Lebanon', code: 'LB', iso2: 'LB', dialCode: '+961', flag: '🇱🇧' },
  { name: 'Lesotho', code: 'LS', iso2: 'LS', dialCode: '+266', flag: '🇱🇸' },
  { name: 'Liberia', code: 'LR', iso2: 'LR', dialCode: '+231', flag: '🇱🇷' },
  { name: 'Libya', code: 'LY', iso2: 'LY', dialCode: '+218', flag: '🇱🇾' },
  { name: 'Liechtenstein', code: 'LI', iso2: 'LI', dialCode: '+423', flag: '🇱🇮' },
  { name: 'Lithuania', code: 'LT', iso2: 'LT', dialCode: '+370', flag: '🇱🇹' },
  { name: 'Luxembourg', code: 'LU', iso2: 'LU', dialCode: '+352', flag: '🇱🇺' },
  { name: 'Madagascar', code: 'MG', iso2: 'MG', dialCode: '+261', flag: '🇲🇬' },
  { name: 'Malawi', code: 'MW', iso2: 'MW', dialCode: '+265', flag: '🇲🇼' },
  { name: 'Malaysia', code: 'MY', iso2: 'MY', dialCode: '+60', flag: '🇲🇾' },
  { name: 'Maldives', code: 'MV', iso2: 'MV', dialCode: '+960', flag: '🇲🇻' },
  { name: 'Mali', code: 'ML', iso2: 'ML', dialCode: '+223', flag: '🇲🇱' },
  { name: 'Malta', code: 'MT', iso2: 'MT', dialCode: '+356', flag: '🇲🇹' },
  { name: 'Marshall Islands', code: 'MH', iso2: 'MH', dialCode: '+692', flag: '🇲🇭' },
  { name: 'Mauritania', code: 'MR', iso2: 'MR', dialCode: '+222', flag: '🇲🇷' },
  { name: 'Mauritius', code: 'MU', iso2: 'MU', dialCode: '+230', flag: '🇲🇺' },
  { name: 'Mexico', code: 'MX', iso2: 'MX', dialCode: '+52', flag: '🇲🇽' },
  { name: 'Micronesia', code: 'FM', iso2: 'FM', dialCode: '+691', flag: '🇫🇲' },
  { name: 'Moldova', code: 'MD', iso2: 'MD', dialCode: '+373', flag: '🇲🇩' },
  { name: 'Monaco', code: 'MC', iso2: 'MC', dialCode: '+377', flag: '🇲🇨' },
  { name: 'Mongolia', code: 'MN', iso2: 'MN', dialCode: '+976', flag: '🇲🇳' },
  { name: 'Montenegro', code: 'ME', iso2: 'ME', dialCode: '+382', flag: '🇲🇪' },
  { name: 'Morocco', code: 'MA', iso2: 'MA', dialCode: '+212', flag: '🇲🇦' },
  { name: 'Mozambique', code: 'MZ', iso2: 'MZ', dialCode: '+258', flag: '🇲🇿' },
  { name: 'Myanmar', code: 'MM', iso2: 'MM', dialCode: '+95', flag: '🇲🇲' },
  { name: 'Namibia', code: 'NA', iso2: 'NA', dialCode: '+264', flag: '🇳🇦' },
  { name: 'Nauru', code: 'NR', iso2: 'NR', dialCode: '+674', flag: '🇳🇷' },
  { name: 'Nepal', code: 'NP', iso2: 'NP', dialCode: '+977', flag: '🇳🇵' },
  { name: 'Netherlands', code: 'NL', iso2: 'NL', dialCode: '+31', flag: '🇳🇱' },
  { name: 'New Zealand', code: 'NZ', iso2: 'NZ', dialCode: '+64', flag: '🇳🇿' },
  { name: 'Nicaragua', code: 'NI', iso2: 'NI', dialCode: '+505', flag: '🇳🇮' },
  { name: 'Niger', code: 'NE', iso2: 'NE', dialCode: '+227', flag: '🇳🇪' },
  { name: 'Nigeria', code: 'NG', iso2: 'NG', dialCode: '+234', flag: '🇳🇬' },
  { name: 'North Korea', code: 'KP', iso2: 'KP', dialCode: '+850', flag: '🇰🇵' },
  { name: 'North Macedonia', code: 'MK', iso2: 'MK', dialCode: '+389', flag: '🇲🇰' },
  { name: 'Norway', code: 'NO', iso2: 'NO', dialCode: '+47', flag: '🇳🇴' },
  { name: 'Oman', code: 'OM', iso2: 'OM', dialCode: '+968', flag: '🇴🇲' },
  { name: 'Pakistan', code: 'PK', iso2: 'PK', dialCode: '+92', flag: '🇵🇰' },
  { name: 'Palau', code: 'PW', iso2: 'PW', dialCode: '+680', flag: '🇵🇼' },
  { name: 'Palestine', code: 'PS', iso2: 'PS', dialCode: '+970', flag: '🇵🇸' },
  { name: 'Panama', code: 'PA', iso2: 'PA', dialCode: '+507', flag: '🇵🇦' },
  { name: 'Papua New Guinea', code: 'PG', iso2: 'PG', dialCode: '+675', flag: '🇵🇬' },
  { name: 'Paraguay', code: 'PY', iso2: 'PY', dialCode: '+595', flag: '🇵🇾' },
  { name: 'Peru', code: 'PE', iso2: 'PE', dialCode: '+51', flag: '🇵🇪' },
  { name: 'Philippines', code: 'PH', iso2: 'PH', dialCode: '+63', flag: '🇵🇭' },
  { name: 'Poland', code: 'PL', iso2: 'PL', dialCode: '+48', flag: '🇵🇱' },
  { name: 'Portugal', code: 'PT', iso2: 'PT', dialCode: '+351', flag: '🇵🇹' },
  { name: 'Qatar', code: 'QA', iso2: 'QA', dialCode: '+974', flag: '🇶🇦' },
  { name: 'Romania', code: 'RO', iso2: 'RO', dialCode: '+40', flag: '🇷🇴' },
  { name: 'Russia', code: 'RU', iso2: 'RU', dialCode: '+7', flag: '🇷🇺' },
  { name: 'Rwanda', code: 'RW', iso2: 'RW', dialCode: '+250', flag: '🇷🇼' },
  { name: 'Saint Kitts and Nevis', code: 'KN', iso2: 'KN', dialCode: '+1-869', flag: '🇰🇳' },
  { name: 'Saint Lucia', code: 'LC', iso2: 'LC', dialCode: '+1-758', flag: '🇱🇨' },
  { name: 'Saint Vincent and the Grenadines', code: 'VC', iso2: 'VC', dialCode: '+1-784', flag: '🇻🇨' },
  { name: 'Samoa', code: 'WS', iso2: 'WS', dialCode: '+685', flag: '🇼🇸' },
  { name: 'San Marino', code: 'SM', iso2: 'SM', dialCode: '+378', flag: '🇸🇲' },
  { name: 'Sao Tome and Principe', code: 'ST', iso2: 'ST', dialCode: '+239', flag: '🇸🇹' },
  { name: 'Saudi Arabia', code: 'SA', iso2: 'SA', dialCode: '+966', flag: '🇸🇦' },
  { name: 'Senegal', code: 'SN', iso2: 'SN', dialCode: '+221', flag: '🇸🇳' },
  { name: 'Serbia', code: 'RS', iso2: 'RS', dialCode: '+381', flag: '🇷🇸' },
  { name: 'Seychelles', code: 'SC', iso2: 'SC', dialCode: '+248', flag: '🇸🇨' },
  { name: 'Sierra Leone', code: 'SL', iso2: 'SL', dialCode: '+232', flag: '🇸🇱' },
  { name: 'Singapore', code: 'SG', iso2: 'SG', dialCode: '+65', flag: '🇸🇬' },
  { name: 'Slovakia', code: 'SK', iso2: 'SK', dialCode: '+421', flag: '🇸🇰' },
  { name: 'Slovenia', code: 'SI', iso2: 'SI', dialCode: '+386', flag: '🇸🇮' },
  { name: 'Solomon Islands', code: 'SB', iso2: 'SB', dialCode: '+677', flag: '🇸🇧' },
  { name: 'Somalia', code: 'SO', iso2: 'SO', dialCode: '+252', flag: '🇸🇴' },
  { name: 'South Africa', code: 'ZA', iso2: 'ZA', dialCode: '+27', flag: '🇿🇦' },
  { name: 'South Korea', code: 'KR', iso2: 'KR', dialCode: '+82', flag: '🇰🇷' },
  { name: 'South Sudan', code: 'SS', iso2: 'SS', dialCode: '+211', flag: '🇸🇸' },
  { name: 'Spain', code: 'ES', iso2: 'ES', dialCode: '+34', flag: '🇪🇸' },
  { name: 'Sri Lanka', code: 'LK', iso2: 'LK', dialCode: '+94', flag: '🇱🇰' },
  { name: 'Sudan', code: 'SD', iso2: 'SD', dialCode: '+249', flag: '🇸🇩' },
  { name: 'Suriname', code: 'SR', iso2: 'SR', dialCode: '+597', flag: '🇸🇷' },
  { name: 'Sweden', code: 'SE', iso2: 'SE', dialCode: '+46', flag: '🇸🇪' },
  { name: 'Switzerland', code: 'CH', iso2: 'CH', dialCode: '+41', flag: '🇨🇭' },
  { name: 'Syria', code: 'SY', iso2: 'SY', dialCode: '+963', flag: '🇸🇾' },
  { name: 'Taiwan', code: 'TW', iso2: 'TW', dialCode: '+886', flag: '🇹🇼' },
  { name: 'Tajikistan', code: 'TJ', iso2: 'TJ', dialCode: '+992', flag: '🇹🇯' },
  { name: 'Tanzania', code: 'TZ', iso2: 'TZ', dialCode: '+255', flag: '🇹🇿' },
  { name: 'Thailand', code: 'TH', iso2: 'TH', dialCode: '+66', flag: '🇹🇭' },
  { name: 'Timor-Leste', code: 'TL', iso2: 'TL', dialCode: '+670', flag: '🇹🇱' },
  { name: 'Togo', code: 'TG', iso2: 'TG', dialCode: '+228', flag: '🇹🇬' },
  { name: 'Tonga', code: 'TO', iso2: 'TO', dialCode: '+676', flag: '🇹🇴' },
  { name: 'Trinidad and Tobago', code: 'TT', iso2: 'TT', dialCode: '+1-868', flag: '🇹🇹' },
  { name: 'Tunisia', code: 'TN', iso2: 'TN', dialCode: '+216', flag: '🇹🇳' },
  { name: 'Turkey', code: 'TR', iso2: 'TR', dialCode: '+90', flag: '🇹🇷' },
  { name: 'Turkmenistan', code: 'TM', iso2: 'TM', dialCode: '+993', flag: '🇹🇲' },
  { name: 'Tuvalu', code: 'TV', iso2: 'TV', dialCode: '+688', flag: '🇹🇻' },
  { name: 'Uganda', code: 'UG', iso2: 'UG', dialCode: '+256', flag: '🇺🇬' },
  { name: 'Ukraine', code: 'UA', iso2: 'UA', dialCode: '+380', flag: '🇺🇦' },
  { name: 'United Arab Emirates', code: 'AE', iso2: 'AE', dialCode: '+971', flag: '🇦🇪' },
  { name: 'United Kingdom', code: 'GB', iso2: 'GB', dialCode: '+44', flag: '🇬🇧' },
  { name: 'United States', code: 'US', iso2: 'US', dialCode: '+1', flag: '🇺🇸' },
  { name: 'Uruguay', code: 'UY', iso2: 'UY', dialCode: '+598', flag: '🇺🇾' },
  { name: 'Uzbekistan', code: 'UZ', iso2: 'UZ', dialCode: '+998', flag: '🇺🇿' },
  { name: 'Vanuatu', code: 'VU', iso2: 'VU', dialCode: '+678', flag: '🇻🇺' },
  { name: 'Venezuela', code: 'VE', iso2: 'VE', dialCode: '+58', flag: '🇻🇪' },
  { name: 'Vietnam', code: 'VN', iso2: 'VN', dialCode: '+84', flag: '🇻🇳' },
  { name: 'Yemen', code: 'YE', iso2: 'YE', dialCode: '+967', flag: '🇾🇪' },
  { name: 'Zambia', code: 'ZM', iso2: 'ZM', dialCode: '+260', flag: '🇿🇲' },
  { name: 'Zimbabwe', code: 'ZW', iso2: 'ZW', dialCode: '+263', flag: '🇿🇼' },
];
