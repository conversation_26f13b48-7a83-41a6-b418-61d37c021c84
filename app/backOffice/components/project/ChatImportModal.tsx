import { useState, useRef } from 'react';
import CloseButton from '../../../components/common/closeButton';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import { Message } from 'ai';

type ChatImportModalProps = {
  closeModal: () => void;
  importChat: (description: string, messages: Message[]) => Promise<void>;
};

type ChatData = {
  messages?: Message[];
  description?: string;
};

export default function ChatImportModal({ closeModal, importChat }: ChatImportModalProps) {
  const { t } = useTranslation('translation');
  const [chatName, setChatName] = useState('');
  const [fileInputKey, setFileInputKey] = useState(0);
  const [showNameError, setShowNameError] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!chatName.trim()) {
      toast.error(t('pleaseEnterName', 'Please enter a chat name before importing'));
      setFileInputKey((prev) => prev + 1); // reset file input to allow re-select
      return;
    }

    try {
      const reader = new FileReader();
      reader.onload = async (event) => {
        try {
          const content = event.target?.result as string;
          const data = JSON.parse(content) as ChatData;

          if (!Array.isArray(data.messages)) {
            toast.error(t('invalidChatFile', 'Invalid chat file format'));
            return;
          }

          await importChat(chatName, data.messages, true);
          toast.success(t('chatImported', 'Chat imported!'));
          closeModal();
        } catch (err) {
          toast.error(t('parseError', 'Failed to parse file'));
        }
      };
      reader.readAsText(file);
    } catch (err) {
      toast.error(t('readError', 'Could not read file'));
    } finally {
      setFileInputKey((prev) => prev + 1); // reset file input
    }
  };

  const handleButtonClick = () => {
    if (!chatName.trim()) {
      setShowNameError(true);
    } else {
      setShowNameError(false);
      fileInputRef.current?.click();
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center backdrop-blur-[5px] z-50">
      <div className="w-full lg:w-[488px] h-auto bg-[#0A1730] border border-[#2E2E2E] rounded-lg pt-6 px-4 py-3 md:py-6">
        <div className="flex justify-between items-center -mt-1">
          <div className="flex flex-col gap-1">
            <span className="font-inter text-xl text-white font-medium leading-relaxed">
              {t('importChat', 'Import Chat')}
            </span>
          </div>
          <CloseButton closeModal={closeModal} />
        </div>

        <div className="flex flex-col items-center mt-4 w-full gap-4">
          <input
            type="text"
            placeholder={t('giveTitle', 'Give a title')}
            value={chatName}
            onChange={(e) => {
              setChatName(e.target.value);
              if (e.target.value.trim()) setShowNameError(false);
            }}
            className={`w-full h-[48px] bg-transparent font-inter text-white placeholder-white border ${
              showNameError ? 'border-red-500' : 'border-[#2E2E2E]'
            } rounded-lg focus:outline-none px-4 text-base`}
          />
          {showNameError && (
            <span className="text-red-500 text-sm -mt-2 self-start">
              {t('pleaseEnterName', 'Please complete name')}
            </span>
          )}

          <button
            onClick={handleButtonClick}
            className={`w-full h-[48px] rounded-lg text-base px-6 py-2 flex items-center justify-center gap-2 transition-all bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 cursor-pointer`}
          >
            {t('selectFile', 'Select Chat File')}
          </button>

          <input
            key={fileInputKey}
            ref={fileInputRef}
            id="chat-import-file"
            type="file"
            accept=".json"
            className="hidden"
            onChange={handleFileChange}
          />
        </div>
      </div>
    </div>
  );
}
