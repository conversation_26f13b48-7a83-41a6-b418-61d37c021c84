import { useState } from 'react';
import CloseButton from '../../../components/common/closeButton';
import { ProjectImportProps } from '~/backOffice/types/project';
import { ImportFolderButtonClient } from '~/ai/components/ImportFolderButton.client';
import { setProjectImportName } from '~/ai/lib/stores/chat';
import { useTranslation } from 'react-i18next';

export default function ProjectImport({ closeModal }: ProjectImportProps) {
  const [projectNameRef, setProjectNameRef] = useState('');
  const [showNameError, setShowNameError] = useState(false);
  const { t } = useTranslation('translation');

  const handleNameChange = (value: string) => {
    setProjectImportName(value);
    setProjectNameRef(value);
    if (value.trim()) setShowNameError(false);
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center backdrop-blur-[5px] z-50">
      <div className="w-full lg:w-[488px] h-auto bg-[#0A1730] border border-[#2E2E2E] rounded-lg pt-6 px-4 py-3 md:py-6">
        <div className="flex justify-between items-center -mt-1">
          <div className="flex flex-col gap-1">
            <span className="font-inter text-xl text-white font-medium leading-relaxed">
              {t('uploadProject', 'Upload Project')}
            </span>
          </div>
          <CloseButton closeModal={closeModal} />
        </div>

        <div className="flex flex-col items-center mt-4 gap-2 w-full">
          <input
            type="text"
            name="title"
            id="title"
            placeholder={t('giveTitle', 'Give a title')}
            value={projectNameRef}
            onChange={(e) => handleNameChange(e.target.value)}
            className={`w-full h-[48px] bg-transparent font-inter text-white placeholder-white border rounded-lg focus:outline-none px-4 text-base ${
              showNameError ? 'border-red-500' : 'border-[#2E2E2E]'
            }`}
          />
          {showNameError && (
            <span className="w-full text-left text-red-500 text-sm">
              {t('pleaseCompleteName', 'Please complete name')}
            </span>
          )}
          {/* not passing importChat here because importing "useChatHistory" in this file will break the app - Error when evaluating SSR module path-browserify */}
          {/* so the component renders here, but once clicked it will actually forward to the one from the SideMenu that has all the functionality */}
          <ImportFolderButtonClient
            className="w-full h-[48px] rounded-lg !text-base px-6 py-2 bg-gradient-to-r from-green-500 to-green-600 text-sm flex items-center justify-center gap-2 hover:from-green-600 hover:to-green-700 transition-all"
            showNameError={showNameError}
            setShowNameError={setShowNameError}
            projectName={projectNameRef}
          />
        </div>
      </div>
    </div>
  );
}
