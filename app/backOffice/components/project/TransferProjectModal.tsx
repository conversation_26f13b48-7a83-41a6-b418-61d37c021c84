import React, { useState, useEffect, FormEvent, ChangeEvent } from 'react';
import { motion } from 'framer-motion';
import {
  XMarkIcon,
  PaperAirplaneIcon,
  CodeBracketIcon,
  PlusIcon,
  CheckIcon,
} from '@heroicons/react/24/outline';

interface TransferModalProps {
  isOpen: boolean | string;
  onClose: () => void;
  isLoading: boolean;
  onTransfer: (recipient: string) => void;
  errorMessage?: string | null;
}

const TransferModal: React.FC<TransferModalProps> = ({
                                                          isOpen,
                                                          onClose,
                                                          isLoading,
                                                          onTransfer,
                                                          errorMessage,
                                                        }) => {
  const [recipient, setRecipient] = useState('');
  const [error, setError] = useState('');
  const [serverError, setServerError] = useState<string | null>(errorMessage ?? null);

  useEffect(() => {
    setServerError(errorMessage ?? null);
  }, [errorMessage]);

  if (!isOpen) return null;

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (!recipient.trim()) {
      setError('Please enter a username or email');
      return;
    }

    const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(recipient);
    if (!isEmail && recipient.length < 3) {
      setError('Please enter a valid username (min 3 characters) or email address');
      return;
    }

    setError('');
    onTransfer(recipient);
  };


  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-[1000]">
      <motion.div
        initial={{ scale: 0.95, opacity: 0, y: 20 }}
        animate={{ scale: 1, opacity: 1, y: 0 }}
        exit={{ scale: 0.95, opacity: 0, y: 20 }}
        transition={{ type: 'spring', stiffness: 300, damping: 20 }}
        className="w-full max-w-lg bg-[#0F1526] rounded-xl border border-white/10 overflow-hidden shadow-xl"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/5">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-[#4ADE80]/10 flex items-center justify-center">
              <PaperAirplaneIcon className="w-5 h-5 text-[#4ADE80]" />
            </div>
            <div>
              <h2 className="text-xl font-medium text-white">Share Project Copy</h2>
              <p className="text-sm text-white/50 font-light">
                Transfer to another BIELA user
              </p>
            </div>
          </div>
          <button onClick={onClose} className="p-2 rounded-full hover:bg-white/5 transition-colors">
            <XMarkIcon className="w-5 h-5 text-white/70" />
          </button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit}>
          <div className={'p-6  space-y-6'}>
            <p className="text-white/80 leading-[1.625] font-light">
              Enter the username or email of the person you want to transfer a copy of this project to.
            </p>

            {/* Info */}
            <div className="space-y-3">
              <div className="flex items-start gap-3 p-4 bg-[#1A2035] rounded-lg border border-[#4ADE80]/20">
                <div className="w-6 h-6 rounded-full bg-[#4ADE80]/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <CodeBracketIcon className="w-4 h-4 text-[#4ADE80]" />
                </div>
                <div>
                  <p className="text-white/90 text-sm font-medium">Original Access Maintained</p>
                  <p className="text-white/60 text-sm font-light mt-1">
                    You will still have access to the original project
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3 p-4 bg-[#1A2035] rounded-lg border border-[#22D3EE]/20">
                <div className="w-6 h-6 rounded-full bg-[#22D3EE]/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <PlusIcon className="w-4 h-4 text-[#22D3EE]" />
                </div>
                <div>
                  <p className="text-white/90 text-sm font-medium">Independent Copy</p>
                  <p className="text-white/60 text-sm font-light mt-1">
                    New changes will not affect the other user's project
                  </p>
                </div>
              </div>
            </div>

            {/* Input */}
            <div className="space-y-3">
              <label className="block text-white/80 text-sm font-medium">
                Username or Email
              </label>
              <input
                type="text"
                value={recipient}
                onChange={(e: ChangeEvent<HTMLInputElement>) => {
                  setRecipient(e.target.value);
                  if (error) setError('');
                  if (serverError) setServerError(null);
                }}
                placeholder="<NAME_EMAIL>"
                className={`w-full bg-[#1A2035] border rounded-lg px-4 py-3 text-white placeholder-white/30 focus:outline-none transition-colors font-light ${
                  error || serverError
                    ? 'border-red-500 focus:border-red-500'
                    : 'border-white/10 focus:border-[#4ADE80]/50'
                }`}
                disabled={isLoading}
              />
              {error && <p className="text-red-500 text-sm">{error}</p>}
              {serverError && <p className="text-red-400 text-sm">{serverError}</p>}
            </div>
          </div>
          {/* Actions */}
          <div className={'p-6 border-t border-white/5 space-y-4'}>
            <div className="flex items-center justify-end gap-3">
              <button
                type="button"
                onClick={onClose}
                disabled={isLoading}
                className="px-6 py-2.5 bg-white/5 text-white/80 rounded-lg hover:bg-white/10 transition-colors font-light disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={!recipient.trim() || isLoading}
                className={`px-6 py-2.5 rounded-lg transition-all font-medium flex items-center gap-2 ${
                  isLoading
                    ? 'bg-[#4ADE80]/20 text-[#4ADE80] cursor-not-allowed'
                    : recipient.trim()
                      ? 'bg-[#4ADE80] text-black hover:bg-[#4ADE80]/90'
                      : 'bg-white/5 text-white/40 cursor-not-allowed'
                }`}
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-[#4ADE80] border-t-transparent rounded-full animate-spin"></div>
                    Transferring...
                  </>
                ) : (
                  <>
                    <PaperAirplaneIcon className="w-4 h-4" />
                    Transfer
                  </>
                )}
              </button>
            </div>

            {/* Security Note */}
            <div className="pt-4 border-t border-white/5">
              <div className="flex items-start gap-3">
                <div className="w-5 h-5 rounded-full bg-[#A78BFA]/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <CheckIcon className="w-3 h-3 text-[#A78BFA]" />
                </div>
                <div>
                  <span className="text-[#A78BFA] text-sm font-medium">Security note:</span>
                  <span className="text-white/60 text-sm font-light ml-1">
                  The recipient will receive full access to a copy of this project and all its associated resources.
                </span>
                </div>
              </div>
            </div>
          </div>

        </form>
      </motion.div>
    </div>
  );
};

export default TransferModal;
