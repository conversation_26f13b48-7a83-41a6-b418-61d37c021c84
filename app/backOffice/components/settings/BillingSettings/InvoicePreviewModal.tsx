import React from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { createPortal } from 'react-dom';
import { PDFViewer } from '@react-pdf/renderer';
import { FaTimes } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

function InvoicePreviewModal({ isOpen, onClose, invoiceData }) {
  const { t } = useTranslation('translation');
  if (!isOpen) return null;

  return createPortal(
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-4 z-50 overflow-hidden rounded-xl bg-gray-900/95 backdrop-blur-sm border border-gray-800 shadow-xl"
          >
            {/* Modal Header */}
            <div className="text-white absolute top-0 left-0 right-0 z-10 flex items-center justify-between p-4 bg-gradient-to-b from-gray-900/95 via-gray-900/95 to-transparent">
              <h3 className="text-xl font-light">{t('invoicePreview', 'Your invoice')}</h3>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-white transition-colors bg-transparent hover:bg-gray-800/50 rounded-lg"
              >
                <FaTimes />
              </button>
            </div>

            {/* PDF Viewer */}
            <div className="absolute inset-0 pt-16 pb-4 px-4">
              <PDFViewer
                style={{
                  width: '100%',
                  height: '100%',
                  backgroundColor: 'transparent',
                  border: 'none',
                }}
              >
                {invoiceData}
              </PDFViewer>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>,
    document.body
  );
}

export default InvoicePreviewModal;
