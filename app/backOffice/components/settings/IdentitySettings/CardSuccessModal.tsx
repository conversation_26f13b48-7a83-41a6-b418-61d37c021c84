import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircle2, Code2, <PERSON><PERSON><PERSON>, X } from 'lucide-react';

function SuccessModal({ onClose }: { onClose: () => void }) {
  const sparkleVariants = {
    initial: { scale: 0, opacity: 0 },
    animate: {
      scale: [0, 1.2, 1],
      opacity: [0, 1, 0.8],
      transition: {
        duration: 2,
        repeat: Infinity,
        repeatType: 'reverse',
      },
    },
  };

  const checkmarkVariants = {
    initial: { scale: 0, rotate: -180 },
    animate: {
      scale: 1,
      rotate: 0,
      transition: {
        type: 'spring',
        stiffness: 200,
        damping: 15,
        duration: 0.8,
      },
    },
  };

  const codeIconVariants = {
    initial: { opacity: 0, y: 20 },
    animate: {
      opacity: [0, 1, 0.5, 1],
      y: [20, -5, 0],
      transition: {
        duration: 2,
        repeat: Infinity,
        repeatType: 'reverse',
      },
    },
  };

  const glowVariants = {
    initial: { opacity: 0.3 },
    animate: {
      opacity: [0.3, 0.6, 0.3],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  };

  const buttonVariants = {
    initial: {
      backgroundPosition: '0% 0%',
    },
    hover: {
      scale: 1.02,
      backgroundPosition: '100% 100%',
      transition: {
        duration: 0.3,
        ease: 'easeOut',
      },
    },
    tap: {
      scale: 0.98,
    },
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-white/[0.02] backdrop-blur-xl flex items-center justify-center p-4 z-[999]"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0, y: 20 }}
        animate={{ scale: 1, opacity: 1, y: 0 }}
        exit={{ scale: 0.9, opacity: 0, y: 20 }}
        className="w-full max-w-lg rounded-3xl border border-emerald-500/20 bg-gradient-to-br from-[#1A1F2E] to-[#141821] relative overflow-hidden"
      >
        <div className="absolute inset-0">
          <motion.div
            variants={glowVariants}
            initial="initial"
            animate="animate"
            className="absolute top-0 right-0 w-[600px] h-[600px] bg-gradient-to-br from-emerald-500/20 via-cyan-500/10 to-purple-500/20 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2"
          />
          <motion.div
            variants={glowVariants}
            initial="initial"
            animate="animate"
            className="absolute bottom-0 left-0 w-[600px] h-[600px] bg-gradient-to-tr from-emerald-500/20 via-cyan-500/10 to-purple-500/20 rounded-full blur-3xl translate-y-1/2 -translate-x-1/2"
          />
        </div>

        <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHBhdHRlcm4gaWQ9ImdyaWQiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHBhdGggZD0iTSAwIDAgTCAyMCAwIE0gMCAwIEwgMCAyMCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLW9wYWNpdHk9IjAuMDUiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JpZCkiLz48L3N2Zz4=')] opacity-20" />

        <motion.div
          whileHover={{ scale: 1.1, rotate: 90 }}
          whileTap={{ scale: 0.9 }}
          onClick={onClose}
          className="absolute right-4 top-4 cursor-pointer z-20 bg-white/5 p-2 rounded-full text-emerald-400 hover:text-cyan-400 hover:bg-white/10 transition-colors duration-200"
        >
          <X size={20} />
        </motion.div>

        <div className="relative z-10 p-8">
          <div className="flex items-center gap-4 mb-8">
            <motion.div
              variants={checkmarkVariants}
              initial="initial"
              animate="animate"
              className="p-3 bg-gradient-to-br from-emerald-500/20 to-emerald-500/5 rounded-xl border border-emerald-500/20"
            >
              <CheckCircle2 className="text-emerald-400" size={28} />
            </motion.div>
            <div>
              <motion.h3
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="text-2xl font-light text-white flex items-center gap-2"
              >
                Verification Complete!
                <motion.span variants={sparkleVariants as any} initial="initial" animate="animate">
                  <Sparkles className="text-[#22c55e]" size={20} />
                </motion.span>
              </motion.h3>
              <motion.p
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="text-gray-400 text-md font-light mt-1"
              >
                Your card has been successfully verified
              </motion.p>
            </div>
          </div>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-xl p-6 mb-8 border border-white/10 relative overflow-hidden group"
          >
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-emerald-500/20 via-cyan-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              animate={{
                backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
              }}
              transition={{
                duration: 5,
                repeat: Infinity,
                ease: 'linear',
              }}
            />
            <p className="text-white/80 font-light leading-relaxed relative z-10">
              Great news! Your card verification is complete. You're all set to start building amazing things. Let's
              dive into coding and bring your ideas to life!
            </p>
          </motion.div>

          <motion.div
            variants={codeIconVariants as any}
            initial="initial"
            animate="animate"
            className="flex justify-center mb-8"
          >
            <Code2 className="text-emerald-400 w-12 h-12" />
          </motion.div>

          <motion.button
            onClick={onClose}
            variants={buttonVariants}
            initial="initial"
            whileHover="hover"
            whileTap="tap"
            className="w-full px-6 py-4 bg-gradient-to-r from-emerald-400 via-cyan-400 to-emerald-400 bg-[length:200%_200%] text-black font-medium rounded-xl flex items-center justify-center gap-2 transition-shadow duration-300 hover:shadow-lg hover:shadow-emerald-500/20"
          >
            <span className="font-semibold tracking-wide">Start Coding</span>
          </motion.button>
        </div>
      </motion.div>
    </motion.div>
  );
}

export default SuccessModal;
