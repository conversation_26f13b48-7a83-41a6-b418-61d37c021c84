.glass-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 24px;
  font-weight: 300;
  color: white;
  background: linear-gradient(to right, #22c55e, #16a34a);
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s;
  text-decoration: none;
  cursor: pointer;
}

.glass-button:hover {
  transform: scale(1.05);
}

.glass-button:active {
  transform: scale(0.95);
}

.glass-button .shimmer {
  position: absolute;
  top: 0;
  left: -150%;
  height: 100%;
  width: 150%;
  background: linear-gradient(
    120deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transform: skewX(-20deg);
  animation: shimmer 2s infinite;
  pointer-events: none;
}

@keyframes shimmer {
  0% {
    left: -150%;
  }
  100% {
    left: 150%;
  }
}

.glass-button svg {
  margin-right: 8px;
}
