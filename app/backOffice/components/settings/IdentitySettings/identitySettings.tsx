import React, { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import {
  FaCalendarAlt,
  FaCcAmex,
  FaCcDinersClub,
  FaCcDiscover,
  FaCcJcb,
  FaCcMastercard,
  FaCcVisa,
  FaCheckCircle,
  FaCreditCard,
  FaExternalLinkAlt,
  FaIdCard,
  FaLock,
  FaShieldAlt,
  FaStripe
} from 'react-icons/fa';
import { CustomerData } from '~/routes/settings.identity';
import { backendApiFetch } from '~/ai/lib/backend-api';
import { useTranslation } from 'react-i18next';
import LoadingIdentity from './LoaderIdentity';
import './button.css';

function IdentitySettings({ customerData }: { customerData: CustomerData | null }) {
  const [isLoading, setIsLoading] = useState(false);
  const [shouldStartExit, setShouldStartExit] = useState(false);
  const { t } = useTranslation();
  const verifyIdentitySession = async (successUrl: string, cancelUrl: string) => {
    setIsLoading(true);
    try {
      const response = await backendApiFetch(`/payments/stripe/verify-identity`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          successUrl,
          cancelUrl,
        }),
      });

      const data = (await response.json()) as { sessionUrl: string };

      if (data.sessionUrl) {
        setShouldStartExit(true);
        setTimeout(() => {
          window.location.href = data.sessionUrl;
        }, 1000);
      } else {
        console.error('Error: No checkout URL returned');
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      setIsLoading(false);
    }
  };

  const handleLoadingComplete = () => {
    setIsLoading(false);
    setShouldStartExit(false);
  };

  const loadingContainerStyle = {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    background: 'linear-gradient(135deg, rgba(123, 31, 162, 0.15), rgba(25, 118, 210, 0.15))',
    backdropFilter: 'blur(4px)',
    zIndex: 50,
    borderRadius: '0.75rem',
    height: '100%',
  } as const;

  const renderUnverifiedState = () => (
    <div className="bg-gradient-to-r from-[#1f293780] to-[#11182780] backdrop-blur-sm rounded-xl p-6 relative overflow-hidden">
      <AnimatePresence>
        {isLoading && (
          <div style={loadingContainerStyle}>
            <div>
              <LoadingIdentity
                onLoadingComplete={handleLoadingComplete}
                shouldStartExit={shouldStartExit}
                message={t('settings.identity.connectingToStripe', 'Connecting to Stripe...')}
              />
            </div>
          </div>
        )}
      </AnimatePresence>

      <div className="relative z-10">
        <div className="md:flex items-center gap-4 mb-6">
          <div className="flex justify-center">
            <div className="p-3 bg-black/20 rounded-xl">
              <FaIdCard className="text-2xl text-blue-400" />
            </div>
          </div>
          <div className="text-center md:text-left">
            <h3 className="text-[20px] font-light">{t('settings.identity.unverified.title', 'Identity Verification')}</h3>
            <p className="text-gray-400 text-[16px] font-light">
              {t('settings.identity.unverified.description', 'Verify your identity to use Biela.dev')}
            </p>
          </div>
        </div>

        <div className="bg-black/20 rounded-xl p-6 mb-6">
          <div className="flex flex-col lg:flex-row items-start gap-4">
            <div className="md:flex items-center gap-4">
              <div className="md:flex items-center md:block gap-4">
                <div className="flex justify-center">
                  <div className="p-3 bg-black/20 rounded-xl mt-1 shrink-0">
                    <FaLock className="text-xl text-yellow-400" />
                  </div>
                </div>
                <h4 className="block text-center md:hidden text-lg font-light">
                  {t('settings.identity.unverified.subtitle', 'Secure Verification Process')}
                </h4>
              </div>
              <ul className="space-y-3 text-gray-300 font-light text-[18px] mt-6 md:mt-0">
                <li className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-blue-400 mt-2.5" style={{ minWidth: '6px' }}></div>
                  <span>
                    {t(
                      'settings.identity.unverified.processServers',
                      "All card information is stored securely on Stripe's servers, not on Biela's servers",
                    )}
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-blue-400 mt-2.5" style={{ minWidth: '6px' }}></div>
                  <span>
                    {t(
                      'settings.identity.unverified.processCharge',
                      'Your card will not be charged without your explicit consent for a subscription',
                    )}
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-blue-400 mt-2.5" style={{ minWidth: '6px' }}></div>
                  <span>
                    {t(
                      'settings.identity.unverified.processBenefits',
                      'Biela.dev is completely free for verified accounts until May 15, 2025',
                    )}
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-blue-500/20 to-blue-600/20 rounded-xl p-6">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-4">
            <div className="md:flex items-center gap-4 w-full lg:w-auto">
              <div className="flex justify-center">
                <div className="p-3 bg-black/20 mb-4 md:mb-0 rounded-xl">
                  <FaStripe className="text-2xl text-white" />
                </div>
              </div>
              <div>
                <div className={'md:flex items-center gap-3'}>
                  <h4 className="text-[20px] font-light text-center md:text-left">
                    {t('settings.identity.unverified.verifyStripe', 'Verify with Credit or Debit Card')}
                  </h4>
                  <div className="grid grid-cols-3 sm:grid-cols-6 gap-4 my-4 md:my-0 w-max mx-auto">
                    <FaCcVisa className="text-2xl" />
                    <FaCcMastercard className="text-2xl " />
                    <FaCcAmex className="text-2xl" />
                    <FaCcDiscover className={'text-2xl'} />
                    <FaCcDinersClub className={'text-2xl'} />
                    <FaCcJcb className={'text-2xl'} />
                  </div>
                </div>
                <p className="text-gray-300 text-[16px] font-light text-center md:text-left">
                  {t(
                    'settings.identity.unverified.verifyStripeDescription',
                    'Connect your payment method for verification',
                  )}
                </p>
              </div>
            </div>
            <a
              className="glass-button"
              onClick={() =>
                verifyIdentitySession(`${window.location.origin}?verified`, `${window.location.origin}/settings`)
              }
            >
              <div className="shimmer"></div>
              <div className={'flex gap-2 items-center'}>
                {t('settings.identity.unverified.verifyNow', 'Verify Now')}
                <FaExternalLinkAlt className="text-sm" />
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  );

  const renderVerifiedState = () => (
    <div className="bg-gradient-to-r from-[#1f293780] to-[#11182780] backdrop-blur-sm rounded-xl p-6 relative overflow-hidden">
      <AnimatePresence>
        {isLoading && (
          <div style={loadingContainerStyle}>
            <div>
              <LoadingIdentity
                onLoadingComplete={handleLoadingComplete}
                shouldStartExit={shouldStartExit}
                message={t('settings.identity.connectingToStripe', 'Connecting to Stripe...')}
              />
            </div>
          </div>
        )}
      </AnimatePresence>
      <div className="absolute top-0 right-0 w-[600px] h-[600px] bg-green-500/5 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2" />
      <div className="absolute bottom-0 left-0 w-[600px] h-[600px] bg-green-600/5 rounded-full blur-3xl translate-y-1/2 -translate-x-1/2" />
      <div className="relative z-10">
        <div className="flex items-center gap-4 mb-6">
          <div className="p-3 bg-green-500/20 rounded-xl">
            <FaCheckCircle className="text-2xl text-green-400" />
          </div>
          <div>
            <h3 className="text-xl font-light">{t('settings.identity.verified.title', 'Identity Verified')}</h3>
            <p className="text-gray-400 text-[16px] font-light">
              {t('settings.identity.verified.description', 'Your identity has been successfully verified')}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="bg-black/20 rounded-xl p-6">
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 bg-black/20 rounded-xl">
                <FaCreditCard className="text-xl text-green-400" />
              </div>
              <div>
                <h4 className="text-lg font-light">
                  {t('settings.identity.verified.paymentMethod', 'Payment Method')}
                </h4>
                <p className="text-gray-400 text-[16px] font-light">
                  {t('settings.identity.verified.cardEnding', 'Card ending in')} {customerData?.card}
                </p>
              </div>
            </div>
            <a
              className="glass-button"
              onClick={() =>
                verifyIdentitySession(`${window.location.origin}/settings`, `${window.location.origin}/settings`)
              }
            >
              <div className="shimmer"></div>
              {t('settings.identity.verified.updatePayment', 'Update Payment Method')}
            </a>
          </div>

          <div className="bg-black/20 rounded-xl p-6">
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 bg-black/20 rounded-xl">
                <FaCalendarAlt className="text-xl text-blue-400" />
              </div>
              <div>
                <h4 className="text-lg font-light">{t('settings.identity.verified.freeAccess', 'Free Access')}</h4>
                <p className="text-gray-400 text-[16px] font-light">
                  {t('settings.identity.verified.untilDate', 'Until May 15, 2025')}
                </p>
              </div>
            </div>
            <div className="text-[18px] text-gray-300 font-light">
              {t('settings.identity.verified.freeAccessDescription', 'Enjoy full access to Biela.dev at no cost')}
            </div>
          </div>
        </div>
        <div className="bg-black/20 rounded-xl p-6">
          <div className="flex flex-col lg:flex-row items-start gap-4">
            <div className={'flex items-center  gap-4  lg:mb-0'}>
              <div className="p-3 bg-black/20 rounded-xl mt-1 shrink-0">
                <FaShieldAlt className="text-xl text-yellow-400" />
              </div>
              <h4 className="block lg:hidden text-lg font-light ">
                {t('settings.identity.verified.secureStorage', 'Secure Storage')}
              </h4>
            </div>
            <div className="flex flex-col">
              <p className="text-gray-300 font-light mt-1 text-[18px]">
                <span className="hidden lg:block text-lg font-light mb-2">
                  {t('settings.identity.verified.secureStorage', 'Secure Storage')}
                </span>
                {t(
                  'settings.identity.verified.secureStorageDescription',
                  "Your card information is stored securely on Stripe's servers, not on Biela's servers. Your card will not be charged without your explicit consent for a subscription.",
                )}
                <br />
                {t(
                  'settings.identity.verified.subscriptionAvailable',
                  'Subscriptions will be available starting May 15, 2025.',
                )}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div
      className="max-w-[1600px] mx-auto rounded-xl"
      style={{
        backgroundImage:
          'radial-gradient(circle at top right, rgba(123, 31, 162, 0.15), transparent), radial-gradient(circle at bottom left, rgba(25, 118, 210, 0.15), transparent)',
      }}
    >
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
        {customerData?.isVerified ? renderVerifiedState() : renderUnverifiedState()}
      </motion.div>
    </div>
  );
}

export default IdentitySettings;
