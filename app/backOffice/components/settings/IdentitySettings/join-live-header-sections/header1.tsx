import React from 'react';

type HeaderTexts = {
  title1?: string;
  title2?: string;
  title3?: string;
  title4?: string;
  description1: string;
  description2: string;
  buttonText1: string;
  buttonText2: string;
};

type Props = {
  texts: HeaderTexts[];
};
export  const Header1: React.FC<Props> = ({texts}) => {
    return (
        <div
            style={{
                background:
                    "linear-gradient(0deg, rgba(192, 132, 252, 0.50) 19.11%, rgba(96, 165, 250, 0.50) 100%)",
            }}
            className={
                "relative flex flex-col overflow-hidden items-center justify-start gap-[29px]"
            }
        >
            <p
                className={
                    "p-[48px] pb-0 text-center rexton-light  text-[30px] not-italic font-medium leading-[36px] tracking-[-6px]"
                }
            >
                {texts[0].title1}{" "}
                <span className={"text-[#4ADE80]"}>{texts[0].title2}</span>
            </p>
            <div className={"pb-[48px]"}>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="147"
                    height="90"
                    viewBox="0 0 147 90"
                    fill="none"
                >
                    <g clipPath="url(#clip0_30695_4004)">
                        <path
                            d="M147 0L107.116 76.5957L78.8559 63.1149L57.3567 52.8511L75.2854 42.2809L76.7858 41.4L147 0Z"
                            fill="white"
                        />
                        <path d="M147 0L43.4922 45.1915L0 35.234L147 0Z" fill="white" />
                        <path
                            d="M78.8559 63.1149L57.3567 90V52.8511L78.8559 63.1149Z"
                            fill="#BFACAC"
                        />
                        <path
                            d="M147 0L57.3565 52.8511V90L43.4922 45.1915L147 0Z"
                            fill="#EAE5E5"
                        />
                    </g>
                    <defs>
                        <clipPath id="clip0_30695_4004">
                            <rect width="147" height="90" fill="white" />
                        </clipPath>
                    </defs>
                </svg>
            </div>
            <div className={"cloud-1 absolute bottom-0 left-0 right-0 w-full"}>
                <svg
                    className={"w-full"}
                    xmlns="http://www.w3.org/2000/svg"
                    width="684"
                    height="52"
                    viewBox="0 0 684 52"
                    fill="none"
                >
                    <path
                        d="M-8 39.498V259H684V5.6476C675.35 1.70668 665.427 0.531321 656.209 2.95118C638.771 7.52817 625.429 24.564 625.18 42.5816C617.61 36.8984 605.458 35.4326 596.988 39.6501C588.518 43.8676 582.816 53.1045 582.816 62.5627C572.948 50.09 556.008 44.5589 540.355 46.7437C531.981 47.8914 523.968 51.2516 517.629 56.921C525.311 43.5357 522.017 24.882 510.211 14.9399C498.419 4.99769 479.445 4.90089 467.556 14.7186C459.737 1.90027 441.869 -3.63084 428.168 2.50869C414.466 8.64822 406.729 25.6702 411.117 40.0234C400.695 38.696 389.845 42.0561 381.721 48.6658C378.828 51.0303 376.295 53.7821 374.233 56.8795C366.372 68.7437 365.874 85.0881 373.029 97.3949C360.532 96.2886 347.633 101.211 339.038 110.365C330.457 119.505 326.361 132.683 328.257 145.073C316.091 138.823 300.978 138.629 288.66 144.561C276.329 150.507 267.084 162.441 264.413 175.854C260.939 161.459 246.296 150.632 231.501 151.531C216.706 152.429 203.489 164.957 201.787 179.656L200.707 179.421C203.586 166.022 191.85 152.996 178.605 149.387C165.36 145.778 151.34 149.014 137.998 152.263C145.084 141.575 143.783 126.087 135.009 116.726C126.248 107.379 110.872 105.055 99.7306 111.416C106.194 98.3628 101.987 80.9675 90.2779 72.3113C78.5554 63.6689 60.6741 64.7475 50.0865 74.7588C48.0244 56.5338 33.091 40.5212 15.0436 37.1749C7.32092 35.7507 -0.692444 36.6771 -8 39.498Z"
                        fill="white"
                        fillOpacity="0.5"
                    />
                </svg>
            </div>
            <div className={"cloud-2 absolute bottom-0 left-0 right-0 w-full"}>
                <svg
                    className={"w-full"}
                    xmlns="http://www.w3.org/2000/svg"
                    width="684"
                    height="83"
                    viewBox="0 0 684 83"
                    fill="none"
                >
                    <path
                        d="M685 39.3455V258H-3V5.62579C5.6 1.70009 15.4659 0.52927 24.6301 2.93979C41.9677 7.49911 55.2323 24.4691 55.48 42.4172C63.0067 36.7559 75.088 35.2958 83.5091 39.497C91.9302 43.6982 97.5994 52.8995 97.5994 62.3211C107.41 49.8966 124.252 44.3869 139.815 46.5633C148.14 47.7065 156.107 51.0537 162.409 56.7012C154.772 43.3676 158.047 24.786 169.784 14.8822C181.508 4.97839 200.373 4.88197 212.193 14.6618C219.967 1.89293 237.731 -3.61682 251.354 2.49901C264.976 8.61483 272.668 25.5711 268.306 39.8689C278.667 38.5466 289.455 41.8937 297.532 48.4779C300.408 50.8333 302.926 53.5744 304.976 56.6599C312.792 68.4783 313.287 84.7596 306.173 97.0188C318.599 95.9169 331.423 100.821 339.968 109.939C348.499 119.044 352.572 132.171 350.687 144.513C362.782 138.287 377.808 138.094 390.054 144.003C402.315 149.926 411.506 161.813 414.162 175.175C417.616 160.836 432.174 150.05 446.883 150.946C461.593 151.841 474.733 164.32 476.426 178.963L477.499 178.728C474.637 165.381 486.306 152.406 499.474 148.81C512.642 145.215 526.581 148.439 539.846 151.676C532.801 141.028 534.094 125.601 542.818 116.275C551.528 106.964 566.815 104.65 577.892 110.986C571.466 97.983 575.649 80.6548 587.29 72.0321C598.945 63.4231 616.723 64.4975 627.249 74.4701C629.3 56.3155 644.147 40.3648 662.09 37.0314C669.768 35.6126 677.735 36.5355 685 39.3455Z"
                        fill="white"
                        fillOpacity="0.5"
                    />
                </svg>
            </div>
            <div className={"cloud-3 absolute bottom-0 left-0 right-0 w-full"}>
                <svg
                    className={"w-full"}
                    xmlns="http://www.w3.org/2000/svg"
                    width="684"
                    height="49"
                    viewBox="0 0 684 49"
                    fill="none"
                >
                    <path
                        d="M739 42.243V277H0V6.04009C9.2375 1.82529 19.8348 0.568247 29.6782 3.15628C48.301 8.05137 62.549 26.2711 62.815 45.5409C70.8997 39.4627 83.8765 37.8951 92.9219 42.4057C101.967 46.9163 108.057 56.7952 108.057 66.9107C118.595 53.5712 136.685 47.6557 153.402 49.9923C162.344 51.2198 170.901 54.8135 177.67 60.8769C169.467 46.5613 172.985 26.6113 185.592 15.9781C198.185 5.34502 218.448 5.2415 231.144 15.7415C239.495 2.03233 258.576 -3.88317 273.208 2.68304C287.841 9.24926 296.103 27.4542 291.417 42.805C302.547 41.3853 314.134 44.9789 322.81 52.048C325.899 54.5769 328.604 57.5198 330.806 60.8325C339.201 73.5213 339.733 91.0016 332.092 104.164C345.438 102.98 359.213 108.245 368.392 118.035C377.555 127.811 381.93 141.905 379.905 155.155C392.897 148.471 409.036 148.264 422.191 154.608C435.36 160.967 445.233 173.73 448.085 188.075C451.795 172.68 467.432 161.1 483.232 162.062C499.032 163.023 513.147 176.422 514.965 192.142L516.118 191.891C513.043 177.56 525.577 163.629 539.721 159.769C553.866 155.91 568.838 159.37 583.086 162.845C575.518 151.414 576.908 134.85 586.278 124.838C595.634 114.841 612.055 112.357 623.953 119.159C617.05 105.199 621.543 86.5945 634.047 77.3368C646.566 68.0938 665.662 69.2473 676.968 79.9544C679.171 60.4628 695.118 43.3374 714.391 39.7585C722.639 38.2353 731.196 39.2261 739 42.243Z"
                        fill="white"
                        fillOpacity="0.2"
                    />
                </svg>
            </div>
        </div>
    );
};
