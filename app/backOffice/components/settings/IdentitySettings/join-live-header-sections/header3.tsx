import React from 'react';

type HeaderTexts = {
  title1?: string;
  title2?: string;
  title3?: string;
  title4?: string;
  description1: string;
  description2: string;
  buttonText1: string;
  buttonText2: string;
};

type Props = {
  texts: HeaderTexts[];
};

export const Header3: React.FC<Props> = ({ texts }) => {
  return (
    <div
      style={{
        background:
          "linear-gradient(0deg, rgba(192, 132, 252, 0.50) 19.11%, rgba(96, 165, 250, 0.50) 100%)",
      }}
      className={
        "relative flex md:flex-row flex-col overflow-hidden items-center justify-between gap-[29px]"
      }
    >
      <p
        className={
          "md:py-[87px] md:pl-[48px] p-[48px] pb-[20px] md:text-left text-center rexton-light  text-[30px] not-italic font-medium leading-[36px] tracking-[-6px]"
        }
      >
        <span className={"text-[#4ADE80]"}>{texts[2].title1}</span> <br />
        {texts[2].title2}{" "}
        <span className={"text-[#C084FC]"}>{texts[2].title3}</span>
      </p>
      <div
        className={
          "pb-[48px] max-md:pb-[20px] max-md:w-full max-md:flex max-md:justify-end"
        }
      >
        <svg
          className={"max-md:w-[30vw] max-md:h-auto"}
          xmlns="http://www.w3.org/2000/svg"
          xlinkHref="http://www.w3.org/1999/xlink"
          width="320"
          height="177"
          viewBox="0 0 320 177"
          fill="none"
        >
          <rect
            y="29"
            width="320"
            height="148"
            fill="url(#pattern0_30728_892)"
          />
          <rect
            x="40.0681"
            y="44.3511"
            width="111.101"
            height="60.8879"
            transform="rotate(-23.3562 40.0681 44.3511)"
            fill="url(#pattern1_30728_892)"
          />
          <defs>
            <pattern
              id="pattern0_30728_892"
              patternContentUnits="objectBoundingBox"
              width="1"
              height="1"
            >
              <use
                xlinkHref="#image0_30728_892"
                transform="matrix(0.000269679 0 0 0.00058309 -0.00119898 0)"
              />
            </pattern>
            <pattern
              id="pattern1_30728_892"
              patternContentUnits="objectBoundingBox"
              width="1"
              height="1"
            >
              <use
                xlinkHref="#image1_30728_892"
                transform="matrix(0.000366854 0 0 0.000669393 -0.0129642 -0.030972)"
              />
            </pattern>
            <image
              id="image0_30728_892"
              width="3717"
              height="1715"
              preserveAspectRatio="none"
              xlinkHref="/images/microphone.png"
              
            />
            <image
              id="image1_30728_892"
              width="2797"
              height="1595"
              preserveAspectRatio="none"
              xlinkHref="/images/live.png"
            />
          </defs>
        </svg>
      </div>
    </div>
  );
};
