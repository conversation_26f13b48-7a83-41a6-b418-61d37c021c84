interface LoadingElements {
  logo: HTMLElement | null;
  logoContainer: HTMLElement | null;
  throttleLines: NodeListOf<Element>;
  stars: NodeListOf<Element>;
  circleElements: NodeListOf<Element>;
  bLetter: Element | null;
  trail: Element | null;
}

export function loadingFunctionIdentity(onLoadingComplete?: () => void, shouldStartExit?: boolean): void {
  const elements: LoadingElements = {
    logo: document.getElementById('biela-logo'),
    logoContainer: document.getElementById('logo-container'),
    throttleLines: document.querySelectorAll('.throttle-line'),
    stars: document.querySelectorAll('.star'),
    circleElements: document.querySelectorAll('.circle-element path'),
    bLetter: document.querySelector('.b-letter'),
    trail: document.querySelector('.trail'),
  };

  startAnimations(elements);

  if (shouldStartExit) {
    setTimeout(() => {
      completeLoading(elements, onLoadingComplete);
    }, 1000);
  }
}

function startAnimations(elements: LoadingElements): void {
  elements.stars.forEach((star: Element) => {
    if (star instanceof HTMLElement) {
      const delay = Math.random() * 2;
      const duration = 1 + Math.random() * 2;
      star.style.animation = `starTwinkle ${duration}s ease-in-out ${delay}s infinite`;
    }
  });

  elements.circleElements.forEach((circle: Element, index: number) => {
    if (circle instanceof HTMLElement) {
      circle.style.opacity = '0';
      setTimeout(() => {
        if (circle instanceof HTMLElement) {
          circle.style.opacity = '1';
          circle.style.transition = 'opacity 0.5s ease-in-out';
        }
      }, 100 * index);
    }
  });

  elements.throttleLines.forEach((line: Element, index: number) => {
    if (line instanceof HTMLElement) {
      setTimeout(() => {
        line.style.animation = 'throttlePulse 1.5s ease-in-out infinite';
      }, 200 * index);
    }
  });

  if (elements.bLetter instanceof HTMLElement) {
    elements.bLetter.style.animation = 'bPulse 2s ease-in-out infinite';
    createThrottleLoadingEffect(elements);
  }
}

function createThrottleLoadingEffect(elements: LoadingElements): void {
  let intensity = 0;
  const maxIntensity = 5;
  const interval = setInterval(() => {
    if (intensity >= maxIntensity) {
      clearInterval(interval);
      return;
    }

    if (elements.logo instanceof HTMLElement) {
      const randomX = (Math.random() - 0.5) * intensity;
      const randomY = (Math.random() - 0.5) * intensity;

      elements.logo.style.transform = `translate(${randomX}px, ${randomY}px)`;

      elements.circleElements.forEach((circle: Element) => {
        if (circle instanceof HTMLElement) {
          circle.style.filter = `drop-shadow(0 0 ${intensity * 3}px rgba(74, 222, 128, 0.8))`;
        }
      });

      elements.stars.forEach((star: Element) => {
        if (star instanceof HTMLElement) {
          star.style.filter = `drop-shadow(0 0 ${intensity * 2}px rgba(74, 222, 128, 0.8))`;
        }
      });
    }

    intensity += 0.5;
  }, 100);
}

function completeLoading(elements: LoadingElements, onLoadingComplete?: () => void): void {
  const container = elements.logoContainer;
  if (!(container instanceof HTMLElement)) return;

  container.style.animation = 'none';
  container.style.transform = '';
  void container.offsetWidth;

  requestAnimationFrame(() => {
    const handleAnimationEnd = function (this: HTMLElement, event: AnimationEvent) {
      this.removeEventListener('animationend', handleAnimationEnd);
      onLoadingComplete?.();
    };

    container.addEventListener('animationend', handleAnimationEnd);

    container.style.animation = 'diagonalExit 1s ease-out forwards';
  });

  if (elements.trail instanceof HTMLElement) {
    elements.trail.style.animation = 'diagonalTrail 1s ease-out forwards';
  }

  setTimeout(() => {
    onLoadingComplete?.();
  }, 1100);
}
