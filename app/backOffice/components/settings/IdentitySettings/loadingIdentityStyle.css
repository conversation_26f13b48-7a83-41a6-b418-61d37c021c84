.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-height: 200px;
  position: relative;
  overflow: visible;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.logo-container-login {
  position: relative;
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-origin: center;
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.logoLogin {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 0 10px rgba(34, 197, 94, 0.5));
  position: relative;
  z-index: 2;
  backface-visibility: hidden;
}

.trail-container {
  position: absolute;
  bottom: -20px;
  left: 50%;
  width: 100%;
  height: 40px;
  overflow: hidden;
  transform: translateZ(0);
}

.trail {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, rgba(34, 197, 94, 0.8), transparent);
  transform: translateZ(0);
}

.exit-animation {
  animation: diagonalExit 1s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
  will-change: transform, opacity;
  backface-visibility: hidden;
}

@keyframes diagonalExit {
  0% {
    transform: translate(0, 0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(200px, -200px) scale(0);
    opacity: 0;
  }
}

@keyframes diagonalTrail {
  0% {
    transform: translate(-50%, 0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(150px, -150px) scale(0);
    opacity: 0;
  }
}

@keyframes throttlePulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.98);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

@keyframes starTwinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.95);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes bPulse {
  0%, 100% {
    opacity: 1;
    filter: drop-shadow(0 0 2px rgba(34, 197, 94, 0.5));
  }
  50% {
    opacity: 0.7;
    filter: drop-shadow(0 0 5px rgba(34, 197, 94, 0.8));
  }
}

@keyframes drawLine {
  from {
    stroke-dashoffset: 1000;
  }
  to {
    stroke-dashoffset: 0;
  }
}

.cls-2 {
  fill: #22c55e;
  stroke: #22c55e;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.throttle-line {
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
  animation: drawLine 2s ease-out forwards;
}

.cls-1 {
  fill: none;
  stroke: none;
}

.star {
  animation: starTwinkle 2s ease-in-out infinite;
}

.circle-element path {
  opacity: 1;
  transition: opacity 0.5s ease-in-out;
}

.b-letter {
  animation: bPulse 2s ease-in-out infinite;
}

