import { IUserData } from '~/routes/profile';
import React from 'react';

export interface IUserGeneral {
  userData: IUserData;
  handleChange: (key: keyof IUserData, value: string) => void;
}

export interface SectionProps {
  title: string;
  icon: React.ElementType;
  children: React.ReactNode;
  className?: string;
}

export interface InputFieldProps {
  label: string;
  type: string;
  value: string;
  onChange?: (value: string) => void;
  readOnly?: boolean;
  disabled?: boolean;
  className?: string;
}

export interface AuthSectionProps {
  platform: string;
  icon: React.ElementType;
  connected?: boolean;
  email?: string;
}

export interface InfoSectionProps {
  icon: React.ElementType;
  title: string;
  children: React.ReactNode;
  childrenClassName?: string;
}

export interface InputUserFieldProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  className?: string;
  disabled?: boolean;
  pattern?: string;
}

export interface SelectFieldProps {
  label: string;
  options: string[] | { value: string; label: string }[];
  value: string;
  onChange: (value: string) => void;
  className?: string;
  disabled?: boolean;
}
