import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { AnimatePresence, motion } from 'framer-motion';
import { FaCheck, FaChevronDown, FaGlobe } from 'react-icons/fa';

interface Language {
  code: string;
  name: string;
  flag: string;
}

interface LanguageSelectorProps {
  large?: boolean;
  withBorder?: boolean;
  hideText?: boolean;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
   hideText=false,
   large = false,
   withBorder = false,
 }) => {
  const { i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const languages: Language[] = [
    { code: "en", name: "English", flag: "🇺🇸" },
    { code: "es", name: "<PERSON>spa<PERSON><PERSON>", flag: "🇪🇸" },
    { code: "fr", name: "Français", flag: "🇫🇷" },
    { code: "de", name: "<PERSON><PERSON><PERSON>", flag: "🇩🇪" },
    { code: "it", name: "Italiano", flag: "🇮🇹" },
    { code: "pt", name: "<PERSON>uguê<PERSON>", flag: "🇵🇹" },
    { code: "ru", name: "Русский", flag: "🇷🇺" },
    { code: "ro", name: "Română", flag: "🇷🇴" },
    { code: "zh", name: "中文", flag: "🇨🇳" },
    { code: "ja", name: "日本語", flag: "🇯🇵" },
    { code: "ko", name: "한국어", flag: "🇰🇷" },
    { code: "ar", name: "العربية", flag: "🇸🇦" },
    { code: "az", name: "Azərbaycan", flag: "🇦🇿" },
  ];

  const currentLanguage =
    languages.find((lang) => lang.code === i18n.language) || languages[0];

  const setUserLanguageCookie = (langCode: string) => {
    document.cookie = `user-language-cookie=${langCode}; path=/; max-age=31536000`;
  };

  const getUserLanguageCookie = (): string | null => {
    const match = document.cookie.match(
      /(^|;) ?user-language-cookie=([^;]*)(;|$)/
    );
    return match ? match[2] : null;
  };

  const changeLanguage = (langCode: string) => {
    i18n.changeLanguage(langCode);
    setUserLanguageCookie(langCode);
    setIsOpen(false);
  };

  useEffect(() => {
    const savedLang = getUserLanguageCookie();
    if (savedLang && savedLang !== i18n.language) {
      i18n.changeLanguage(savedLang);
    }
  }, [i18n]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative w-full focus-invisible" ref={dropdownRef}>
      <button
        style={{width: '100%', justifyContent: 'space-between'}}
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center gap-2 bg-transparent ${
          withBorder && "border-green-500 border"
        } px-3 py-2 rounded-lg transition-colors text-white/80 ${
          large && "w-100"
        }`}
      >
        <div className={'flex items-center gap-2'}>
          <FaGlobe className="text-sm" />
          <span className={`font-light text-sm ${hideText && ' max-[450px]:hidden'} ${large && "w-full text-left"}`}>
          {currentLanguage.flag} {currentLanguage.name}
        </span>
        </div>
        <FaChevronDown
          className={`text-xs transition-transform ${
            isOpen ? "rotate-180" : ""
          }`}
        />
      </button>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.95 }}
            transition={{ duration: 0.1 }}
            className={`absolute right-0 mt-2 w-48 bg-gray-900/95 backdrop-blur-sm rounded-lg shadow-lg border border-gray-800 overflow-x-hidden overflow-y-auto z-50 ${
              large && "w-full"
            }`}
          >
            <div
              className="max-h-[150px]">
              {languages.map((language) => (
                <button
                  key={language.code}
                  onClick={() => changeLanguage(language.code)}
                  className="w-full text-left px-4 py-2 text-white/80 bg-dark hover:bg-dark-800/50 transition-colors flex items-center justify-between"
                >
                  <span className="font-light">
                    {language.flag} {language.name}
                  </span>
                  {language.code === i18n.language && (
                    <FaCheck className="text-green-400 text-sm" />
                  )}
                </button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LanguageSelector;
