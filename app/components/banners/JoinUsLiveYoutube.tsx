import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { ChevronLeft, ChevronRight, X } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Header1 } from '../../backOffice/components/settings/IdentitySettings/join-live-header-sections/header1';
import { Header2 } from '../../backOffice/components/settings/IdentitySettings/join-live-header-sections/header2';
import { Header3 } from '../../backOffice/components/settings/IdentitySettings/join-live-header-sections/header3';

interface TextContent {
  title1: string;
  title2?: string;
  title3?: string;
  title4?: string;
  description1: string;
  description2: string;
  description3: string;
  description4: string;
  buttonText1: string;
  buttonText2: string;
}

export default function JoinUsLiveYoutube(): JSX.Element {
  const { t } = useTranslation('translation');
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const modalRef = useRef<HTMLDivElement>(null);

  const handleClickOutside = (e: MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
      onClose();
    }
  };

  useEffect(() => {
    const today = new Date().toISOString().split("T")[0];
    const lastClosed = localStorage.getItem("dailySectionClosedAt");

    if (lastClosed !== today) {
      setIsVisible(true);
      setCurrentIndex(Math.floor(Math.random() * 3));
    }
  }, []);

  const onClose = () => {
    setIsVisible(!isVisible);
    const today = new Date().toISOString().split("T")[0];
    localStorage.setItem("dailySectionClosedAt", today);
  };

  const texts: TextContent[] = [
    {
      title1: "YOU'RE",
      title2: "INVITED",
      description1: "Join us this Friday for a live vibe coding session — where we’ll show you how to think, build, and launch smarter using Biela.dev.",
      description2: "You’ll learn how to write better prompts, build real projects from scratch, and get more out of the platform — with purpose, precision, and clarity.",
      description3: "We’ll also review community-submitted projects, explore use cases, and wrap up with a live Q&A.",
      description4: "It’s the perfect session to level up your build game, whether you’re just getting started or already deep in creation. Don’t miss it.",
      buttonText1: "Click Here And Tap “Notify Me",
      buttonText2: "On YouTube”",
    },
    {
      title1: "BE",
      title2: "FIRST",
      title3: "TO",
      title4: "BUILD",
      description1: "Join us this Friday for a live vibe coding session — where we’ll show you how to think, build, and launch smarter using Biela.dev.",
      description2: "You’ll learn how to write better prompts, build real projects from scratch, and get more out of the platform — with purpose, precision, and clarity.",
      description3: "We’ll also review community-submitted projects, explore use cases, and wrap up with a live Q&A.",
      description4: "It’s the perfect session to level up your build game, whether you’re just getting started or already deep in creation. Don’t miss it.",
      buttonText1: "Click Here To Set Your YouTube",
      buttonText2: "Reminder",
    },
    {
      title1: "SAVE",
      title2: "YOUR",
      title3: "SPOT",
      description1: "Join us this Friday for a live vibe coding session — where we’ll show you how to think, build, and launch smarter using Biela.dev.",
      description2: "You’ll learn how to write better prompts, build real projects from scratch, and get more out of the platform — with purpose, precision, and clarity.",
      description3: "We’ll also review community-submitted projects, explore use cases, and wrap up with a live Q&A.",
      description4: "It’s the perfect session to level up your build game, whether you’re just getting started or already deep in creation. Don’t miss it.",
      buttonText1: "Click Here And Tap “Notify Me",
      buttonText2: "On YouTube”",
    },
  ];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={handleClickOutside}
      className={`fixed inset-0 bg-black/0 w-[100vw] min-h-[100svh] ${!isVisible ? 'pointer-events-none' : ''} transition-opacity flex items-center justify-end z-50`}
    >
      <motion.div
        initial={{ transform: 'translateX(calc(100% - 2.5rem + 4px))' }}
        animate={{
          transform: isVisible ? 'translateX(0)' : 'translateX(calc(100% - 2.5rem + 4px))',
        }}
        exit={{ transform: 'translateX(calc(100% - 2.5rem + 4px))' }}
        className="w-[90%] max-md:max-h-[calc(100vh_-_100px)] max-md:mt-[50px] max-md:mb-[50px] md:w-full max-w-[680px] flex rounded-xl relative overflow-hidden items-center"
      >
        <motion.div
          onClick={() => {
            setIsVisible(!isVisible);
          }}
          className="w-10 h-10 md:w-12 md:h-12 pointer-events-auto bg-[#0A1730] hover:bg-[#0A1730]/80 border border-white/10 hover:border-[#34D399] rounded-l-xl flex items-center justify-center cursor-pointer transition-all duration-300 shadow-md"
        >
          {isVisible ? (
            <ChevronRight className="w-5 h-5 text-white hover:text-[#34D399] transition-colors duration-300" />
          ) : (
            <ChevronLeft className="w-5 h-5 text-white hover:text-[#34D399] transition-colors duration-300" />
          )}
        </motion.div>

        <motion.div
          ref={modalRef}
          className="w-full max-md:max-h-[calc(100vh_-_100px)] pointer-events-auto flex flex-col rounded-xl bg-[#0A1730] relative overflow-hidden"
        >
          <button
            onClick={onClose}
            className="absolute top-4 left-4 w-8 h-8 rounded-full flex items-center justify-center border border-white/10 bg-transparent hover:bg-[#34D399]/20 hover:border-[#34D399] transition-colors duration-200 z-20"
          >
            <X className="w-4 h-4 text-white/70 hover:text-[#34D399] transition-colors duration-200" />
          </button>

          <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            {currentIndex === 0 ? (
              <Header1 texts={texts} />
            ) : currentIndex === 1 ? (
              <Header2 texts={texts} />
            ) : (
              <Header3 texts={texts} />
            )}
          </motion.div>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className={'relative max-md:p-0 p-[30px] overflow-hidden '}
          >
            <div className=" absolute right-[-200px] top-[-250px] w-[353px] h-[353px] rounded-[353px] bg-[#20C55E] blur-[100px]"></div>
            <div className=" absolute left-[-200px] bottom-[-250px] w-[353px] h-[353px] rounded-[353px] bg-[#C084FC] blur-[100px]"></div>
            <div
              className={
                'max-md:overflow-y-auto scrollbar-hide flex flex-col gap-[32px] max-md:gap-[20px] max-md:p-[20px]  max-md:max-h-[calc(100vh_-_387px)]  '
              }
            >
              <motion.div
                className={'absolute top-[-100px] left-0 w-full h-full'}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="684" height="587" viewBox="0 0 684 587" fill="none">
                  <line
                    x1="166.812"
                    y1="1.75665e-08"
                    x2="166.812"
                    y2="620"
                    stroke="url(#paint0_linear_30695_3967)"
                    strokeOpacity="0.25"
                    strokeWidth="1.04584"
                  />
                  <line
                    x1="333.102"
                    y1="1.75665e-08"
                    x2="333.102"
                    y2="620"
                    stroke="url(#paint1_linear_30695_3967)"
                    strokeOpacity="0.25"
                    strokeWidth="1.04584"
                  />
                  <line
                    x1="499.391"
                    y1="1.75665e-08"
                    x2="499.391"
                    y2="620"
                    stroke="url(#paint2_linear_30695_3967)"
                    strokeOpacity="0.25"
                    strokeWidth="1.04584"
                  />
                  <line
                    x1="-1.2455e-08"
                    y1="464.477"
                    x2="684"
                    y2="464.477"
                    stroke="url(#paint3_linear_30695_3967)"
                    strokeOpacity="0.25"
                    strokeWidth="1.04584"
                  />
                  <line
                    x1="-1.2455e-08"
                    y1="309.477"
                    x2="684"
                    y2="309.477"
                    stroke="url(#paint4_linear_30695_3967)"
                    strokeOpacity="0.25"
                    strokeWidth="1.04584"
                  />
                  <line
                    x1="-1.2455e-08"
                    y1="154.477"
                    x2="684"
                    y2="154.477"
                    stroke="url(#paint5_linear_30695_3967)"
                    strokeOpacity="0.25"
                    strokeWidth="1.04584"
                  />
                  <defs>
                    <linearGradient
                      id="paint0_linear_30695_3967"
                      x1="165.789"
                      y1="-1.67965e-08"
                      x2="165.789"
                      y2="620"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#132344" stopOpacity="0" />
                      <stop offset="0.55" stopColor="#4362A1" />
                      <stop offset="1" stopColor="#132344" stopOpacity="0" />
                    </linearGradient>
                    <linearGradient
                      id="paint1_linear_30695_3967"
                      x1="332.079"
                      y1="-1.67965e-08"
                      x2="332.079"
                      y2="620"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#132344" stopOpacity="0" />
                      <stop offset="0.55" stopColor="#4362A1" />
                      <stop offset="1" stopColor="#132344" stopOpacity="0" />
                    </linearGradient>
                    <linearGradient
                      id="paint2_linear_30695_3967"
                      x1="498.368"
                      y1="-1.67965e-08"
                      x2="498.368"
                      y2="620"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#132344" stopOpacity="0" />
                      <stop offset="0.55" stopColor="#4362A1" />
                      <stop offset="1" stopColor="#132344" stopOpacity="0" />
                    </linearGradient>
                    <linearGradient
                      id="paint3_linear_30695_3967"
                      x1="1.1909e-08"
                      y1="465.5"
                      x2="684"
                      y2="465.5"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#4362A1" stopOpacity="0" />
                      <stop offset="0.55" stopColor="#4362A1" />
                      <stop offset="1" stopColor="#4362A1" stopOpacity="0" />
                    </linearGradient>
                    <linearGradient
                      id="paint4_linear_30695_3967"
                      x1="1.1909e-08"
                      y1="310.5"
                      x2="684"
                      y2="310.5"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#4362A1" stopOpacity="0" />
                      <stop offset="0.55" stopColor="#4362A1" />
                      <stop offset="1" stopColor="#4362A1" stopOpacity="0" />
                    </linearGradient>
                    <linearGradient
                      id="paint5_linear_30695_3967"
                      x1="1.1909e-08"
                      y1="155.5"
                      x2="684"
                      y2="155.5"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#4362A1" stopOpacity="0" />
                      <stop offset="0.55" stopColor="#4362A1" />
                      <stop offset="1" stopColor="#4362A1" stopOpacity="0" />
                    </linearGradient>
                  </defs>
                </svg>
              </motion.div>

              <div className={'flex flex-col gap-[18px] max-md:gap-[20px] relative max-w-[525px] m-auto'}>
                <p className={'text-white text-center text-[18px] not-italic font-light leading-[28px]'}>
                  {texts[currentIndex].description1}
                </p>
                <p className={'text-center text-white text-[18px] not-italic font-light leading-[28px]'}>
                  {texts[currentIndex].description2}
                </p>
                <p className={'text-center text-white text-[18px] not-italic font-light leading-[28px]'}>
                  {texts[currentIndex].description3}
                </p>
                <p className={'text-center text-white text-[18px] not-italic font-light leading-[28px]'}>
                  {texts[currentIndex].description4}
                </p>
              </div>
              <a
                href={'https://live.biela.dev'}
                onClick={onClose}
                target={'_blank'}
                style={{
                  background: 'linear-gradient(92deg, #22C55E 28.36%, #49E583 49.66%, #22C55E 76.25%)',
                }}
                className=" flex-wrap transform transition duration-200 ease-in-out hover:scale-105 relative rounded-lg flex px-8 py-[14.5px] justify-center items-center text-center gap-[10px] self-stretch"
              >
                <p>{texts[currentIndex].buttonText1}</p>
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19" fill="none">
                  <g clipPath="url(#clip0_30695_3998)">
                    <path
                      d="M5.56812 16.25C5.85886 16.9185 6.33854 17.4876 6.94823 17.8873C7.55792 18.287 8.27108 18.5 9.00012 18.5C9.72915 18.5 10.4423 18.287 11.052 17.8873C11.6617 17.4876 12.1414 16.9185 12.4321 16.25H5.56812Z"
                      fill="white"
                    />
                    <path
                      d="M16.7939 9.91127L15.4919 5.61902C15.0749 4.11778 14.1684 2.79885 12.9163 1.87165C11.6641 0.944442 10.1381 0.462108 8.58054 0.501229C7.02297 0.540349 5.5231 1.09868 4.31908 2.08757C3.11507 3.07647 2.27591 4.43924 1.93486 5.95952L0.923856 10.1123C0.789481 10.6641 0.782201 11.2392 0.902564 11.7943C1.02293 12.3493 1.26779 12.8697 1.61867 13.3163C1.96956 13.7629 2.41729 14.124 2.92809 14.3722C3.43889 14.6205 3.99942 14.7495 4.56736 14.7495H13.2051C13.7907 14.7495 14.3681 14.6124 14.8911 14.3492C15.4142 14.0859 15.8683 13.7039 16.2171 13.2336C16.566 12.7633 16.7998 12.2178 16.9 11.6409C17.0001 11.064 16.9638 10.4716 16.7939 9.91127Z"
                      fill="white"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_30695_3998">
                      <rect width="18" height="18" fill="white" transform="translate(0 0.5)" />
                    </clipPath>
                  </defs>
                </svg>
                <p>{texts[currentIndex].buttonText2}</p>
              </a>
            </div>
          </motion.div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
}
