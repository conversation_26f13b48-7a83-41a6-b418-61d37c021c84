import React, { useEffect, useRef, useState } from 'react';
import { ArrowPathIcon, LinkIcon, MicrophoneIcon, Square2StackIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import MagicIcon from '~/assets/icons/magicIcon.svg?url';
import { AnimatePresence, motion } from 'framer-motion';
import ExtendedThinkingToggle from './ExtendedThinkingToggle';
import { useTranslation } from 'react-i18next';
import { ChatDescription } from '~/ai/lib/persistence/ChatDescription.client';
import ClientOnly from '~/components/common/ClientOnly';
import AIModelSelector from './AIModelSelector.tsx';
import { createPortal } from 'react-dom';
import BarVisualizer from '~/components/common/BarVisualizer';
import { clearChatMessages, downloadChatMessages } from '~/ai/lib/stores/projects/chat';
import { useParams } from 'react-router-dom';
import { useStore } from '@nanostores/react';
import { chatId as chatIdStore } from '~/ai/lib/persistence/useChatHistory.client';
import { generateProjectSlug } from '~/ai/lib/persistence/useChatHistory.client';
import ClearChatHistoryModal from '~/components/common/ClearChatHistoryModal';
import { useChatHistory } from '~/ai/lib/persistence/useChatHistory.client';
import { useNavigate } from 'react-router-dom';

const languages = [
  { code: 'en', label: 'English', img: '/flags/en.png' },
  { code: 'es', label: 'Spanish', img: '/flags/es.png' },
  { code: 'fr', label: 'French', img: '/flags/fr.png' },
  { code: 'de', label: 'German', img: '/flags/de.png' },
  { code: 'it', label: 'Italian', img: '/flags/it.png' },
  { code: 'pt', label: 'Portuguese', img: '/flags/pt.png' },
  { code: 'ru', label: 'Russian', img: '/flags/ru.png' },
  { code: 'ro', label: 'Romanian', img: '/flags/ro.png' },
  { code: 'zh', label: 'Chinese', img: '/flags/zh.png' },
  { code: 'ja', label: 'Japanese', img: '/flags/ja.png' },
  { code: 'ko', label: 'Korean', img: '/flags/ko.png' },
  { code: 'ar', label: 'Arabic', img: '/flags/ar.png' },
  { code: 'az', label: 'Azerbaijani', img: '/flags/az.png' },
];

interface ActionButtonsProps {
  onFileClick: () => void;
  isListening: boolean;
  onStartVoice: (languageCode: string) => void;
  onStopVoice: () => void;
  enhancePrompt: () => void;
  input: string;
  enhancingPrompt: boolean;
  handleCleanUp: () => void;
  chatStarted: boolean;
  hasContent: boolean;
  hasHistory: boolean;
}

//! Create A Hook To Detect Fire Fox:
const useFireFox = () => {
  const [isFireFox, setIsFireFox] = useState(false);

  useEffect(() => {
    if (typeof navigator !== undefined) {
      setIsFireFox(/firefox/i.test(navigator.userAgent));
    }
  }, []);

  return isFireFox;
};

const ActionButtons: React.FC<ActionButtonsProps> = ({
  onFileClick,
  isListening,
  onStartVoice,
  onStopVoice,
  enhancePrompt,
  input,
  enhancingPrompt,
  handleCleanUp,
  chatStarted,
  hasContent,
  hasHistory,
}) => {
  const { t } = useTranslation();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [selectedLanguage, setSelectedLanguage] = useState(languages[0]);
  const [showClearModal, setShowClearModal] = useState(false);
  const isFireFox = useFireFox(); // detection Hook Here.

  // Directly use the chatStarted prop to determine compact mode
  const isCompactMode = chatStarted;

  const { chatId: chatIdFromParams } = useParams();
  const chatId = chatIdFromParams || useStore(chatIdStore);
  const projectSlug = generateProjectSlug(chatId);
  const { loadChat } = useChatHistory();
  const navigate = useNavigate();

  const handleClearChat = async () => {
    setShowClearModal(true);
  };

  const handleConfirmClear = async () => {
    setShowClearModal(false);

    if (!projectSlug || !chatId) {
      toast.error(t('ChatNotFound', 'No chat or user found'));
      return;
    }

    try {
      const cleanResponse = await clearChatMessages(projectSlug);

      if (!cleanResponse?.cleaned) {
        toast.error(t('ChatClearFailedServer', 'Failed to clear chat history (server error).'));
        return;
      }

      if (cleanResponse.cleaned && cleanResponse.deletedCount === 0) {
        toast.info(t('NoMessagesToClear', 'No messages to clear'));
        return;
      }

      const messagesResponse = await downloadChatMessages(projectSlug, 0, 100);
      const remainingMessages = (messagesResponse?.items || []).filter((msg) => !msg.hiddenAt);

      const { openDatabase, setMessages, getMessages } = await import('~/ai/lib/persistence/db');
      const db = await openDatabase();
      const oldChat = await getMessages(db, chatId);

      await setMessages(db, chatId, remainingMessages, oldChat?.urlId, oldChat?.description, new Date().toISOString());

      const updatedChat = await getMessages(db, chatId);

      const newId = updatedChat?.id;

      if (newId) {
        localStorage.setItem('chatId', newId);

        if (window.location.pathname !== `/chat/${newId}`) {
          navigate(`/chat/${newId}`, { replace: true });
        }
      }

      await loadChat(chatId);
      window.dispatchEvent(new Event('biela:refresh-chat'));

      toast.success(t('ChatClearSuccess', 'Chat history cleared!'));
    } catch (e) {
      toast.error(t('ChatClearFailedUnexpected', 'Failed to clear chat history (unexpected error).'));
      console.error('Error clearing chat history:', e);
    }
  };

  //Function To Handle Select Language:
  const handleLanguageSelect = (language: (typeof languages)[number]) => {
    setSelectedLanguage(language);
    setIsDropdownOpen(false);
  };

  // Toggle voice input based on current state
  const handleVoice = () => {
    if (isListening) {
      onStopVoice();
    } else {
      onStartVoice(selectedLanguage.code);
    }
  };

  // Only allow enhancement when input exists and not already processing
  const handleEnhancePrompt = () => {
    if (hasContent && !enhancingPrompt) {
      enhancePrompt();
      toast.success('Prompt enhanced!');
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const dropdownEl = document.querySelector('.language-dropdown');
      const target = event.target as Node;

      if (dropdownRef.current && !dropdownRef.current.contains(target) && dropdownEl && !dropdownEl.contains(target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Define buttons with the same design and new logic
  const buttons = [
    {
      icon: LinkIcon,
      tooltip: t('attachFile'),
      onClick: onFileClick,
      hasLanguageSelector: false,
    },
    {
      icon: MicrophoneIcon,
      tooltip: isFireFox ? t('notAvailableInFirefox') : t('voiceInput'),
      onClick: isFireFox ? undefined : handleVoice,
      disabled: isFireFox,
      hasLanguageSelector: !isFireFox,
    },
    {
      icon: MagicIcon,
      tooltip: t('enhancePrompt'),
      onClick: handleEnhancePrompt,
      disabled: !hasContent || enhancingPrompt,
      hasLanguageSelector: false,
    },
    {
      icon: ArrowPathIcon,
      tooltip: t('cleanUpProject'),
      onClick: handleCleanUp,
      hasLanguageSelector: false,
    },
    hasHistory && {
      icon: Square2StackIcon,
      tooltip: t('clearChatHistory', 'Clear chat history'),
      onClick: handleClearChat,
      hasLanguageSelector: false,
    },
  ].filter(Boolean);

  return (
    <>
      <div className="max-w-fit md:w-full md:border-r md:border-[#FFFFFF19] flex items-center flex-wrap gap-1.5 md:gap-3 p-1 md:p-3 hover:bg-[#FFFFFF05] transition-all duration-300 ease-in-out settings-icons">
        {chatStarted && <AIModelSelector isCompact={true} />}
        {buttons.map(({ icon: Icon, tooltip, onClick, hasLanguageSelector, disabled }, index) => (
          <React.Fragment key={index}>
            <button
              onClick={onClick}
              disabled={disabled}
              className={`action-button p-1.5 ${tooltip === t('enhancePrompt') && 'opacity-[0.5]'} rounded-md transition-all duration-200 relative group bg-transparent`}
            >
              {tooltip === t('enhancePrompt') ? (
                <img src={Icon} alt={t('enhancePrompt')} />
              ) : (
                <Icon className="w-4 h-4 text-white/50 group-hover:text-white/70 transition-colors" />
              )}
              {/* If Browser FireFox */}
              {/*{isFireFox && Icon === MicrophoneIcon ? (*/}
              {/*  <div className="pointer-events-none absolute bottom-[130%] left-[120px] transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity z-10 w-max">*/}
              {/*    <div className="relative bg-black text-white text-xs p-2 rounded shadow-lg">{tooltip}</div>*/}
              {/*  </div>*/}
              {/*) : (*/}
              {/*  <span className="tooltip">{tooltip}</span>*/}
              {/*)}*/}
            </button>
            {hasLanguageSelector && (
              <SelectorLanguage
                selectedLanguage={selectedLanguage}
                onLanguageSelect={handleLanguageSelect}
                isDropdownOpen={isDropdownOpen}
                setIsDropdownOpen={setIsDropdownOpen}
                dropdownRef={dropdownRef}
                isHomePage={window.location.pathname === '/'}
                isListening={isListening}
              />
            )}
          </React.Fragment>
        ))}

        <div className="h-6 w-px bg-white/10 mx-1 hidden md:block"></div>
        <ExtendedThinkingToggle isCompact={chatStarted} />
      </div>
      <ClearChatHistoryModal
        isOpen={showClearModal}
        onClose={() => setShowClearModal(false)}
        onConfirm={handleConfirmClear}
      />
    </>
  );
};

export default ActionButtons;

// Selector Language Component:
interface ISelectorLanguageProps {
  selectedLanguage: (typeof languages)[number];
  onLanguageSelect: (language: (typeof languages)[number]) => void;
  isDropdownOpen: boolean;
  setIsDropdownOpen: React.Dispatch<React.SetStateAction<boolean>>;
  dropdownRef: React.RefObject<HTMLDivElement>;
  isHomePage?: boolean;
  isListening?: boolean;
}

const SelectorLanguage: React.FC<ISelectorLanguageProps> = ({
  selectedLanguage,
  onLanguageSelect,
  isDropdownOpen,
  setIsDropdownOpen,
  dropdownRef,
  isHomePage = false,
  isListening = false,
}) => {
  const dropdownPosition = isHomePage ? 'top-full mt-2' : 'bottom-full mb-2';
  const { t } = useTranslation('translation');

  const buttonRef = useRef<HTMLButtonElement>(null);
  const [coords, setCoords] = useState({ top: 0, left: 0 });

  const handleToggleDropdown = () => {
    if (!isListening) {
      if (buttonRef.current) {
        const rect = buttonRef.current.getBoundingClientRect();
        setCoords({ top: rect.bottom + window.scrollY - 280, left: rect.left + window.scrollX });
      }

      setIsDropdownOpen(!isDropdownOpen);
    }
  };

  const dropdownEl = (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 10, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: 10, scale: 0.95 }}
        transition={{ duration: 0.1 }}
        style={{ position: 'absolute', top: coords.top, left: coords.left }}
        className="language-dropdown absolute left-0 bg-[#0a1730] backdrop-blur-sm rounded-lg shadow-lg border border-gray-800 z-10 min-w-[140px]" // <-- Added class here
      >
        <div className="p-2 grid grid-cols-3 gap-2">
          {languages.map((lang) => (
            <button
              key={lang.code}
              onClick={() => onLanguageSelect(lang)}
              className={`
                w-10 h-10 flex items-center justify-center rounded hover:bg-gray-700/30 transition-colors ${
                  lang.code === selectedLanguage.code ? 'bg-accent/20 ring-1 ring-accent/40' : ''
                }`}
              title={lang.code.toUpperCase()}
            >
              <img src={lang.img} alt={lang.label} className="w-5 h-5 rounded object-contain" />
            </button>
          ))}
        </div>
      </motion.div>
    </AnimatePresence>
  );

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Dropdown Toggle Button */}
      <button
        ref={buttonRef}
        onClick={handleToggleDropdown}
        className={`action-button bg-transparent transition-colors relative group p-1 ${
          isListening ? 'cursor-not-allowed opacity-50' : ''
        }`}
      >
        <div className="relative w-5 h-5">
          <ClientOnly>
            {() => (
              <BarVisualizer isListening={isListening} width={20} height={22} barWidth={1} gap={1} color="#49de80" />
            )}
          </ClientOnly>

          <img
            src={selectedLanguage.img}
            alt={selectedLanguage.label}
            className={`absolute top-0 left-0 w-full h-full rounded object-contain transition-opacity duration-200 ease-in-out
                    ${isListening ? 'opacity-0' : 'opacity-100'}`}
          />
        </div>

        <span className="tooltip">
          {isListening
            ? t('languageSelectionDisabled', 'Language selection disabled during recording')
            : t('selectLanguage', 'Select language for voice input')}
        </span>
      </button>

      {isDropdownOpen && !isListening && createPortal(dropdownEl, document.body)}
    </div>
  );
};
