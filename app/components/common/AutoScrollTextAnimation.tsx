import React, { useRef, useState, useEffect } from "react";

const ScrollableText = ({ text }: { text: string }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const textRef = useRef<HTMLSpanElement>(null);
  const [shouldScroll, setShouldScroll] = useState(false);

  useEffect(() => {
    const containerWidth = containerRef.current?.offsetWidth || 0;
    const textWidth = textRef.current?.scrollWidth || 0;
    setShouldScroll(textWidth > containerWidth);
  }, [text]);

  return (
    <div
      className={`scroll-container-wrapper ${shouldScroll ? "with-dots" : ""}`}
    >
      <div className="scroll-container" ref={containerRef}>
        <span
          ref={textRef}
          className={`scroll-text ${shouldScroll ? "animate-scroll" : ""}`}
        >
          {text}
        </span>
      </div>
    </div>
  );
};

export default ScrollableText;
