import React, { useEffect, useRef } from 'react';

interface BarVisualizerProps {
  isListening: boolean;
  width?: number;
  height?: number;
  barWidth?: number;
  gap?: number;
  color?: string;
  smoothing?: number;
  fftSize?: number;
}

const BarVisualizer: React.FC<BarVisualizerProps> = ({
                                                       isListening,
                                                       width = 500,
                                                       height = 55,
                                                       barWidth = 1,
                                                       gap = 1,
                                                       color = '#49de80',
                                                       smoothing = 0.8,
                                                       fftSize = 128,
                                                     }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const state = useRef<{
    ctx: AudioContext;
    analyser: AnalyserNode;
    data: Uint8Array;
    stream: MediaStream;
    rafId: number;
  } | null>(null);

  useEffect(() => {
    const cleanup = () => {
      if (state.current) {
        state.current.stream.getTracks().forEach((t) => t.stop());
        state.current.ctx.close();
        cancelAnimationFrame(state.current.rafId);
        state.current = null;
      }
      const c = canvasRef.current?.getContext('2d');
      if (c) c.clearRect(0, 0, width, height);
    };

    if (!isListening) {
      cleanup();
      return;
    }

    let mounted = true;
    (async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        if (!mounted) {
          stream.getTracks().forEach(t => t.stop());
          return;
        }

        const ctx = new AudioContext();
        const source = ctx.createMediaStreamSource(stream);
        const analyser = ctx.createAnalyser();
        analyser.fftSize = fftSize;
        analyser.smoothingTimeConstant = smoothing;
        source.connect(analyser);

        const bufferLen = analyser.frequencyBinCount; // fftSize/2
        const data = new Uint8Array(bufferLen);
        const canvas = canvasRef.current!;
        const drawCtx = canvas.getContext('2d')!;

        const barCount = Math.floor(width / (barWidth + gap));

        const render = () => {
          analyser.getByteFrequencyData(data);
          drawCtx.clearRect(0, 0, width, height);

          for (let i = 0; i < barCount; i++) {
            const bin = Math.floor((i / barCount) * bufferLen);
            const magnitude = data[bin] / 255;        // 0..1
            const barHeight = magnitude * height;     // scale to canvas

            const x = i * (barWidth + gap);
            const y = (height - barHeight) / 2;

            drawCtx.fillStyle = color;
            drawCtx.fillRect(x, y, barWidth, barHeight);
          }

          state.current!.rafId = requestAnimationFrame(render);
        };

        state.current = { ctx, analyser, data, stream, rafId: 0 };
        render();
      } catch (err) {
        console.error('Mic access error', err);
      }
    })();

    return () => {
      mounted = false;
      cleanup();
    };
  }, [
    isListening,
    width,
    height,
    barWidth,
    gap,
    color,
    smoothing,
    fftSize,
  ]);

  return (
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        style={{ display: 'block', background: 'transparent' }}
      />
  );
};

export default BarVisualizer;
