import React from 'react';
import { Check, X, Trash2, Alert<PERSON>riangle } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { createPortal } from 'react-dom';

interface ClearChatHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

const ClearChatHistoryModal: React.FC<ClearChatHistoryModalProps> = ({ isOpen, onClose, onConfirm }) => {
  const { t } = useTranslation();

  if (!isOpen) {
    return null;
  }

  return createPortal(
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50 animate-fade-in"
      onClick={onClose}
    >
      <div className="bg-[#111727] rounded-2xl shadow-2xl max-w-md w-full mx-4 animate-scale-in border border-white/10">
        {/* Header */}
        <div className="p-6 pb-4">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-red-500/20 rounded-xl flex items-center justify-center">
              <Trash2 className="w-6 h-6 text-red-400" />
            </div>
            <div>
              <h2 className="text-xl font-medium text-white">{t('ClearChatTitle', 'Clear Chat History')}</h2>
            </div>
          </div>

          <p className="text-white/70 font-light mb-2">
            {t('ClearChatConfirm', 'Are you sure you want to clear the chat history?')}
          </p>

          <div className="flex items-center gap-2 p-3 bg-amber-500/10 rounded-lg border border-amber-500/20">
            <AlertTriangle className="w-4 h-4 text-amber-400 flex-shrink-0" />
            <p className="text-md text-amber-300 font-medium">
              {t('ClearChatIrreversible', 'This action cannot be undone.')}
            </p>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 pb-6">
          <div className="space-y-3">
            {/* Pro */}
            <div className="flex items-start gap-3 p-3 bg-green-500/10 rounded-lg border border-green-500/20">
              <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <Check className="w-3 h-3 text-white" strokeWidth={3} />
              </div>
              <div>
                <p className="text-md font-medium text-green-300 mb-1">{t('Advantage', 'Advantage')}</p>
                <p className="text-md text-light text-white/70">{t('ClearChatPro', 'Frees up tokens for better performance.')}</p>
              </div>
            </div>

            {/* Contra */}
            <div className="flex items-start gap-3 p-3 bg-red-500/10 rounded-lg border border-red-500/20">
              <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <X className="w-3 h-3 text-light text-white" strokeWidth={3} />
              </div>
              <div>
                <p className="text-md font-medium text-red-300 mb-1">{t('Disadvantage', 'Disadvantage')}</p>
                <p className="text-md text-light text-white/70">
                  {t(
                    'ClearChatCon',
                    'The AI will no longer remember past messages, but it will still have access to the code.',
                  )}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 pt-0">
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="flex-1 px-4 py-3 text-white/70 font-medium bg-white/5 hover:bg-white/10 rounded-xl transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white/20 border border-white/10"
            >
              {t('cancel', 'Cancel')}
            </button>
            <button
              onClick={onConfirm}
              className="flex-1 px-4 py-3 text-white font-medium bg-red-600 hover:bg-red-700 rounded-xl transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500/50 focus:ring-offset-2 focus:ring-offset-[#111727]"
            >
              {t('confirm', 'Confirm')}
            </button>
          </div>
        </div>
      </div>
    </div>,
    document.body,
  );
};

export default ClearChatHistoryModal;
