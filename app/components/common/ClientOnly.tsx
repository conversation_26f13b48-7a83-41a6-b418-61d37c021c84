import { useEffect, useState } from 'react';
interface ClientOnlyProps {
  children: React.ReactNode | (() => React.ReactNode);
  fallback?: React.ReactNode;
}

const ClientOnly: React.FC<ClientOnlyProps> = ({ children, fallback = null }) => {
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return <>{fallback}</>;
  }

  return <>{typeof children === 'function' ? children() : children}</>;
};
export default ClientOnly;
