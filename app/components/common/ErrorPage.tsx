import React from 'react';
import { useLocation } from 'react-router-dom';
import AccessDeinedPage from '~/routes/Access-Deined-Page';
import LoadFailurePage from '~/routes/Load-Failure-Page';
import PageNotFound from '~/routes/Not-Found-Page';

import '../styles/font.scss';

export function ErrorPage() {
  const location = useLocation();
  const [error, setError] = React.useState<any>(null);
  React.useEffect(() => {
    const state = location.state as { error?: any };

    if (state && state.error) {
      setError(state.error);
    } else {
      setError({ status: 500 });
    }
  }, [location]);

  if (error) {
    if (error.status === 404) {
      return <PageNotFound />;
    }

    if (error.status === 403) {
      return <AccessDeinedPage />;
    }

    if (error.status === 500) {
      return <LoadFailurePage />;
    }
  }

  return <LoadFailurePage />;
}

export default ErrorPage;
