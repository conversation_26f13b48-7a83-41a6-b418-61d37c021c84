import React from 'react';
import { Link, NavLink } from 'react-router-dom';
import styles from '../../ai/styles/BaseChat.module.scss';
import Logo from '/biela-logo-only.svg?url';
import '../styles/font.scss';
import { classNames } from '~/utils/classNames';
import ErrorPageIcon from '/hero-bg-shade-empty.png';
import ArrowLeftIcon from '../../assets/icons/arrow-left-go-icon.svg?url';
import { ErrorPageProps } from '~/components/types/componentTypes';
import { motion } from 'framer-motion';
import bielaText from '/biela-text.svg?url';

const ErrorUi: React.FC<ErrorPageProps> = ({ title, description, buttonText, buttonLink, children }) => {
  return (
    <div className="min-h-screen bg-auth bg-no-repeat bg-cover"
         style={{ backgroundImage: `url(${ErrorPageIcon})` }}
    >
      <div
        className="container px-[70px] mx-auto  top-0 w-full flex items-center justify-between max-sm:!pl-[40px] max-sm:px-[25px]">
        <Link to="/" className="flex items-center gap-2 sm:gap-3 group">
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            style={{
              transform: "none",
              height: "32px",
              minWidth: "40px",
            }}
            className="relative"
          >
            <img
              src={Logo}
              alt="BIELA"
              style={{
                position: "absolute",
                right: 0,
                top: "-7px",
              }}
              className="h-[80px] min-w-[80px] max-sm:min-w-[70px] max-sm:h-[70px]"
            />
            <div
              className="absolute inset-0 bg-accent/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300" />
          </motion.div>
          <img
            src={bielaText}
            alt="biela.dev"
            className="h-[60px] xl:h-[80px] w-[143px] max-sm:w-[100px]"
          />
        </Link>

        {/* MULTI LANGUAGE COMPONENT HERE */}
        <div className="">
          {/*sal*/}
        </div>
      </div>


      <div
        className=" flex items-center flex-col justify-center  "

      >

        <div className="flex flex-col items-center justify-center ">
          <div className="p-[48px] lg:w-[682px] flex flex-col items-center gap-[32px] justify-center text-center">
            {children}
            <p className={classNames(styles.RextonFont, 'text-[#ffffff] text-[49px] font-[500]')}>{title}</p>
            <p className={classNames( 'manrope text-[#f5f5f5] text-[20px] font-[400]')}>{description}</p>
            <NavLink to={buttonLink}>
              <button
                className="w-full bg-green-500 hover:bg-green-600 text-white py-3 px-5 rounded-lg transition-colors relative flex items-center font-thin justify-center gap-2">
                <img src={ArrowLeftIcon} alt="arrow" className="invert" />
                {buttonText}
              </button>
            </NavLink>
          </div>
        </div>
      </div>
    </div>
  )
    ;
};
export default ErrorUi;
