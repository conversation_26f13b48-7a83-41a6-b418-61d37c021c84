import React, { useEffect, useRef, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { AI_MODEL_STORAGE_KEY, AI_MODELS } from '~/utils/constants';

// Extended thinking modes
export type ExtendedThinkingMode = 'disabled' | 'first-response' | 'always';

interface ExtendedThinkingToggleProps {
  className?: string;
  isCompact?: boolean;
}

const ExtendedThinkingToggle: React.FC<ExtendedThinkingToggleProps> = ({ className = '', isCompact = false }) => {
  const { t } = useTranslation('translation');

  const [currentMode, setCurrentMode] = useState<ExtendedThinkingMode | undefined>(undefined);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedModelId, setSelectedModelId] = useState<string>('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  const currentModel = AI_MODELS.find((model) => model.value === selectedModelId);

  const extendedThinkingSupport = currentModel?.supportsExtendedThinking;

  const isAlwaysOn = extendedThinkingSupport === 'always' || extendedThinkingSupport === 'forced';
  const isUserToggleable = extendedThinkingSupport === true;
  const isAvailable = extendedThinkingSupport !== false;

  const isEnabled = isAlwaysOn || (isUserToggleable && currentMode !== 'disabled');

  // Load saved preference and current model on mount
  useEffect(() => {
    const savedMode = localStorage.getItem('extendedThinkingMode') as ExtendedThinkingMode | null;
    setCurrentMode(savedMode || 'first-response');

    const savedModelId = localStorage.getItem(AI_MODEL_STORAGE_KEY) || AI_MODELS[0].value;
    setSelectedModelId(savedModelId);

    // Setup event listener for model changes
    const handleModelChangeEvent = () => {
      const currentModelId = localStorage.getItem(AI_MODEL_STORAGE_KEY) || AI_MODELS[0].value;
      setSelectedModelId(currentModelId);

      // Get the model
      const model = AI_MODELS.find((m) => m.value === currentModelId);

      if (model?.supportsExtendedThinking === 'always' && currentMode !== 'always') {
        setCurrentMode('always');
        localStorage.setItem('extendedThinkingMode', 'always');
      } else if (model?.supportsExtendedThinking === 'forced') {
        setCurrentMode('always');
        localStorage.setItem('extendedThinkingMode', 'disabled');
      } else if (model?.supportsExtendedThinking === false && currentMode !== 'disabled') {
        setCurrentMode('disabled');
        localStorage.setItem('extendedThinkingMode', 'disabled');
      }
    };

    window.addEventListener('storage', handleModelChangeEvent);

    // Listen for custom model change events
    const handleCustomModelChange = (event: CustomEvent<{ modelValue: string; source?: string }>) => {
      if (event.detail?.source === 'ExtendedThinkingToggle') {
        return;
      }

      if (event.detail && event.detail.modelValue) {
        setSelectedModelId(event.detail.modelValue);

        const model = AI_MODELS.find((m) => m.value === event.detail.modelValue);

        if (model?.supportsExtendedThinking === 'always' && currentMode !== 'always') {
          setCurrentMode('always');
          localStorage.setItem('extendedThinkingMode', 'always');
        } else if (model?.supportsExtendedThinking === 'forced') {
          setCurrentMode('always');
          localStorage.setItem('extendedThinkingMode', 'disabled');
        } else if (model?.supportsExtendedThinking === false && currentMode !== 'disabled') {
          setCurrentMode('disabled');
          localStorage.setItem('extendedThinkingMode', 'disabled');
        }
      }
    };

    window.addEventListener('biela:changeAIModel', handleCustomModelChange as EventListener);

    return () => {
      window.removeEventListener('storage', handleModelChangeEvent);
      window.removeEventListener('biela:changeAIModel', handleCustomModelChange as EventListener);
    };
  }, [currentMode]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleEnabled = () => {
    if (!isUserToggleable) {
      return;
    }

    const newMode = isEnabled ? 'disabled' : 'first-response';
    setCurrentMode(newMode);
    localStorage.setItem('extendedThinkingMode', newMode);

    // If turning on, open the dropdown to show options
    if (newMode !== 'disabled') {
      setIsDropdownOpen(true);
    }
  };

  const handleModeChange = (newMode: ExtendedThinkingMode) => {
    if (!isUserToggleable) {
      return;
    }

    setCurrentMode(newMode);
    localStorage.setItem('extendedThinkingMode', newMode);
    setIsDropdownOpen(false);
  };

  const toggleSettings = () => {
    if (!isUserToggleable) {
      return;
    }

    setIsDropdownOpen(!isDropdownOpen);
  };

  if (currentMode === undefined) {
    return null;
  }

  const getTooltipMessage = () => {
    if (isAlwaysOn) {
      return t('ExtendedThinkingAlwaysOn', 'Always on with this model');
    }

    if (!isAvailable) {
      return t('ExtendedThinkingDisabledForModel', 'Not available with this model');
    }

    return t('extendedThinkingTooltip', 'Enable AI to think more deeply before responding');
  };

  return (
    <div className={`relative flex items-center ${className}`} ref={dropdownRef}>
      <div
        className={`flex items-center gap-2 ${isUserToggleable ? 'cursor-pointer group' : 'cursor-not-allowed'}`}
        onClick={toggleSettings}
      >
        <button
          className={`action-button flex items-center gap-2 p-1.5 rounded-md transition-all duration-200 relative group ${!isAvailable ? 'opacity-50' : ''} bg-transparent`}
        >
          <svg
            className="w-4 h-4 text-white/50 group-hover:text-white/70 transition-colors"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth={2}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"
            />
          </svg>
          {!isCompact && (
            <span className="text-white/70 text-sm whitespace-nowrap ml-1 mr-2">{t('extendedThinking')}</span>
          )}
          <span className="tooltip">{getTooltipMessage()}</span>
        </button>

        <button
          className={`relative inline-flex h-5 w-10 items-center rounded-full transition-colors focus:outline-none ${
            isEnabled ? 'bg-green-500' : 'bg-gray-600'
          } ${!isUserToggleable ? 'opacity-50 cursor-not-allowed' : ''}`}
          onClick={(e) => {
            e.stopPropagation();
            toggleEnabled();
          }}
          disabled={!isUserToggleable}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              isEnabled ? 'translate-x-5' : 'translate-x-1'
            }`}
          />
        </button>
      </div>

      <AnimatePresence>
        {isUserToggleable && isEnabled && isDropdownOpen && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.95 }}
            transition={{ duration: 0.1 }}
            className="absolute bottom-full mb-2 left-0 right-0 mx-auto bg-[#0A0F1C] rounded-lg shadow-lg border border-gray-800 overflow-hidden z-50"
            style={{ width: '240px', marginLeft: '-24px' }}
          >
            <div className="flex flex-col">
              <button
                onClick={() => handleModeChange('first-response')}
                className={`flex items-center justify-between w-full px-4 py-3 text-sm text-left transition-colors ${
                  currentMode === 'first-response'
                    ? 'bg-[#17223C] text-white'
                    : 'bg-[#0A0F1C] text-white/70 hover:bg-[#17223C]'
                }`}
              >
                <span>{t('firstResponseOnly')}</span>
                {currentMode === 'first-response' && (
                  <svg
                    className="w-4 h-4 text-green-500"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M5 12L10 17L19 8"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                )}
              </button>
              <button
                onClick={() => handleModeChange('always')}
                className={`flex items-center justify-between w-full px-4 py-3 text-sm text-left transition-colors ${
                  currentMode === 'always' ? 'bg-[#17223C] text-white' : 'bg-[#0A0F1C] text-white/70 hover:bg-[#17223C]'
                }`}
              >
                <span>{t('always')}</span>
                {currentMode === 'always' && (
                  <svg
                    className="w-4 h-4 text-green-500"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M5 12L10 17L19 8"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                )}
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ExtendedThinkingToggle;
