import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaReg<PERSON><PERSON><PERSON>, FaTelegram, FaTiktok, FaYoutube } from 'react-icons/fa';
import { FaXTwitter } from 'react-icons/fa6';
import { useTranslation } from 'react-i18next';
import BielaLogoSVG from '~/components/common/Footer/footerLogo';
import './pulse-animation.css';

interface CopyRightingProps {
  vertical?: boolean;
}

const CopyRighting: React.FC<CopyRightingProps> = ({ vertical = false }) => {
  const { t } = useTranslation('footer');

  const hexToRGB = (hex: string) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `${r}, ${g}, ${b}`;
  };

  const socialPlatforms = [
    {
      icon: <FaXTwitter />,
      name: 'X',
      url: 'https://x.com/DevBiela',
      color: '#ffffff',
      hoverEffect: 'hover:bg-[#FFFFFF19]',
    },
    {
      icon: <FaTelegram />,
      name: 'Telegram',
      url: 'https://t.me/teachmecode',
      color: '#0088cc',
      hoverEffect: 'hover:bg-[#0088CC19]',
    },
    {
      icon: <FaYoutube />,
      name: 'YouTube',
      url: 'https://youtube.com/@biela-dev',
      color: '#FF0000',
      hoverEffect: 'hover:bg-[#FF000019] ',
    },
    {
      icon: <FaDiscord />,
      name: 'Discord',
      url: 'https://discord.gg/A9VryKC8TQ',
      color: '#5865F2',
      hoverEffect: 'hover:bg-[#5865F219]',
    },
    {
      icon: <FaTiktok />,
      name: 'TikTok',
      url: 'https://tiktok.com/@biela.dev',
      color: '#ffffff',
      hoverEffect: 'hover:bg-[#FFFFFF19]',
    },
  ];
  const BASE_URL = 'https://biela.dev';

  const quickLinks = [
    { label: t('register', 'Register'), href: `https://affiliate.biela.dev/register/` },
    { label: t('bielaInAction', 'See Biela.dev in Action'), href: `${BASE_URL}/#demo` },
    { label: t('liveOnTelegram', 'We are Live on Telegram'), href: `${BASE_URL}/#telegram-community` },
    { label: t('affiliate', 'Affiliate Program'), href: `${BASE_URL}/#join-network` },
    { label: t('partners', 'Partners'), href: `${BASE_URL}/#partners` },
  ];

  return (
    <div className="flex flex-col bg-[#0a1730]">
      <footer className="py-6 sm:py-8 md:py-12 overflow-hidden border-t border-[#FFFFFF19] bg-[#0B0E1400] backdrop-blur-lg w-full">
        <div className="container mx-auto px-4 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 gap-6 md:gap-8 mb-6 md:mb-8">
            <div className="col-span-1 md:col-span-2 lg:col-span-3">
              <div className="flex w-56 sm:w-full max-w-[265px] items-center gap-3 mb-4">
                <BielaLogoSVG />
              </div>
              <div className="flex flex-wrap gap-3 sm:gap-4 mb-4 mt-6 sm:mt-[50px]">
                {socialPlatforms.map((platform) => (
                  <a
                    key={platform.name}
                    href={platform.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="relative"
                  >
                    <div
                      className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center ${platform.hoverEffect} transition-all duration-300 hover:scale-125`}
                      style={{ color: platform.color }}
                    >
                      <div className="text-lg sm:text-xl">{platform.icon}</div>
                    </div>
                    <div
                      className="absolute inset-0 rounded-full"
                      style={{
                        border: `0px solid ${platform.color}`,
                        boxShadow: `0px 0px 5px ${platform.color}`,
                      }}
                    />
                  </a>
                ))}
              </div>
            </div>

            <div className="col-span-1 md:col-span-2 lg:col-span-2">
              <h3 className="text-base sm:text-lg font-light mb-3 sm:mb-4 text-white">{t('title', 'Quick Links')}</h3>
              <ul className="space-y-2 sm:space-y-3">
                {quickLinks.map((link, index) => (
                  <li key={index}>
                    <a
                      href={link.href}
                      target={'_blank'}
                      className="text-sm sm:text-base text-[#9CA3AF] hover:text-white transition-colors font-normal"
                    >
                      {link.label}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            <div className="col-span-1 md:col-span-2 lg:col-span-7">
              <h3 className="text-base sm:text-lg font-light text-white mb-3 sm:mb-4">{t('legal', 'Legal')}</h3>
              <div className="space-y-3 sm:space-y-4">
                <div className="relative p-3 sm:p-4 border border-[#FFFFFF0C] rounded-xl backdrop-blur-lg bg-gradient-to-br from-[#1A1D2E7F] to-[#1A1D2E33] transition-all duration-300 hover:border-accent/20 hover:from-[#1A1D2E99] hover:to-[#1A1D2E4C]">
                  <p className="text-xs sm:text-sm text-[#D1D5DB] mb-2">
                    {t('copyright', '© 2025 TeachMeCode Institute. All rights reserved.')}
                  </p>
                  <p className="text-xs text-[#9CA3AF]">
                    {t(
                      'unauthorized',
                      'Unauthorized use, reproduction, or distribution of this service, in whole or in part, without explicit written permission is strictly prohibited.',
                    )}
                  </p>
                </div>
                <p className="text-[12px] sm:text-xs text-[#6B7280]">
                  {t(
                    'reservations',
                    'TeachMeCode Institute reserves all legal rights to the intellectual property associated with Biela.',
                  )}
                </p>
                <div className="space-x-2 mt-4 flex items-center">
                  <a
                    href={`${BASE_URL}/terms`}
                    target={'_blank'}
                    className="text-[#9CA3AF] font-light hover:text-white transition-colors"
                  >
                    {t('termsOfService', 'Terms of Service')}
                  </a>
                  <span className="text-[#9CA3AF] text-xs">•</span>
                  <a
                    href={`${BASE_URL}/privacy`}
                    target={'_blank'}
                    className="text-[#9CA3AF] font-light hover:text-white transition-colors"
                  >
                    {t('privacyPolicy', 'Privacy Policy')}
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t border-[#FFFFFF0C] pt-4 flex flex-col sm:flex-row justify-between items-center gap-3 sm:gap-0">
            <div className="flex items-center gap-2 text-[12px] sm:text-xs text-[#6B7280]">
              <FaRegCopyright />
              <span
                dangerouslySetInnerHTML={{
                  __html: t('left', '2025 Biela.dev. Powered by {{institute}} © All rights reserved.', {
                    defaultValue:
                      '2025 Biela.dev. Powered by <a href="https://teachmecode.ae" target="_blank" class="hover:text-white transition-colors">TeachMeCode Institute (TMC)</a> © All rights reserved.',
                    institute:
                      '<a href="https://teachmecode.ae" target="_blank" class="hover:text-white transition-colors">TeachMeCode Institute (TMC)</a>',
                  }),
                }}
              />
            </div>

            <div className="text-[12px] sm:text-xs text-[#6B7280] flex items-center gap-2">
              <FaCode className="text-[#22C55E]" />
              <span>
                {t('right', 'Developed by {{company}}', {
                  defaultValue: 'Developed by TitanLabs',
                  company: 'TitanLabs',
                })}
              </span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default CopyRighting;
