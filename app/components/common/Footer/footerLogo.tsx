import React from 'react';
import { motion } from 'framer-motion';

const <PERSON><PERSON><PERSON><PERSON>ogoSVG = () => (
  <motion.div
    initial={{opacity: 0, y: 10}}
    animate={{opacity: 1, y: 0}}
    transition={{duration: 0.4}}
    className="flex items-center gap-3 mb-4 embossed-logo"
  >
    <svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 812.37 324.22">

      <g id="logo-under-text-bold_-_UPDATED" data-name="logo-under-text-bold - UPDATED">
        <g>
          <path className="cls-1"
                d="M404.67,125.34v-44.26h4.2v10.51c0,1.12.91,2.04,2.04,2.04h29.81c9.18,0,15.1,6.26,15.1,15.96s-5.79,15.76-15.1,15.76h-36.05ZM410.91,97.83c-1.12,0-2.04.91-2.04,2.04v19.24c0,1.12.91,2.04,2.04,2.04h29.81c6.52,0,10.9-4.62,10.9-11.49s-4.48-11.82-10.9-11.82h-29.81Z"/>
          <path className="cls-1"
                d="M467.84,125.34v-31.79h4.2v31.79h-4.2ZM467.84,85.35v-4.27h4.2v4.27h-4.2Z"/>
          <path className="cls-1"
                d="M499.23,125.34c-9.32,0-15.11-6.04-15.11-15.76s5.93-15.96,15.11-15.96h20.95c9.14,0,15.04,6.26,15.04,15.96v2.04h-44.13c-.66,0-1.29.32-1.67.87-.38.54-.47,1.24-.24,1.87,1.55,4.25,5.31,6.79,10.05,6.79h36.05v4.2h-36.05ZM499.23,97.83c-4.75,0-8.41,2.49-10.05,6.83-.24.63-.15,1.33.23,1.88s1.01.88,1.67.88h37.24c.62,0,1.21-.28,1.6-.77.39-.49.53-1.12.39-1.73-1.01-4.3-4.99-7.09-10.12-7.09h-20.95Z"/>
          <rect className="cls-1" x="547.89" y="81.08" width="4.2" height="44.26"/>
          <path className="cls-1"
                d="M577.9,125.34c-5.13,0-13.73-1.19-13.73-9.13,0-7.33,7.46-8.87,13.73-8.87h23.71c3.09,0,9.52,0,9.52-4.79s-6.19-4.79-9.52-4.79h-35.33v-4.2h35.33c9.11,0,13.73,3.03,13.73,9,0,.18.02.35.07.52v22.27h-37.5ZM577.9,111.55c-3.33,0-9.52,0-9.52,4.79s6.43,4.79,9.52,4.79h31.26c1.12,0,2.04-.91,2.04-2.04v-6.5c0-.64-.31-1.25-.82-1.63-.35-.26-.78-.4-1.21-.4-.2,0-.4.03-.59.09-1.99.6-4.27.9-6.96.9h-23.71Z"/>
          <rect className="cls-1" x="627.21" y="121.14" width="4.21" height="4.2"/>
          <path className="cls-1"
                d="M657.16,125.34c-9.32,0-15.1-6.04-15.1-15.76s5.93-15.96,15.1-15.96h29.82c1.12,0,2.04-.91,2.04-2.04v-10.51h4.2v44.26h-36.05ZM657.09,97.83c-6.38,0-10.83,4.86-10.83,11.82s4.35,11.49,10.83,11.49h29.82c1.12,0,2.04-.91,2.04-2.04v-19.24c0-1.12-.91-2.04-2.04-2.04h-29.82Z"/>
          <path className="cls-1"
                d="M720.33,125.34c-9.32,0-15.1-6.04-15.1-15.76s5.93-15.96,15.1-15.96h20.95c9.14,0,15.04,6.26,15.04,15.96v2.04h-44.13c-.66,0-1.29.32-1.67.87-.38.54-.47,1.24-.24,1.87,1.55,4.25,5.31,6.79,10.06,6.79h36.05v4.2h-36.05ZM720.33,97.83c-4.74,0-8.41,2.49-10.05,6.83-.24.63-.15,1.33.23,1.88.38.55,1.01.88,1.67.88h37.24c.62,0,1.21-.28,1.59-.77.39-.49.53-1.12.39-1.73-1.01-4.3-4.99-7.09-10.12-7.09h-20.95Z"/>
          <path className="cls-1"
                d="M784.42,125.34l-20.49-31.79h4.88l17.79,27.25c.38.58,1.02.92,1.7.92h0c.69,0,1.33-.36,1.71-.94l17.39-27.23h4.96l-20.26,31.79h-7.69Z"/>
        </g>
        <g id="Layer_8_copy_3" data-name="Layer 8 copy 3">
          <g id="Layer_4_copy_6" data-name="Layer 4 copy 6">
            <path className="cls-1"
                  d="M329.93,230.89c-.02.08-.05.17-.09.25-.35.82-8.89,20.42-30.57,40.4-12.72,11.72-27.33,21.1-43.43,27.88-20.1,8.46-42.58,12.86-66.81,13.07-1.08,0-1.96-.86-1.97-1.94-.01-1.08.86-1.96,1.94-1.97,53.59-.48,88.04-21.9,107.5-39.79,21.12-19.42,29.67-39,29.76-39.19.43-.99,1.57-1.45,2.56-1.03.91.39,1.37,1.39,1.11,2.31Z"/>
            <path className="cls-1"
                  d="M174.55,309.23c-.24.88-1.09,1.5-2.04,1.42-13.43-1.07-26.35-5.86-26.89-6.06-1.01-.38-1.52-1.5-1.14-2.51.38-1.01,1.5-1.52,2.51-1.15.13.05,12.96,4.8,25.83,5.83,1.07.09,1.88,1.03,1.79,2.1,0,.13-.03.25-.06.37Z"/>
            <path className="cls-1"
                  d="M342.38,199.38c-2.92,10.56-6.57,18.81-6.75,19.2-.44.99-1.59,1.43-2.58.99-.99-.44-1.43-1.59-.99-2.58.05-.11,4.78-10.82,7.7-23.13.24-1.06,1.3-1.7,2.35-1.45s1.7,1.3,1.45,2.35c-.37,1.58-.77,3.12-1.19,4.62Z"/>
            <path className="cls-1"
                  d="M260.32,22.69c-.05.18-.13.36-.23.53-.58.91-1.78,1.18-2.69.61-.09-.05-4.3-2.61-17.29-6.71-1.03-.32-1.59-1.43-1.27-2.45.32-1.03,1.42-1.6,2.45-1.27,13.69,4.33,18.02,7.02,18.2,7.13.74.47,1.06,1.36.84,2.17Z"/>
            <path className="cls-1"
                  d="M47.56,186.28c-.2.71-.79,1.28-1.56,1.41-1.06.18-2.07-.54-2.25-1.6-4.75-28.45-.72-52.9,3.5-68.41,4.58-16.83,10.23-26.81,10.47-27.23.54-.94,1.73-1.26,2.66-.73.94.54,1.26,1.73.73,2.66-.08.14-5.68,10.09-10.12,26.45-4.1,15.1-8.01,38.91-3.38,66.6.05.29.03.58-.04.84Z"/>
            <path className="cls-1"
                  d="M226.73,11.63c-.28,1.01-1.31,1.62-2.33,1.38-.29-.07-24.37-5.61-55.3-.41-28.49,4.79-68.82,19.94-99.2,64.98-.6.89-1.82,1.13-2.71.53s-1.13-1.82-.53-2.71c13.77-20.41,30.68-36.59,50.26-48.1,15.67-9.21,33.06-15.46,51.69-18.57,31.71-5.3,55.69.25,56.69.49,1.05.25,1.7,1.3,1.45,2.35,0,.02-.01.05-.02.07Z"/>
            <path className="cls-1"
                  d="M302.83,37.04c12.19,5.09,20.14,18,20.37,30.76.33,6.95-2.97,12.81-6.31,19.1-1.08,3.84,2.29,8.76,6.53,7.83,10.45-2.12,21.32-.97,29.43,6.22,9.85,8.83,14.32,24.34,10.41,37.62-7,25.9-34.6,39.47-57.71,52.33,0,0,0,0,0,0-.34.29-.72.56-1.13.8l-65.44,37.07c-3.28,1.86-7.45.71-9.31-2.58-1.86-3.28-.71-7.45,2.58-9.32l57.39-32.52h0c16.6-10.29,41.8-20.37,52.31-36.77,9.47-13.24,3.32-33.06-11.18-39.4-12.59-5.5-26.45.75-38.61,7.78-5.51,2.4-15.86,12.48-20.78,3.97-4.57-8.28,8.02-11.81,12.9-15.13,11.8-6.97,18.06-11.63,21.54-22.33,3.15-11.54-3.53-25.2-14.07-30.55-12.22-6.26-26.48.96-38.42,7.8,0,0-.02.01-.03.02-.32.18-.64.37-.96.55h0s-3.31,1.88-3.31,1.88l-30.97,17.55c-3.29,1.86-7.46.7-9.32-2.58-1.86-3.29-.71-7.46,2.58-9.32l34.97-19.82c.23-.13.46-.24.69-.34,18.21-10.42,36.9-18.7,55.86-10.65Z"/>
            <path className="cls-1" d="M252.32,60.31c-1.12.65-2.22,1.29-3.29,1.91l-.02-.03,3.31-1.88Z"/>
            <line className="cls-1" x1="261.72" y1="165.37" x2="76.09" y2="270.53"/>
            <path className="cls-1"
                  d="M262.45,166.65l-183.47,108.97c-2.78,1.65-6.38.74-8.03-2.05-1.7-2.85-.67-6.58,2.25-8.15,0,0,187.78-101.35,187.78-101.35.72-.39,1.62-.12,2,.6.38.7.13,1.57-.55,1.97h0Z"/>
            <line className="cls-1" x1="164.74" y1="145.36" x2="45.46" y2="212.93"/>
            <path className="cls-1"
                  d="M165.43,146.58l-117.08,71.45c-2.76,1.69-6.37.81-8.06-1.95-1.69-2.76-.81-6.37,1.95-8.06.02-.07,121.67-63.79,121.81-63.89,1.52-.77,2.95,1.4,1.39,2.44h0Z"/>
            <line className="cls-1" x1="215.38" y1="155.59" x2="5.95" y2="274.24"/>
            <path className="cls-1"
                  d="M216.14,156.92L8.84,279.33c-2.79,1.65-6.38.72-8.02-2.07-1.69-2.85-.66-6.55,2.25-8.13,0,0,211.57-114.88,211.57-114.88.74-.4,1.67-.13,2.07.61.39.73.14,1.63-.57,2.05h0Z"/>
            <path className="cls-1" d="M253.28,59.76c-.09.06-.18.11-.27.16l-.69.39c.32-.18.64-.37.96-.55Z"/>
            <path className="cls-1"
                  d="M223.64,236.33l-100.42,52.42c-2.21,1.1-4.07-2.01-1.94-3.42,0,0,96.58-59.19,96.58-59.19,2.76-1.69,6.37-.83,8.06,1.93,1.78,2.87.71,6.71-2.29,8.26h0Z"/>
            <path className="cls-1"
                  d="M201.42,87.6l-69.36,34.82c-.97.49-2.15.1-2.64-.87-.46-.91-.13-2.01.7-2.54l65.52-41.59c2.73-1.74,6.36-.93,8.1,1.81,1.74,2.73.93,6.36-1.81,8.1-.17.1-.34.2-.51.29,0,0,.17-.09,0,0Z"/>
            <path className="cls-1"
                  d="M113.91,7.22h0C110.79,6.35,108.96,3.12,109.82,0h0c-.86,3.12-4.1,4.95-7.22,4.09h0c3.12.86,4.95,4.1,4.09,7.22h0c.86-3.12,4.1-4.95,7.22-4.09Z"/>
            <path className="cls-1"
                  d="M165.35,57.35h0c-4.29-1.19-6.8-5.63-5.62-9.92h0c-1.19,4.29-5.63,6.8-9.92,5.62h0c4.29,1.19,6.8,5.63,5.62,9.92h0c1.19-4.29,5.63-6.8,9.92-5.62Z"/>
            <path className="cls-1"
                  d="M93.36,119.92h0c-4.29-1.19-6.8-5.63-5.62-9.92h0c-1.19,4.29-5.63,6.8-9.92,5.62h0c4.29,1.19,6.8,5.63,5.62,9.92h0c1.19-4.29,5.63-6.8,9.92-5.62Z"/>
            <path className="cls-1"
                  d="M174.09,288.71h0c-3.09-.85-4.9-4.05-4.05-7.14h0c-.85,3.09-4.05,4.9-7.14,4.05h0c3.09.85,4.9,4.05,4.05,7.14h0c.85-3.09,4.05-4.9,7.14-4.05Z"/>
            <path className="cls-1"
                  d="M241.79,277.21h0c-5.45-1.51-8.64-7.15-7.14-12.6h0c-1.51,5.45-7.15,8.64-12.6,7.14h0c5.45,1.51,8.64,7.15,7.14,12.6h0c1.51-5.45,7.15-8.64,12.6-7.14Z"/>
            <path className="cls-1"
                  d="M303.27,225.45h0c-4.38-1.21-6.94-5.74-5.73-10.12h0c-1.21,4.38-5.74,6.94-10.12,5.73h0c4.38,1.21,6.94,5.74,5.73,10.12h0c1.21-4.38,5.74-6.94,10.12-5.73Z"/>
            <path className="cls-1"
                  d="M291.91,317.85h0c-4.87-1.35-7.72-6.38-6.37-11.25h0c-1.35,4.87-6.38,7.72-11.25,6.37h0c4.87,1.35,7.72,6.38,6.37,11.25h0c1.35-4.87,6.38-7.72,11.25-6.37Z"/>
            <path className="cls-1"
                  d="M85.51,251.25h0c-2.09-.58-3.32-2.75-2.74-4.84h0c-.58,2.09-2.75,3.32-4.84,2.74h0c2.09.58,3.32,2.75,2.74,4.84h0c.58-2.09,2.75-3.32,4.84-2.74Z"/>
            <path className="cls-1"
                  d="M41.31,236.27h0c-2-.55-3.18-2.63-2.62-4.63h0c-.55,2-2.63,3.18-4.63,2.62h0c2,.55,3.18,2.63,2.62,4.63h0c.55-2,2.63-3.18,4.63-2.62Z"/>
          </g>
        </g>
      </g>
    </svg>

  </motion.div>

);

export default BielaLogoSVG;
