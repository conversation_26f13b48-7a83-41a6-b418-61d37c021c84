import { useNavigate } from 'react-router-dom';
import { ReactNode } from 'react';

const ListItem = ({
  icon,
  title,
  description,
  path,
  isExternal = false,
  openInNewTab = false, // ✅ New prop
}: {
  icon: ReactNode;
  title: string;
  description: string;
  path: string;
  isExternal?: boolean;
  openInNewTab?: boolean; // ✅ Include in types
}) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (isExternal || openInNewTab) {
      window.open(path, '_blank', 'noopener,noreferrer');
    } else {
      navigate(path);
    }
  };

  if (isExternal) {
    return (
      <a
        href={path}
        target="_blank"
        rel="noopener noreferrer"
        className="border border-transparent py-3 sm:p-3 cursor-pointer relative rounded-lg flex hover:bg-[#1F293780] items-center w-full text-black dark:text-white mt-[4px] group"
      >
        <div className="border-container-list">
          <div className="light-border"></div>
        </div>
        <div className="flex gap-2 items-center">
          <div className="flex items-center p-2 bg-[#1F293780] group-hover:bg-[#37415180] transition-colors rounded-lg h-fit">
            {icon}
          </div>
          <div className="flex flex-col gap-[4px]">
            <span className="ml-2 text-[16px] font-light font-manrope">{title}</span>
            <span className="ml-2 text-[14px] font-light font-manrope text-[#9CA3AF]">{description}</span>
          </div>
        </div>
      </a>
    );
  }

  return (
    <li
      onClick={handleClick}
      className="border border-transparent py-3 sm:p-3 cursor-pointer relative rounded-lg flex hover:bg-[#1F293780] items-center w-full text-black dark:text-white mt-[4px] group"
    >
      <div className="border-container-list">
        <div className="light-border"></div>
      </div>
      <div className="flex gap-2 items-center">
        <div className="flex items-center p-2 bg-[#1F293780] group-hover:bg-[#37415180] transition-colors rounded-lg h-fit">
          {icon}
        </div>
        <div className="flex flex-col gap-[4px]">
          <span className="ml-2 text-[16px] font-light font-manrope">{title}</span>
          <span className="ml-2 text-[14px] font-light font-manrope text-[#9CA3AF]">{description}</span>
        </div>
      </div>
    </li>
  );
};

export default ListItem;
