import React from 'react';
import { ClockIcon, ListBulletIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';
import WithTooltip from '~/ai/components/Tooltip';
import { ChatCodeSwitch } from '~/ai/components/ChatCodeSwitch';

interface TabsSectionProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const TabsSection: React.FC<TabsSectionProps> = ({ activeTab, setActiveTab }) => {
  return (
    <div className="w-full bg-[#0A0F1C] z-1 tab-section-chat max-[800px]:pr-[55px]">
      <div className="show-prompt border border-white/5 rounded-t-lg p-2 z-1 min-[800px]:max-w-[var(--chat-min-width)] bg-[#121724]">
        <div className="grid grid-cols-4 gap-1">
          {/* Replacing "chat" and "code" tabs with your custom switch */}
          <div className="col-span-2">
            <ChatCodeSwitch
              disableLayoutAnimation={true}
              activeTab={activeTab}
              changeTabToChat={()=>{
              setActiveTab('chat')
            }} />
          </div>

          {/* Keeping "History" tab */}
          <button
            onClick={() => setActiveTab('history')}
            className={clsx(
              'flex items-center col-span-1 justify-center w-auto gap-2 px-3 py-1.5 text-sm rounded-md transition-colors',
              activeTab === 'history' ? 'bg-[#A78BFA] text-black' : 'text-white/70 bg-transparent hover:bg-white/5'
            )}
          >
            <ClockIcon className="w-4 h-4" />
            <span className="text-[15px] font-normal tracking-[0.4px]">History</span>
          </button>

          {/* Roadmap with tooltip */}
          <WithTooltip
            className={'text-[13px] font-normal tracking-[0.4px]'}
            tooltip={'Coming Soon'}
          >
            <button
              className={clsx(
                'flex items-center col-span-1 w-full justify-center gap-2 px-3 py-1.5 rounded-md transition-colors text-sm text-white/70 bg-transparent hover:bg-white/5'
              )}
            >
              <ListBulletIcon className="w-4 h-4" />
              <span className="text-[15px] font-normal tracking-[0.4px]">Roadmap</span>
            </button>
          </WithTooltip>
        </div>
      </div>
    </div>
  );
};

export default TabsSection;
