import { CloseButtonProps } from '~/components/types/componentTypes';
import React from 'react';
import { setProjectImportName } from '~/ai/lib/stores/chat';
import { FaTimes } from 'react-icons/fa';

const CloseButton: React.FC<CloseButtonProps> = ({ closeModal }) => {
  return (
    <div
      onClick={(e) => {
        setProjectImportName('');
        e.stopPropagation();
        closeModal();
      }}
      className="w-[48px] h-[48px] flex items-center justify-center bg-transparent border border-[#2E2E2E] rounded-full cursor-pointer hover:bg-[#2E2E2E] hover:border-[#ffffff] transition-all"
    >
      <button className="flex items-center justify-center bg-transparent">
        <FaTimes />
      </button>
    </div>
  );
};

export default CloseButton;
