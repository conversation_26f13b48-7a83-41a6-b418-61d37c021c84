
.aiSelector_optionDisabled:hover::before {
    background-color: rgba(15, 23, 42, 0.25);
}

.aiSelector_full {
    display: flex;
    align-items: center;
    margin-left: 1.5rem;
}

.aiSelector_label {
    font-size: 15px;
    color: rgba(255, 255, 255, 0.7);
    margin-right: 0.5rem;
}

.aiSelector_buttonContainer {
    position: relative;
}

.aiSelector_button {
    display: flex;
    align-items: center;
}

.aiSelector_buttonCompact {
    padding: 0.375rem;
    border-radius: 0.375rem;
    transition: all 0.2s;
    position: relative;
    background-color: transparent;
}

.aiSelector_buttonFull {
    height: 38px;
    padding: 0 1rem;
    background-color: #1A243B;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem;
    transition: colors 0.2s;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.aiSelector_buttonFull:hover {
    background-color: #1E2A45;
}

.aiSelector_iconWrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    flex-shrink: 0;
}

.aiSelector_iconCompact {
    width: 1rem;
    height: 1rem;
}

.aiSelector_iconFull {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.5rem;
}

.aiSelector_icon {
    position: absolute;
    top: 48%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.1rem;
    line-height: 1;
}

.aiSelector_iconCompactDefault {
    color: rgba(255, 255, 255, 0.5);
    transition: colors 0.2s;
}

.aiSelector_iconCompactHover:hover {
    color: rgba(255, 255, 255, 0.7);
}

.aiSelector_name {
    font-size: 0.875rem;
    color: white;
    white-space: nowrap;
}

.aiSelector_arrow {
    color: rgba(255, 255, 255, 0.5);
    margin-left: 0.375rem;
    font-size: 0.75rem;
    flex-shrink: 0;
}

.aiSelector_dropdown {
    position: absolute;
    z-index: 50;
    width: 420px;
    background-image: linear-gradient(to bottom, #131B2E, #0A1020);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    overflow: hidden;
}

.aiSelector_dropdownBelow {
    left: 0;
    margin-top: 0.5rem;
}

.aiSelector_dropdownAbove {
    left: 0;
    bottom: calc(100% + 0.5rem);
}

.aiSelector_scrollArea {
    padding: 1rem;
    max-height: 250px;
    overflow-y: auto;
    width: 100%;
}

.aiSelector_optionCard {
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.2s;
    border-radius: 0.375rem;
    position: relative;
    border: 1px solid rgba(31, 45, 80, 0.4);
    background-color: #131B2E;
    min-width: 0;
    max-width: 100%;
    width: 100%;
    box-sizing: border-box;
}

.aiSelector_optionCard:last-child {
    margin-bottom: 0;
}

.aiSelector_optionCard:hover {
    background-color: rgba(26, 36, 59, 0.7);
}

.aiSelector_optionSelected {
    background-color: #1A243B;
    border-color: #1F2D50;
    border-left-width: 3px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.aiSelector_optionDefault {
    border-left-width: 1px;
}

.aiSelector_optionDisabled {
    position: relative;
    overflow: hidden;
    cursor: not-allowed;
    opacity: 0.85;
}

.aiSelector_optionDisabled::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(15, 23, 42, 0.3);
    background-image: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(0, 0, 0, 0.1) 10px,
        rgba(0, 0, 0, 0.1) 20px
    );
    z-index: 1;
    pointer-events: none;
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;
}

.aiSelector_optionDisabled .aiSelector_modelIconInner {
    opacity: 1 !important;
}

.aiSelector_optionDisabled .aiSelector_modelIcon {
    opacity: 1 !important;
    position: relative;
    z-index: 2;
}

.aiSelector_optionDisabled .aiSelector_premiumRibbon,
.aiSelector_optionDisabled .aiSelector_premiumLabel,
.aiSelector_optionDisabled .aiSelector_premiumIcon,
.aiSelector_optionDisabled .aiSelector_premiumCorner {
    opacity: 1 !important;
    z-index: 10 !important;
}

.aiSelector_optionDisabled .aiSelector_statValue {
    opacity: 1 !important;
    position: relative;
    z-index: 2;
}

.aiSelector_optionDisabled .aiSelector_modelName,
.aiSelector_optionDisabled .aiSelector_lockIcon {
    opacity: 1 !important;
    position: relative;
    z-index: 2;
}

.aiSelector_optionDisabled .aiSelector_upgradeTooltip {
    opacity: 1 !important;
    position: relative;
    z-index: 2;
}

.aiSelector_optionDisabled .aiSelector_upgradeTooltip {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.aiSelector_optionDisabled:hover::before {
    background-color: rgba(15, 23, 42, 0.35);
}

.aiSelector_premiumCorner {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10;
    overflow: hidden;
    width: 120px;
    height: 120px;
    pointer-events: none;
}

.aiSelector_premiumRibbon {
    position: absolute;
    width: 150px;
    top: 20px;
    right: -35px;
    transform: rotate(45deg);
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    background: linear-gradient(to right, var(--locked-badge-color), var(--locked-badge-darker, var(--locked-badge-color)));
}

.aiSelector_premiumLabel {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #0F172A;
    font-size: 11px;
    font-weight: 700;
    letter-spacing: 0.8px;
    padding: 4px 0;
    white-space: nowrap;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.1);
}

.aiSelector_premiumIcon {
    width: 12px;
    height: 12px;
    margin-right: 4px;
    stroke-width: 2;
}

.aiSelector_optionHeader {
    display: flex;
    align-items: center;
}

.aiSelector_modelIcon {
    flex-shrink: 0;
    width: 2.25rem;
    height: 2.25rem;
    border-radius: 9999px;
    margin-right: 0.75rem;
    position: relative;
}

.aiSelector_modelIconInner {
    position: absolute;
    top: 48%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.4rem;
    line-height: 1;
}

.aiSelector_modelInfo {
    flex-grow: 1;
    min-width: 0;
}

.aiSelector_modelHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.aiSelector_modelName {
    color: white;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.aiSelector_lockIcon {
    font-size: 14px;
    margin-left: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.3));
}

.aiSelector_activeTag {
    font-size: 0.75rem;
    padding: 0 0.5rem;
    border-radius: 9999px;
}

.aiSelector_modelDesc {
    color: rgba(255, 255, 255, 0.6);
    font-size: 13px;
    margin-top: 0.125rem;
    position: relative;
    min-height: 40px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.aiSelector_upgradeTooltip {
    display: flex;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;
    padding: 8px 10px;
    border: 1px solid;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 0.75rem;
}

.aiSelector_upgradeTooltip span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-shrink: 1;
}

.aiSelector_premiumUpgradeTooltip {
    width: 100%;
    min-width: 0;
    box-sizing: border-box;
    display: flex;
    align-items: center;
}

.aiSelector_upgradeIcon {
    display: flex;
    align-items: center;
    margin-right: 6px;
}

.aiSelector_packageHighlight {
    font-weight: 700;
    display: inline-block;
    max-width: 130px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.aiSelector_featuresGrid {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1rem;
}

.aiSelector_sectionTitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.aiSelector_featureList {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.aiSelector_featureItem {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 13px;
}

.aiSelector_featureDot {
    margin-right: 0.5rem;
    width: 0.375rem;
    height: 0.375rem;
    border-radius: 9999px;
    flex-shrink: 0;
}

.aiSelector_statsContainer {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.aiSelector_statItem {
    display: flex;
    align-items: center;
    font-size: 13px;
}

.aiSelector_statLabel {
    color: rgba(255, 255, 255, 0.5);
    margin-right: 0.5rem;
}

.aiSelector_flexRow {
    display: flex;
    align-items: center;
}