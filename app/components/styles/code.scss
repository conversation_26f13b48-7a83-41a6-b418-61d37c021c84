.actions .shiki {
  background-color: #1A1F2E !important;
  padding: 8px;
  font-family: "Fira Code", 'sans-serif';
  font-weight: 300;
  font-size: 12px;
  transition: all .3s ease-in-out;
}
.run-command .shiki:hover {
  cursor: pointer;
  background-color: #20263c !important;
  transition: all .3s ease-in-out;
}
.shiki .line {
  span {
    &:nth-of-type(2) {
      color: #42E3FF !important;
    }

    &:nth-of-type(3) {
      color: #42E3FF !important;
    }

    &:first-of-type {
      color: #FFE873 !important;
    }
  }
}

.shiki {
  &:not(:has(.actions), .actions *) {
    background-color: var(--biela-elements-messages-code-background) !important;
  }
}
