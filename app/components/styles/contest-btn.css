@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base button styles with shared properties */
.project-action {
  @apply flex items-center rounded-lg transition-all duration-300 relative hover:scale-105;
}

/* Primary action - vibrant gradient with glow effect */
.project-action-primary {
  @apply project-action bg-gradient-to-r from-biela-green to-biela-hover text-white py-1.5 px-3 font-normal shadow-md
    hover:shadow-lg hover:from-biela-hover hover:to-biela-green hover:scale-105;
}

/* Download action - blue with subtle pattern */
.project-action-download {
  @apply project-action bg-[#0B1931] text-white py-1.5 px-2.5 shadow-md
    hover:bg-[#152A4A] hover:shadow-lg hover:scale-105;
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%,
                    transparent 50%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.05) 75%,
                    transparent 75%, transparent);
  background-size: 8px 8px;
}

/* Secondary actions - subtle glass-like effect */
.project-action-secondary {
  @apply project-action bg-app-hover/20 text-app-icon-default p-1.5 border border-app-hover/30 backdrop-blur-sm
    hover:bg-app-hover/40 hover:text-app-icon-hover hover:border-app-hover hover:scale-105;
}

/* Warning action - amber styling */
.project-action-warning {
  @apply project-action bg-app-icon-warning/10 text-app-icon-warning p-1.5 border border-app-icon-warning/30
    hover:bg-app-icon-warning/20 hover:border-app-icon-warning hover:scale-105;
}

/* Danger action - red with pulsing effect on hover */
.project-action-danger {
  @apply project-action bg-app-icon-danger/10 text-app-icon-danger p-1.5 border border-app-icon-danger/30
    hover:bg-app-icon-danger/20 hover:border-app-icon-danger hover:scale-105;
}

/* Contest publish button - purple gradient styling */
.project-action-contest {
  @apply project-action bg-gradient-to-r from-purple-600 to-indigo-700 text-white rounded-lg py-1.5 px-3 font-normal shadow-md
    hover:shadow-lg hover:from-indigo-700 hover:to-purple-600 hover:scale-105;
}

/* Info button - subtle blue styling */
.project-action-info {
  @apply project-action bg-blue-500/10 text-blue-400 rounded-lg p-1.5 border border-blue-500/30
    hover:bg-blue-500/20 hover:border-blue-400 hover:scale-105;
}

/* Button group styling */
.action-group {
  @apply flex items-center space-x-1 px-2.5 py-0.5 bg-app-hover/20 rounded-lg border border-app-hover/30;
}

/* Tooltip styling with enhanced appearance */
.custom-tooltip {
  @apply absolute bottom-full left-1/2 transform -translate-x-1/2 mb-3 px-3 py-2
    bg-gray-800/90 backdrop-blur-sm text-white text-xs font-light rounded-md shadow-lg
    opacity-0 group-hover:opacity-100 transition-all duration-300 -translate-y-1
    group-hover:translate-y-0 z-10 whitespace-nowrap;
}

.custom-tooltip:after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: rgba(31, 41, 55, 0.9) transparent transparent transparent;
}

/* Project card enhanced styling */
.project-card {
  @apply bg-gradient-to-b from-app-card to-app-card/95 rounded-lg shadow-md border border-app-hover/20 transition-all duration-300
    hover:shadow-lg hover:from-app-card/95 hover:to-app-card p-5;
}

/* Motion effects */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.pulse-on-hover:hover {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
.background-image-full::before {
  content: "";
  position: fixed;
  inset: 0;
  background-image: url("/hero-bg-shade-empty.png");
  background-blend-mode: multiply !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  background-size: cover !important;
  background-attachment: fixed !important;
}
