:root {
  --cm-backgroundColor: var(--biela-elements-editor-backgroundColor, var(--biela-elements-bg-depth-1));
  --cm-textColor: var(--biela-elements-editor-textColor, var(--biela-elements-textPrimary));

  /* Gutter */

  --cm-gutter-backgroundColor: var(--biela-elements-editor-gutter-backgroundColor, var(--cm-backgroundColor));
  --cm-gutter-textColor: var(--biela-elements-editor-gutter-textColor, var(--biela-elements-textSecondary));
  --cm-gutter-activeLineTextColor: var(--biela-elements-editor-gutter-activeLineTextColor, var(--cm-gutter-textColor));

  /* Fold Gutter */

  --cm-foldGutter-textColor: var(--biela-elements-editor-foldGutter-textColor, var(--cm-gutter-textColor));
  --cm-foldGutter-textColorHover: var(--biela-elements-editor-foldGutter-textColorHover, var(--cm-gutter-textColor));

  /* Active Line */

  --cm-activeLineBackgroundColor: var(--biela-elements-editor-activeLineBackgroundColor, rgb(224 231 235 / 30%));

  /* Cursor */

  --cm-cursor-width: 2px;
  --cm-cursor-backgroundColor: var(--biela-elements-editor-cursorColor, var(--biela-elements-textSecondary));

  /* Matching Brackets */

  --cm-matching-bracket: var(--biela-elements-editor-matchingBracketBackgroundColor, rgb(50 140 130 / 0.3));

  /* Selection */

  --cm-selection-backgroundColorFocused: var(--biela-elements-editor-selection-backgroundColor, #42b4ff);
  --cm-selection-backgroundOpacityFocused: var(--biela-elements-editor-selection-backgroundOpacity, 0.3);
  --cm-selection-backgroundColorBlured: var(--biela-elements-editor-selection-inactiveBackgroundColor, #c9e9ff);
  --cm-selection-backgroundOpacityBlured: var(--biela-elements-editor-selection-inactiveBackgroundOpacity, 0.3);

  /* Panels */

  --cm-panels-borderColor: var(--biela-elements-editor-panels-borderColor, var(--biela-elements-borderColor));

  /* Search */

  --cm-search-backgroundColor: var(--biela-elements-editor-search-backgroundColor, var(--cm-backgroundColor));
  --cm-search-textColor: var(--biela-elements-editor-search-textColor, var(--biela-elements-textSecondary));
  --cm-search-closeButton-backgroundColor: var(--biela-elements-editor-search-closeButton-backgroundColor, transparent);

  --cm-search-closeButton-backgroundColorHover: var(
      --biela-elements-editor-search-closeButton-backgroundColorHover,
      var(--biela-elements-item-backgroundActive)
  );

  --cm-search-closeButton-textColor: var(
      --biela-elements-editor-search-closeButton-textColor,
      var(--biela-elements-item-contentDefault)
  );

  --cm-search-closeButton-textColorHover: var(
      --biela-elements-editor-search-closeButton-textColorHover,
      var(--biela-elements-item-contentActive)
  );

  --cm-search-button-backgroundColor: var(
      --biela-elements-editor-search-button-backgroundColor,
      var(--biela-elements-item-backgroundDefault)
  );

  --cm-search-button-backgroundColorHover: var(
      --biela-elements-editor-search-button-backgroundColorHover,
      var(--biela-elements-item-backgroundActive)
  );

  --cm-search-button-textColor: var(--biela-elements-editor-search-button-textColor, var(--biela-elements-textSecondary));

  --cm-search-button-textColorHover: var(
      --biela-elements-editor-search-button-textColorHover,
      var(--biela-elements-textPrimary)
  );

  --cm-search-button-borderColor: var(--biela-elements-editor-search-button-borderColor, transparent);
  --cm-search-button-borderColorHover: var(--biela-elements-editor-search-button-borderColorHover, transparent);

  --cm-search-button-borderColorFocused: var(
      --biela-elements-editor-search-button-borderColorFocused,
      var(--biela-elements-borderColorActive)
  );

  --cm-search-input-backgroundColor: var(--biela-elements-editor-search-input-backgroundColor, transparent);
  --cm-search-input-textColor: var(--biela-elements-editor-search-input-textColor, var(--biela-elements-textPrimary));
  --cm-search-input-borderColor: var(--biela-elements-editor-search-input-borderColor, var(--biela-elements-borderColor));

  --cm-search-input-borderColorFocused: var(
      --biela-elements-editor-search-input-borderColorFocused,
      var(--biela-elements-borderColorActive)
  );

  /* Tooltip */

  --cm-tooltip-backgroundColor: var(--biela-elements-editor-tooltip-backgroundColor, var(--cm-backgroundColor));
  --cm-tooltip-textColor: var(--biela-elements-editor-tooltip-textColor, var(--biela-elements-textPrimary));

  --cm-tooltip-backgroundColorSelected: var(
      --biela-elements-editor-tooltip-backgroundColorSelected,
      theme('colors.alpha.accent.30')
  );

  --cm-tooltip-textColorSelected: var(
      --biela-elements-editor-tooltip-textColorSelected,
      var(--biela-elements-textPrimary)
  );

  --cm-tooltip-borderColor: var(--biela-elements-editor-tooltip-borderColor, var(--biela-elements-borderColor));

  --cm-searchMatch-backgroundColor: var(--biela-elements-editor-searchMatch-backgroundColor, rgba(234, 92, 0, 0.33));
}

html[data-theme='light'] {
  --biela-elements-editor-gutter-textColor: #237893;
  --biela-elements-editor-gutter-activeLineTextColor: var(--biela-elements-textPrimary);
  --biela-elements-editor-foldGutter-textColorHover: var(--biela-elements-textPrimary);
  --biela-elements-editor-activeLineBackgroundColor: rgb(50 53 63 / 5%);
  --biela-elements-editor-tooltip-backgroundColorSelected: theme('colors.alpha.accent.20');
  --biela-elements-editor-search-button-backgroundColor: theme('colors.gray.100');
  --biela-elements-editor-search-button-backgroundColorHover: theme('colors.alpha.gray.10');
}

html[data-theme='dark'] {
  --cm-backgroundColor: var(--biela-elements-bg-depth-2);
  --biela-elements-editor-gutter-textColor: var(--biela-elements-textTertiary);
  --biela-elements-editor-gutter-activeLineTextColor: var(--biela-elements-textSecondary);
  --biela-elements-editor-selection-inactiveBackgroundOpacity: 0.3;
  --biela-elements-editor-activeLineBackgroundColor: rgb(50 53 63 / 50%);
  --biela-elements-editor-foldGutter-textColorHover: var(--biela-elements-textPrimary);
  --biela-elements-editor-matchingBracketBackgroundColor: rgba(66, 180, 255, 0.3);
  --biela-elements-editor-search-button-backgroundColor: theme('colors.gray.800');
  --biela-elements-editor-search-button-backgroundColorHover: theme('colors.alpha.white.10');
}
