.loading-container {
  width: 100%;
  max-width: 1200px;
  height: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  margin: 0 auto;
}
.cls-2 {
  fill: #4ade80;
}
.logo-container-biela {
  width: 300px;
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 60px;
}
.logo-biela {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transform: rotate(-72deg);
  position: relative;
  z-index: 2;
  overflow: visible;
}
.trail-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}
.trail {
  position: absolute;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(74, 222, 128, 0.8) 0%, rgba(74, 222, 128, 0) 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
}
/* Animation keyframes */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}
@keyframes throttle {
  0% {
    stroke-dashoffset: 1000;
    opacity: 0.3;
  }
  100% {
    stroke-dashoffset: 0;
    opacity: 1;
  }
}
@keyframes starTwinkle {
  0% {
    opacity: 0.2;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.2;
    transform: scale(0.8);
  }
}
@keyframes trailEffect {
  0% {
    width: 0;
    height: 0;
    opacity: 0;
  }
  50% {
    width: 150px;
    height: 150px;
    opacity: 0.7;
  }
  100% {
    width: 300px;
    height: 300px;
    opacity: 0;
  }
}
@keyframes fadeInOut {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}
@keyframes pathGlow {
  0% {
    filter: drop-shadow(0 0 2px rgba(74, 222, 128, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 15px rgba(74, 222, 128, 0.8));
  }
  100% {
    filter: drop-shadow(0 0 2px rgba(74, 222, 128, 0.5));
  }
}
@keyframes flyAway {
  0% {
    transform: rotate(-72deg) scale(1.2);
    opacity: 1;
  }
  100% {
    transform: rotate(-72deg) scale(0.1) translate(1000px, -1000px);
    opacity: 0;
  }
}
.circle-fade {
  animation: fadeInOut 2s ease-in-out infinite;
}