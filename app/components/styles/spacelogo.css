.logo-container {
  position: relative;
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  overflow: visible;
}

.logo {
  position: absolute;
  width: auto;
  height: 60px;
  z-index: 2;
  transform-origin: center;
  transition: transform 0.3s ease;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Stars container */
.stars-container {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}

.star {
  position: absolute;
  background-color: #fff;
  border-radius: 50%;
  opacity: 0;
  z-index: 1;
}

/* Common animations */
@keyframes pulse {
  0% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}

@keyframes vibrate {
  0% {
    transform: scale(0.995);
  }
  100% {
    transform: scale(1.005);
  }
}

@keyframes blink {
  0%, 80% {
    opacity: 1;
  }
  90% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

@keyframes twinkle {
  0% {
    opacity: 0.7;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes shooting-star {
  0% {
    transform: translateX(30px) translateY(-30px);
    opacity: 0;
  }
  10% {
    opacity: 0.8;
  }
  100% {
    transform: translateX(-30px) translateY(30px);
    opacity: 0;
  }
}

/* Cosmic Pulse - Enhanced with alternating throttle effects */
.cosmic-pulse .throttle-group {
  filter: drop-shadow(0 0 1px rgba(74, 222, 128, 0.2));
}

/* Chat mode specific styles with renamed class */
.cosmic-pulse.logo-chat-mode .throttle-group {
  filter: drop-shadow(0 0 1px rgba(135, 19, 135, 0.2));
}

.is-running-animation.cosmic-pulse .throttle-group {
  filter: drop-shadow(0 0 6px rgba(74, 222, 128, 0.8));
}

.is-running-animation.cosmic-pulse.logo-chat-mode .throttle-group {
  filter: drop-shadow(0 0 6px rgba(135, 19, 135, 0.8));
}

/* Enhanced throttle lines with intense vibration effect - IDENTICAL to error state */
.cosmic-pulse .throttle-line {
  transform-origin: left center;
  opacity: 0.8;
  transition: opacity 0.3s ease, filter 0.3s ease;
}

.is-running-animation.cosmic-pulse .throttle-line-1 {
  animation: cosmic-throttle-1 0.08s infinite alternate;
  filter: drop-shadow(0 0 5px rgba(74, 222, 128, 0.9));
}

.is-running-animation.cosmic-pulse.logo-chat-mode .throttle-line-1 {
  animation: cosmic-throttle-1 0.08s infinite alternate;
  filter: drop-shadow(0 0 5px rgba(135, 19, 135, 0.9));
}

.is-running-animation.cosmic-pulse .throttle-line-2 {
  animation: cosmic-throttle-2 0.06s infinite alternate;
  filter: drop-shadow(0 0 6px rgba(74, 222, 128, 0.9));
}

.is-running-animation.cosmic-pulse.logo-chat-mode .throttle-line-2 {
  animation: cosmic-throttle-2 0.06s infinite alternate;
  filter: drop-shadow(0 0 6px rgba(135, 19, 135, 0.9));
}

.is-running-animation.cosmic-pulse .throttle-line-3 {
  animation: cosmic-throttle-3 0.1s infinite alternate;
  filter: drop-shadow(0 0 7px rgba(74, 222, 128, 0.9));
}

.is-running-animation.cosmic-pulse.logo-chat-mode .throttle-line-3 {
  animation: cosmic-throttle-3 0.1s infinite alternate;
  filter: drop-shadow(0 0 7px rgba(135, 19, 135, 0.9));
}

.is-running-animation.cosmic-pulse .throttle-line-4 {
  animation: cosmic-throttle-4 0.07s infinite alternate;
  filter: drop-shadow(0 0 5px rgba(74, 222, 128, 0.9));
}

.is-running-animation.cosmic-pulse.logo-chat-mode .throttle-line-4 {
  animation: cosmic-throttle-4 0.07s infinite alternate;
  filter: drop-shadow(0 0 5px rgba(135, 19, 135, 0.9));
}

.is-running-animation.cosmic-pulse .spaceship-body {
  animation: vibrate 0.1s infinite alternate;
}

/* Enhanced star particles with magic effect - IDENTICAL to error state */
.cosmic-pulse .star-particle {
  opacity: 0.7;
  transition: opacity 0.3s ease, transform 0.3s ease, filter 0.3s ease;
}

.is-running-animation.cosmic-pulse .star-particle {
  opacity: 1;
  animation: cosmic-twinkle 2s infinite alternate;
  filter: drop-shadow(0 0 3px rgba(74, 222, 128, 0.8));
}

.is-running-animation.cosmic-pulse.logo-chat-mode .star-particle {
  opacity: 1;
  animation: cosmic-twinkle-chat 2s infinite alternate;
  filter: drop-shadow(0 0 3px rgba(135, 19, 135, 0.8));
}

/* Different animation delays for each star - IDENTICAL to error state */
.is-running-animation.cosmic-pulse .star-particle-1 { animation-delay: 0.1s; }
.is-running-animation.cosmic-pulse .star-particle-2 { animation-delay: 0.3s; }
.is-running-animation.cosmic-pulse .star-particle-3 { animation-delay: 0.5s; }
.is-running-animation.cosmic-pulse .star-particle-4 { animation-delay: 0.2s; }
.is-running-animation.cosmic-pulse .star-particle-5 { animation-delay: 0.4s; }
.is-running-animation.cosmic-pulse .star-particle-6 { animation-delay: 0.6s; }
.is-running-animation.cosmic-pulse .star-particle-7 { animation-delay: 0.15s; }
.is-running-animation.cosmic-pulse .star-particle-8 { animation-delay: 0.35s; }
.is-running-animation.cosmic-pulse .star-particle-9 { animation-delay: 0.55s; }

.is-running-animation.cosmic-pulse .star {
  animation: cosmic-shooting-star 2s infinite linear;
}

.is-running-animation.cosmic-pulse.logo-chat-mode .star {
  animation: cosmic-shooting-star-chat 2s infinite linear;
}

/* Star positions - same as error state */
.cosmic-pulse .star1 { width: 1px; height: 1px; top: 10%; left: 20%; animation-delay: 0.1s; }
.cosmic-pulse .star2 { width: 2px; height: 2px; top: 20%; left: 80%; animation-delay: 0.5s; }
.cosmic-pulse .star3 { width: 1px; height: 1px; top: 30%; left: 40%; animation-delay: 0.9s; }
.cosmic-pulse .star4 { width: 1px; height: 1px; top: 40%; left: 60%; animation-delay: 1.3s; }
.cosmic-pulse .star5 { width: 2px; height: 2px; top: 50%; left: 30%; animation-delay: 1.7s; }
.cosmic-pulse .star6 { width: 1px; height: 1px; top: 60%; left: 70%; animation-delay: 2.1s; }
.cosmic-pulse .star7 { width: 1px; height: 1px; top: 70%; left: 50%; animation-delay: 2.5s; }
.cosmic-pulse .star8 { width: 2px; height: 2px; top: 80%; left: 10%; animation-delay: 2.9s; }
.cosmic-pulse .star9 { width: 1px; height: 1px; top: 85%; left: 90%; animation-delay: 3.3s; }
.cosmic-pulse .star10 { width: 1px; height: 1px; top: 15%; left: 45%; animation-delay: 3.7s; }
.cosmic-pulse .star11 { width: 2px; height: 2px; top: 55%; left: 15%; animation-delay: 4.1s; }
.cosmic-pulse .star12 { width: 1px; height: 1px; top: 75%; left: 85%; animation-delay: 4.5s; }

@keyframes cosmic-throttle-1 {
  0% {
    transform: translate(-0.8px, -0.5px);
  }
  100% {
    transform: translate(0.8px, 0.5px);
  }
}

@keyframes cosmic-throttle-2 {
  0% {
    transform: translate(1px, -0.6px);
  }
  100% {
    transform: translate(-1px, 0.6px);
  }
}

@keyframes cosmic-throttle-3 {
  0% {
    transform: translate(-1.2px, 0.3px);
  }
  100% {
    transform: translate(1.2px, -0.3px);
  }
}

@keyframes cosmic-throttle-4 {
  0% {
    transform: translate(0.7px, 0.9px);
  }
  100% {
    transform: translate(-0.7px, -0.9px);
  }
}

@keyframes cosmic-twinkle {
  0% {
    opacity: 0.7;
    transform: scale(0.95);
    filter: drop-shadow(0 0 1px rgba(74, 222, 128, 0.5));
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
    filter: drop-shadow(0 0 3px rgba(74, 222, 128, 0.8));
  }
  100% {
    opacity: 0.9;
    transform: scale(1);
    filter: drop-shadow(0 0 2px rgba(74, 222, 128, 0.6));
  }
}

@keyframes cosmic-twinkle-chat {
  0% {
    opacity: 0.7;
    transform: scale(0.95);
    filter: drop-shadow(0 0 1px rgba(135, 19, 135, 0.5));
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
    filter: drop-shadow(0 0 3px rgba(135, 19, 135, 0.8));
  }
  100% {
    opacity: 0.9;
    transform: scale(1);
    filter: drop-shadow(0 0 2px rgba(135, 19, 135, 0.6));
  }
}

@keyframes cosmic-shooting-star {
  0% {
    transform: translateX(40px) translateY(-40px);
    opacity: 0;
    background-color: #4ADE80;
  }
  10% {
    opacity: 0.9;
    transform: translateX(30px) translateY(-30px);
    background-color: #4ADE80;
  }
  50% {
    opacity: 1;
    transform: translateX(0) translateY(0);
    background-color: #ffffff;
  }
  90% {
    opacity: 0.9;
    transform: translateX(-30px) translateY(30px);
    background-color: #4ADE80;
  }
  100% {
    transform: translateX(-40px) translateY(40px);
    opacity: 0;
    background-color: #4ADE80;
  }
}

@keyframes cosmic-shooting-star-chat {
  0% {
    transform: translateX(40px) translateY(-40px);
    opacity: 0;
    background-color: #871387;
  }
  10% {
    opacity: 0.9;
    transform: translateX(30px) translateY(-30px);
    background-color: #871387;
  }
  50% {
    opacity: 1;
    transform: translateX(0) translateY(0);
    background-color: #ffffff;
  }
  90% {
    opacity: 0.9;
    transform: translateX(-30px) translateY(30px);
    background-color: #871387;
  }
  100% {
    transform: translateX(-40px) translateY(40px);
    opacity: 0;
    background-color: #871387;
  }
}
