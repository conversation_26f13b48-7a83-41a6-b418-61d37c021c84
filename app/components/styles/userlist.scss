
html[data-theme="light"] .user-list {
  height: auto;
  background: white;
  border: 1px solid #E9E9E9;


}

html[data-theme="dark"] .user-list {
  height: auto;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.95) 0%, rgba(31, 31, 31, 0.80) 100%);
}

.user-list ul {
  padding: 16px;
}

.light-border {
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, rgb(37, 35, 35), rgb(37, 35, 35), rgb(37, 35, 35), #4ADE80, rgb(37, 35, 35), rgb(37, 35, 35), rgb(37, 35, 35));
}

.border-container-list {
  top: -1px;
  display: none;
  position: absolute;
  width: 90%;
  background-color: transparent;


}

html[data-theme="dark"]
.user-list-item:hover .border-container-list {
  display: block;

}

.user-list-item {
  border: 1px solid transparent;
  padding: 10px 10px;
  cursor: pointer;
  position: relative;
  border-radius: 8px;


}

a {
  text-decoration: none !important;
}

html[data-theme="light"] .user-list-chat-open {
  height: auto;
  background: white;
  border: 1px solid #E9E9E9;
}

html[data-theme="dark"] .user-list-chat-open {
  height: auto;
}

.user-list-chat-open ul {
  padding: 16px;
}


.list-footer {
  display: flex;
  justify-content: space-between;
  padding: 10px 24px
}
