import React, { useState } from 'react';
import { Dialog, DialogButton, DialogTitle, DialogDescription, DialogClose, DialogRoot } from '~/ai/components/Dialog';
import { FaSpinner } from 'react-icons/fa';

interface DisconnectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

const DisconnectModal: React.FC<DisconnectModalProps> = ({ isOpen, onClose, onConfirm }) => {
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);


  const handleConfirmClick = () => {
    setIsDisconnecting(true);
    setTimeout(() => {
      setIsDisconnecting(false);
      setIsSuccess(true);
      onConfirm();
      // After showing success message, close modal and reset state
      setTimeout(() => {
        setIsSuccess(false);
      }, 1500);
    }, 1000);
  };
  return (
    <DialogRoot open={isOpen}>
      <Dialog onBackdrop={onClose} onClose={onClose} >
        <DialogTitle className={'font-light text-white'}>
          {isSuccess ? 'Success' : 'Disconnect Database'}
          {!isDisconnecting && !isSuccess && (
            <DialogClose onClick={onClose} />
          )}
        </DialogTitle>
        <DialogDescription>
          {isSuccess ? (
            <div className="flex flex-col items-center text-center">
              <div className="text-primary mb-4 animate-fade-in">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#4ADE80" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
              </div>
              <div className={'font-light text-white'}>Successfully disconnected from Supabase!</div>
            </div>
          ) : isDisconnecting ? (
            <div className="flex flex-col items-center text-center">
              <FaSpinner className="animate-spin text-gray-400 w-6 h-6  mb-4" />
              <div className={'font-light text-white'}>Disconnecting from Supabase...</div>
            </div>
          ) : (
            <>
              <div className="my-2 font-light text-white">Are you sure you want to disconnect?</div>
              <div className="my-2 font-light text-white">After that you will need to connect again to Supabase.</div>
            </>
          )}
        </DialogDescription>
        {!isDisconnecting && !isSuccess && (
          <div className="flex justify-end gap-4 px-6 pb-6">
            <DialogButton type="secondary" onClick={onClose}>
              Cancel
            </DialogButton>
            <DialogButton type="danger" onClick={handleConfirmClick}>
              Disconnect
            </DialogButton>
          </div>
        )}
      </Dialog>
    </DialogRoot>
  );
};

export default DisconnectModal;
