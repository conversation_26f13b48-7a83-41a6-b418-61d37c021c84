import React, { useEffect, useState } from 'react';
import { loadingFunctionBiela } from '~/utils/loadingAnimation';
import '~/components/styles/loading-biela-supabase.css';
import SettingUpProject from '~/assets/icons/loading-animation/setting-up-project.svg?url';
import CreatingUser from '~/assets/icons/loading-animation/creating-user.svg?url';

export const LoadingBielaSupabase: React.FC<{ isLoadingComplete: () => boolean }> = ({ isLoadingComplete }) => {
  const messages = [
    { img: CreatingUser, text: 'Setting up Supabase Authentication...' },
    { img: SettingUpProject, text: 'Setting up Supabase Project...' },
  ];
  const [currentMessage, setCurrentMessage] = useState(0);
  const [fade, setFade] = useState(true);

  useEffect(() => {
    loadingFunctionBiela(isLoadingComplete);

    // Animate messages
    const interval = setInterval(() => {
      setFade(false);
      setTimeout(() => {
        setCurrentMessage((prevIndex) => {
          if (prevIndex === messages.length - 1) {
            clearInterval(interval);
            return prevIndex;
          }

          return prevIndex + 1;
        });
        setFade(true);
      }, 250);
    }, 1500);

    return () => clearInterval(interval);
  }, [isLoadingComplete, messages.length]);

  return (
    <div className={`w-full h-full`}>
      <div className="loading-container">
        <div className="logo-container-biela">
          <svg id="biela-logo" className="logo-biela" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
            <defs>
              <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
                <feGaussianBlur stdDeviation="10" result="blur" />
                <feComposite in="SourceGraphic" in2="blur" operator="over" />
              </filter>
            </defs>
            <g id="background">
              <rect width="1024" height="1024" fill="transparent" />
            </g>
            <g id="logo_01" className="logo-elements">
              <g id="main">
                <g id="exterior_circle" className="circle-element" data-name="exterior circle">
                  <path
                    className="cls-2"
                    d="M434.7,894.94c-.22,0-.45-.02-.68-.05-2.28-.31-56.47-7.99-120.57-47.84-37.61-23.38-70.73-53.03-98.45-88.14-34.6-43.83-60.8-96.29-77.88-155.95-.76-2.65.78-5.42,3.43-6.18,2.65-.76,5.42,.78,6.18,3.43,37.77,131.92,114.16,202.29,171.59,238.1,62.34,38.86,116.49,46.6,117.03,46.67,2.73,.37,4.65,2.89,4.28,5.63-.34,2.51-2.48,4.33-4.95,4.33Z"
                  />
                  <path
                    className="cls-2"
                    d="M135.3,565c-2.35,0-4.44-1.66-4.9-4.05-6.53-33.87-3.53-69.02-3.4-70.5,.24-2.75,2.67-4.79,5.42-4.54,2.75,.24,4.78,2.67,4.54,5.42-.03,.35-3,35.27,3.26,67.72,.52,2.71-1.25,5.33-3.96,5.86-.32,.06-.64,.09-.95,.09Z"
                  />
                  <path
                    className="cls-2"
                    d="M520.96,904.13c-28.06,0-50.9-3.37-51.99-3.54-2.73-.41-4.61-2.96-4.2-5.69,.41-2.73,2.95-4.62,5.69-4.2,.3,.04,29.96,4.42,62.34,3.21,2.77-.13,5.08,2.05,5.18,4.81s-2.05,5.08-4.81,5.18c-4.14,.16-8.23,.22-12.21,.22Z"
                  />
                  <path
                    className="cls-2"
                    d="M900.95,580.99c-.48,0-.97-.07-1.46-.22-2.64-.8-4.13-3.58-3.33-6.22,.07-.25,3.5-12.4,4.75-47.24,.1-2.76,2.44-4.91,5.18-4.82,2.76,.1,4.92,2.42,4.82,5.18-1.32,36.74-5.02,49.26-5.17,49.77-.66,2.16-2.64,3.55-4.78,3.55Z"
                  />
                  <path
                    className="cls-2"
                    d="M351.98,167.65c-1.88,0-3.69-1.07-4.54-2.89-1.17-2.5-.08-5.48,2.42-6.64,66.96-31.16,130.06-37.9,171.2-38.07,44.66-.19,73.15,6.93,74.34,7.23,2.68,.68,4.29,3.4,3.61,6.08-.68,2.68-3.4,4.29-6.08,3.61-.41-.1-28.78-7.14-72.18-6.92-40.06,.2-101.49,6.81-166.67,37.14-.68,.32-1.4,.47-2.11,.47Z"
                  />
                  <path
                    className="cls-2"
                    d="M905.32,490.55c-2.68,0-4.89-2.12-4.99-4.82-.03-.75-2.79-63.96-36.74-136.75-31.26-67.03-96.21-156.21-228.08-200.43-2.62-.88-4.03-3.71-3.15-6.33s3.71-4.03,6.33-3.15c59.76,20.04,111.25,50.72,153.02,91.17,33.43,32.37,60.72,71.03,81.12,114.89,34.72,74.64,37.4,137.59,37.5,140.24,.1,2.76-2.05,5.08-4.81,5.18-.06,0-.12,0-.19,0Z"
                  />
                </g>
                <g id="inner-content">
                  <g id="B" className="b-letter">
                    <path
                      className="cls-2"
                      d="M894.58,695.69c-4.23,33.56-30.66,61.99-62,71.27-16.92,5.55-33.65,1.42-51.45-2.54-10.2-.04-20.05,11.64-14.87,21.47,12.37,24.33,16.95,51.95,4.76,76.88-15.07,30.34-50.3,51.95-85.73,51.37-68.7,.4-121.03-58.43-168.54-106.69,0-.02-.01-.02-.02-.02-.96-.64-1.88-1.39-2.74-2.24l-136.17-136.18c-6.83-6.83-6.83-17.91,0-24.74,6.83-6.84,17.91-6.84,24.75,0l119.43,119.43h.01c36.72,33.92,78.81,89.24,126.46,103.98,39.13,14.34,83.85-14.37,89.59-54.5,4.97-34.82-19.9-64.76-45.57-89.97-9.68-11.96-41.62-30.63-24-48.57,17.3-16.93,34.62,11.72,46.15,21.51,25.26,24.35,41.02,36.62,69.82,37.9,30.63-.1,59.77-25.91,65.77-55.58,7.11-34.42-20.45-64.68-45.49-89.47-.02-.02-.04-.04-.07-.06-.67-.67-1.34-1.33-2.01-1.99h0s-6.89-6.89-6.89-6.89l-64.45-64.45c-6.83-6.84-6.83-17.92,0-24.75,6.84-6.84,17.92-6.84,24.75,0l72.78,72.77c.47,.47,.91,.97,1.32,1.48,38.16,37.82,71.34,78.29,64.42,130.58Z"
                    />
                    <path className="cls-2" d="M802.65,586.94c-2.36-2.32-4.69-4.6-6.95-6.82l.06-.06,6.89,6.88Z" />
                  </g>
                  <g className="throttle-lines">
                    <g>
                      <line className="cls-2" x1="549.83" y1="681.86" x2="163.56" y2="295.6" />
                      <path
                        className="cls-2 throttle-line"
                        d="M547.15,684.54L152.96,306.21c-5.98-5.74-6.17-15.24-.44-21.21,5.87-6.15,15.78-6.14,21.65,0,0,0,378.33,394.19,378.33,394.19,1.45,1.51,1.4,3.91-.11,5.35-1.47,1.41-3.79,1.39-5.24,0h0Z"
                      />
                    </g>
                    <g>
                      <line className="cls-2" x1="532.98" y1="428.89" x2="284.78" y2="180.68" />
                      <path
                        className="cls-2 throttle-line"
                        d="M530.44,431.43l-256.27-240.14c-6.05-5.67-6.36-15.17-.69-21.21s15.17-6.36,21.21-.69c.19,0,240.5,256.7,240.83,256.96,2.95,3.23-1.43,8.24-5.09,5.09h0Z"
                      />
                    </g>
                    <g>
                      <line className="cls-2" x1="542.32" y1="560.85" x2="106.51" y2="125.04" />
                      <path
                        className="cls-2 throttle-line"
                        d="M539.55,563.62S95.91,135.65,95.91,135.65c-5.96-5.75-6.13-15.25-.38-21.21,5.87-6.11,15.72-6.11,21.59,0,0,0,427.97,443.64,427.97,443.64,1.5,1.56,1.46,4.04-.1,5.54-1.52,1.47-3.93,1.45-5.44,0h0Z"
                      />
                    </g>
                  </g>
                  <path
                    className="cls-2"
                    d="M804.67,588.94c-.2-.18-.39-.37-.58-.56l-1.43-1.43c.67,.66,1.34,1.32,2.01,1.99Z"
                  />
                  <path
                    className="cls-2 throttle-line"
                    d="M348.7,636.35l-197.92-212.03c-4.21-4.7,2.19-11.42,7.11-7.11,0,0,212.03,197.92,212.03,197.92,6.06,5.66,6.39,15.15,.73,21.21-5.88,6.35-16.06,6.34-21.94,0h0Z"
                  />
                  <path
                    className="cls-2 throttle-line"
                    d="M700.54,479.96l-133.28-147.38c-1.86-2.06-1.7-5.24,.36-7.11,1.93-1.74,4.86-1.7,6.75,0l147.38,133.28c6.15,5.56,6.63,15.06,1.07,21.21s-15.06,6.63-21.21,1.07c-.37-.34-.74-.7-1.07-1.07,0,0,.33,.36,0,0Z"
                  />
                </g>
              </g>
              <g id="stars" className="stars-element">
                <path
                  className="cls-2 star"
                  d="M839.17,209.12h0c0-8.29,6.72-15.02,15.02-15.02h0c-8.29,0-15.02-6.72-15.02-15.02h0c0,8.29-6.72,15.02-15.02,15.02h0c8.29,0,15.02,6.72,15.02,15.02Z"
                />
                <path
                  className="cls-2 star"
                  d="M750.58,370.29h0c0-11.4,9.24-20.63,20.63-20.63h0c-11.4,0-20.63-9.24-20.63-20.63h0c0,11.4-9.24,20.63-20.63,20.63h0c11.4,0,20.63,9.24,20.63,20.63Z"
                />
                <path
                  className="cls-2 star"
                  d="M547,235.37h0c0-11.4,9.24-20.63,20.63-20.63h0c-11.4,0-20.63-9.24-20.63-20.63h0c0,11.4-9.24,20.63-20.63,20.63h0c11.4,0,20.63,9.24,20.63,20.63Z"
                />
                <path
                  className="cls-2 star"
                  d="M185.62,549.85h0c0-8.21,6.65-14.86,14.86-14.86h0c-8.21,0-14.86-6.65-14.86-14.86h0c0,8.21-6.65,14.86-14.86,14.86h0c8.21,0,14.86,6.65,14.86,14.86Z"
                />
                <path
                  className="cls-2 star"
                  d="M260.21,709.05h0c0-14.47,11.73-26.21,26.21-26.21h0c-14.47,0-26.21-11.73-26.21-26.21h0c0,14.47-11.73,26.21-26.21,26.21h0c14.47,0,26.21,11.73,26.21,26.21Z"
                />
                <path
                  className="cls-2 star"
                  d="M429.93,825.44h0c0-11.63,9.43-21.06,21.06-21.06h0c-11.63,0-21.06-9.43-21.06-21.06h0c0,11.63-9.43,21.06-21.06,21.06h0c11.63,0,21.06,9.43,21.06,21.06Z"
                />
                <path
                  className="cls-2 star"
                  d="M194.16,860.48h0c0-12.93,10.48-23.4,23.4-23.4h0c-12.93,0-23.4-10.48-23.4-23.4h0c0,12.93-10.48,23.4-23.4,23.4h0c12.93,0,23.4,10.48,23.4,23.4Z"
                />
                <path
                  className="cls-2 star"
                  d="M217.56,305.67h0c0-5.56,4.51-10.07,10.07-10.07h0c-5.56,0-10.07-4.51-10.07-10.07h0c0,5.56-4.51,10.07-10.07,10.07h0c5.56,0,10.07,4.51,10.07,10.07Z"
                />
                <path
                  className="cls-2 star"
                  d="M224.36,186.37h0c0-5.32,4.32-9.64,9.64-9.64h0c-5.32,0-9.64-4.32-9.64-9.64h0c0,5.32-4.32,9.64-9.64,9.64h0c5.32,0,9.64,4.32,9.64,9.64Z"
                />
              </g>
            </g>
          </svg>
          <div className="trail-container">
            <div className="trail"></div>
          </div>
        </div>
        <div className="flex flex-col items-center gap-4 overflow-hidden h-[40px]">
          <div
            className={`flex items-center justify-center gap-2 transition-opacity duration-300 ${fade ? 'opacity-100' : 'opacity-0'}`}
          >
            <img src={messages[currentMessage].img} alt="" style={{ width: 32, height: 32 }} />
            <p className="text-white">{messages[currentMessage].text}</p>
          </div>
        </div>
      </div>

    </div>
  );
};
