import React, { useEffect, useState, useRef } from 'react';
import { chatId } from '~/ai/lib/persistence/useChatHistory.client';
import { backendApiFetch } from '~/ai/lib/backend-api';
import { UserStore } from '~/ai/lib/stores/user/userStore';
import { workbenchStore } from '~/ai/lib/stores/workbench';
import { useTranslation } from 'react-i18next';
import { CircleStackIcon } from '@heroicons/react/24/outline';
import { Copy, LinkIcon } from 'lucide-react';
import { ChevronDownIcon, CheckIcon, ArrowPathIcon } from '@heroicons/react/24/solid';
import type { JSONValue, Message } from 'ai';
import ErrorSupabaseDialog from '~/ai/components/ErrorSupabaseDialog';
import DisconnectModal from '~/components/supabase/DisconectSupabaseModal';
import { FaSpinner } from 'react-icons/fa';

interface SupabaseProject {
  id: string;
  name: string;
  organization_id: string;
  region: string;
  created_at: string;
  status: string;
  sendMessage?: (event: React.UIEvent, messageInput?: string, messageOptions?: JSONValue) => void;
}

interface SupabaseConnectInterface {
  sendMessage?: (event: React.UIEvent, messageInput?: string, messageOptions?: JSONValue) => void;
}

const SupabaseConnectClient = ({sendMessage}:SupabaseConnectInterface) => {
  const [projectSlug, setProjectSlug] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [hasRefreshToken, setHasRefreshToken] = useState(false);
  const [projectId, setProjectId] = useState<string | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [projects, setProjects] = useState<SupabaseProject[]>([]);
  const [isProjectsDropdownOpen, setIsProjectsDropdownOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<SupabaseProject | null>(null);
  const [isDuplicateWithData, setIsDuplicateWithData] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [pausingProjects, setPausingProjects] = useState<Set<string>>(new Set());
  const [pollingProjects, setPollingProjects] = useState<Set<string>>(new Set());
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation('translation');
  const [isErrorSupabaseDialogOpen, setErrorSupabaseDialogOpen] = useState(false);
  const [creatingNewDataBase, setCreatingNewDataBase] = useState(false);
  const [typeOfRequest, setTypeOfRequest] = useState<string | null>(null);
  const [specificProjectId,setSpecificProjectId] = useState<string | null>(null);
  const [error, setError] = useState<{show: boolean; message: string; title?: string}>({
    show: false,
    message: '',
    title: 'Error'
  });
  const [showDisconnectModal, setShowDisconnectModal] = useState(false);
  async function fetchDatabaseSchemaAndSendToAI(
    projectSlug: string,
    operationType: 'link' | 'duplicate' | 'normal' = 'normal'
  ) {
    try {
      const dbInfoResponse = await backendApiFetch(`/cloud/supabase/project-info/${projectSlug}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!dbInfoResponse.ok) {
        throw new Error('Failed to fetch database info');
      }

      const dbInfo = await dbInfoResponse.json();

      const schemaResponse = await backendApiFetch(`/cloud/supabase/database-schema/${projectSlug}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
      });

      if (!schemaResponse.ok) {
        console.error('Failed to fetch database schema');
        return;
      }

      const schemaData = await schemaResponse.json();

      let dbMessage = "";

      if (operationType === 'link') {
        dbMessage = 'Do not run migration, and discuss with the user the database structure according with his project files, do not start to code until the user explicitly tell you what is his approach.\n\n';
        dbMessage += "The project is now connected with an existing Supabase database with the following details:\n\n";
      } else if (operationType === 'duplicate') {
        dbMessage = "Check the project files and the database structure. Do not make any changes to the project files if they are not related to the database structure.\n\n";
        dbMessage += "I've duplicated a Supabase project with the following database structure:\n\n";
      } else {
        dbMessage = "I've connected my project to a Supabase database with the following details:\n\n";
      }

      if (schemaData && schemaData.tables && schemaData.tables.length > 0) {
        dbMessage += "Database Schema:\n";

        schemaData.tables.forEach(table => {
          dbMessage += `\nTable: ${table.name}\n`;
          dbMessage += "Columns:\n";

          table.columns.forEach(column => {
            dbMessage += `- ${column.column_name}: ${column.data_type}`;
            if (column.is_primary) dbMessage += " (Primary Key)";
            if (column.is_nullable === false) dbMessage += " (NOT NULL)";
            if (column.default_value) dbMessage += ` (Default: ${column.default_value})`;
            dbMessage += "\n";
          });

          if (table.relations && table.relations.length > 0) {
            dbMessage += "Relations:\n";
            table.relations.forEach(relation => {
              dbMessage += `- ${relation.constraint_name}: ${table.name}.${relation.column_name} → ${relation.foreign_table}.${relation.foreign_column}\n`;
            });
          }
        });

        if (operationType === 'link') {
          dbMessage += "\nI've linked to an existing database. Please use the existing schema and don't run any migrations. Can you help me integrate with this existing database structure?";
        } else if (operationType === 'duplicate') {
          dbMessage += "\nPlease analyze if the database structure is compatible with the project files. Advise on any potential issues and suggest how to properly integrate this duplicated database structure.";
        } else {
          dbMessage += "\nNow that you have the database schema, can you integrate this supabase features effectively in my project?";
        }
      }

      sendMessage?.({} as React.UIEvent, dbMessage, {
        annotations: ['hidden'],
      });
    } catch (error) {
      console.error("Error fetching database info:", error);
    }
  };

  // Fixed polling logic with proper dependency tracking
  useEffect(() => {
    // Clear existing interval
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }

    // Start polling if we have projects that need status checks
    if (pollingProjects.size > 0) {
      pollingIntervalRef.current = setInterval(() => {
        fetchSupabaseProjects();
      }, 30000); // Check every 30 seconds
    }

    // Cleanup on unmount or when effect re-runs
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, [pollingProjects.size]); // Use size instead of the Set itself for proper dependency tracking

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isDropdownOpen) {
        const target = event.target as HTMLElement;
        if (dropdownRef.current && !dropdownRef.current.contains(target)) {
          setIsDropdownOpen(false);
          setIsProjectsDropdownOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isDropdownOpen]);

  useEffect(() => {
    const initializeProjectSlug = async () => {
      const slug = workbenchStore.getSlug();
      setProjectSlug(slug);
      localStorage.setItem('projectSlug', slug);
    };

    initializeProjectSlug();
  }, []);

  useEffect(() => {
    const checkSupabaseAuth = async () => {
      const userStore = UserStore.getInstance();
      const user = userStore.getUser();

      if (!user) return;

      try {
        const response = await backendApiFetch('/cloud/supabase/check-auth', {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
        });

        const data = await response.json();

        if (response.ok && data.hasRefreshToken) {
          setHasRefreshToken(true);
        }
      } catch (error) {
        console.error('Failed to check auth status:', error);
      }
    };

    checkSupabaseAuth();
  }, []);

  useEffect(() => {
    const checkSupabaseConnection = async () => {
      const userStore = UserStore.getInstance();
      const user = userStore.getUser();

      if (!projectSlug || !user) return;

      try {
        const response = await backendApiFetch(`/cloud/supabase/project/${projectSlug}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          },
        });

        const data = await response.json();

        if (response.ok && data.projectId) {
          setIsConnected(true);
          localStorage.setItem('projectUrl', data.projectUrl);
          localStorage.setItem('annonKey', data.annonKey);
          setProjectId(data.projectId);
        } else {
          clearSupabaseData();
        }
      } catch (error) {
        clearSupabaseData();
      }
    };

    checkSupabaseConnection();
  }, [projectSlug]);

  const clearSupabaseData = () => {
    localStorage.removeItem('projectUrl');
    localStorage.removeItem('annonKey');
    setIsConnected(false);
    setProjectId(null);
  };

  const handleSupabaseAuthComplete = (data: { projectUrl: string; annonKey: string }) => {
    setIsConnected(true);
    setHasRefreshToken(true);
    localStorage.setItem('projectUrl', data.projectUrl);
    localStorage.setItem('annonKey', data.annonKey);
    localStorage.setItem('justConnectedSupabase', 'true');
    const projectIdValue = data.projectUrl.split('//')[1].split('.')[0];
    setProjectId(projectIdValue);

    if (projectSlug) {
      fetchDatabaseSchemaAndSendToAI(projectSlug);
    }

 /*   workbenchStore.actionAlert.set({
      type: 'database',
      title: 'Database Creation',
      description: 'Would you like to create a database for your project?',
      content: 'Content',
      source: 'preview',
    });*/
  };

  const handleDisconnect = async () => {
    try {
      const response = await backendApiFetch('/cloud/supabase/disconnect', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.ok) {
        clearSupabaseData();
        setIsConnected(false);
        setHasRefreshToken(false);
        setIsDropdownOpen(false);

      /*  workbenchStore.actionAlert.set({
          type: 'success',
          title: 'Disconnected',
          description: 'Successfully disconnected from Supabase',
          content: 'Content',
          source: 'preview',
        });*/
      } else {
        console.error('Failed to disconnect from Supabase');
      }
    } catch (error) {
      console.error('Error disconnecting from Supabase:', error);
    }
  };

  const initOAuthFlow = async () => {
    const response = await backendApiFetch('/cloud/supabase/auth-url', {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
    });

    const data = await response.json();

    if (response.ok && data.authUrl) {
      localStorage.setItem('code_verifier', data.codeVerifier);
      localStorage.setItem('chatID', chatId.get() as string);
      window.open(data.authUrl, '_blank');

      const handleStorageChange = (e: StorageEvent) => {
        if (e.key === 'supabaseAuthData' && e.newValue) {
          try {
            const authData = JSON.parse(e.newValue);
            localStorage.removeItem('supabaseAuthData');
            handleSupabaseAuthComplete(authData);
          } catch (err) {
            console.error('Error processing auth data:', err);
          }
        }
      };

      window.addEventListener('storage', handleStorageChange);
      window.addEventListener('message', (event) => {
        if (event.data.type === 'SUPABASE_AUTH_COMPLETE') {
          handleSupabaseAuthComplete(event.data);
        }
      }, { once: true });
    }
  };

  const handleConnect = async () => {
    if (isConnected && projectId) {
      window.open(`https://supabase.com/dashboard/project/${projectId}/editor`, '_blank');
      return;
    }

    if (!hasRefreshToken) {
      initOAuthFlow();
      return;
    }

    setIsDropdownOpen(!isDropdownOpen);

    if (!isDropdownOpen && hasRefreshToken) {
      fetchSupabaseProjects();
    }
  };

  const fetchSupabaseProjects = async () => {
    if(!projects.length) {
      setIsLoading(true);
    }
    try {
      const response = await backendApiFetch('/cloud/supabase/supabase-projects', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
      });

      if (response.ok) {
        const data = await response.json();
        setProjects(data);

        // Update polling projects set
        const newPollingProjects = new Set<string>();
        data.forEach((project: SupabaseProject) => {
          if (project.status === 'COMING_UP' || project.status === 'PAUSING') {
            newPollingProjects.add(project.id);
          }
        });
        setPollingProjects(newPollingProjects);
      } else {
        console.error('Failed to fetch Supabase projects');
      }
    } catch (error) {
      console.error('Error fetching Supabase projects:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateNewDatabase = async () => {
    setIsDropdownOpen(false);
    setTypeOfRequest('create-database')
    if (hasRefreshToken) {
      setIsLoading(true);
      setCreatingNewDataBase(true)
      try {
        const response = await backendApiFetch('/cloud/supabase/supabase-auth', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            code: '',
            codeVerifier: '',
            projectSlug: projectSlug
          })
        });

        const data = await response.json();

        if (response.ok && data.success) {
          handleSupabaseAuthComplete(data);
          setTypeOfRequest(null)
        } else {
          console.error('Failed to create project:', data);

          const errorMessage = data?.message ?? 'An unexpected error occurred';
          if (errorMessage.includes('PROJECT_LIMIT_REACHED'))
            setErrorSupabaseDialogOpen(true);
          else {
            setError({
              show: true,
              message: errorMessage,
              title: 'Project Creation Error'
            });
          }
        }
      } catch (error) {
        console.error('Error creating project:', error);
        setError({
          show: true,
          message: 'Failed to create project with existing auth',
          title: 'Connection Error'
        });
      } finally {
        setIsLoading(false);
        setCreatingNewDataBase(false)
      }
    } else {
      initOAuthFlow();
    }
  };

  const handleLinkExistingProject = async () => {
    if (!selectedProject || !projectSlug) return;

    setIsLoading(true);
    try {
      const response = await backendApiFetch('/cloud/supabase/link-existing-project', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          existingProjectId: selectedProject.id,
          newProjectSlug: projectSlug
        })
      });

      const data = await response.json();

      if (response.ok) {
        setIsConnected(true);
        setHasRefreshToken(true);
        localStorage.setItem('projectUrl', data.projectUrl);
        localStorage.setItem('annonKey', data.annonKey);
        setProjectId(data.projectId);
        setIsDropdownOpen(false);
        setIsProjectsDropdownOpen(false);

        await fetchDatabaseSchemaAndSendToAI(projectSlug, 'link');

       /* workbenchStore.actionAlert.set({
          type: 'success',
          title: 'Supabase Connected',
          description: 'Successfully linked existing Supabase project',
          content: 'Content',
          source: 'preview',
        });*/
      } else {
        console.error('Failed to link existing project:', data);
      }
    } catch (error) {
      console.error('Error linking existing project:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDuplicateProject = async () => {
    if (!selectedProject || !projectSlug) return;
    setErrorSupabaseDialogOpen(false)
    setIsLoading(true);
    setTypeOfRequest('duplicate-project')
    try {
      const response = await backendApiFetch('/cloud/supabase/duplicate-project', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sourceProjectId: selectedProject.id,
          newProjectSlug: projectSlug,
          includeData: isDuplicateWithData
        })
      });

      const data = await response.json();

      if (response.ok) {
        setTypeOfRequest(null)
        setIsConnected(true);
        setHasRefreshToken(true);
        localStorage.setItem('projectUrl', data.projectUrl);
        localStorage.setItem('annonKey', data.annonKey);
        setProjectId(data.projectId);
        setIsDropdownOpen(false);
        setIsProjectsDropdownOpen(false);

        await fetchDatabaseSchemaAndSendToAI(projectSlug, 'duplicate');

        workbenchStore.actionAlert.set({
          type: 'success',
          title: 'Supabase Connected',
          description: `Successfully duplicated Supabase project ${isDuplicateWithData ? 'with' : 'without'} data`,
          content: 'Content',
          source: 'preview',
        });
      } else {
        const errorMessage = data?.message ?? 'An unexpected error occurred';
        if (errorMessage === 'PROJECT_LIMIT_REACHED')
          setErrorSupabaseDialogOpen(true);
        else {
          setError({
            show: true,
            message: errorMessage,
            title: 'Connection Error'
          });
        }
        console.error('Failed to duplicate project:', data);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      if (errorMessage === 'PROJECT_LIMIT_REACHED')
        setErrorSupabaseDialogOpen(true);
      else {
        setError({
          show: true,
          message: errorMessage,
          title: 'Connection Error'
        });
      }
      console.error('Error duplicating project:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePauseProject = async (project: SupabaseProject) => {
    setPausingProjects(prev => new Set([...prev, project.id]));
    setTypeOfRequest('pause')
    try {
      const response = await backendApiFetch(`/cloud/supabase/project/${project.id}/pause`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
      });

      if (response.ok) {
        // Immediately update the project status in the UI
        setProjects(prevProjects =>
          prevProjects.map(p =>
            p.id === project.id
              ? { ...p, status: 'PAUSING' }
              : p
          )
        );

        // Add to polling projects
        setPollingProjects(prev => new Set([...prev, project.id]));

        workbenchStore.actionAlert.set({
          type: 'success',
          title: 'Project Pausing',
          description: `${project.name} is shutting down...`,
          content: 'Content',
          source: 'preview',
        });

        // Fetch updated project list after a delay
        setTypeOfRequest(null)
        setTimeout(() => {
          fetchSupabaseProjects();
        }, 5000);
      } else {
        const errorData = await response.json();
        setError({
          show: true,
          message: errorData.message || 'Failed to pause project',
          title: 'Error Pausing Project'
        });
      }
    } catch (error) {
      console.error('Error pausing project:', error);
      setError({
        show: true,
        message: 'Failed to pause project',
        title: 'Connection Error'
      });
    } finally {
      setPausingProjects(prev => {
        const newSet = new Set(prev);
        newSet.delete(project.id);
        return newSet;
      });
    }
  };

  const handleRestoreProject = async (project: SupabaseProject) => {
    setPausingProjects(prev => new Set([...prev, project.id]));
    setSpecificProjectId(project?.id ?? null);
    setTypeOfRequest('restore')
    try {
      const response = await backendApiFetch(`/cloud/supabase/project/${project.id}/restore`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
      });

      if (response.ok) {
        // Immediately update the project status in the UI
        setProjects(prevProjects =>
          prevProjects.map(p =>
            p.id === project.id
              ? { ...p, status: 'COMING_UP' }
              : p
          )
        );

        // Add to polling projects
        setPollingProjects(prev => new Set([...prev, project.id]));

        workbenchStore.actionAlert.set({
          type: 'success',
          title: 'Project Restored',
          description: `${project.name} is starting up...`,
          content: 'Content',
          source: 'preview',
        });

        // Fetch updated project list after a delay
        setTypeOfRequest(null)
        setTimeout(() => {
          fetchSupabaseProjects();
        }, 5000); // Check after 5 seconds for initial update
      } else {
        const errorData = await response.json();
        setError({
          show: true,
          message: errorData.message || 'Failed to restore project',
          title: 'Error Restoring Project'
        });
      }
    } catch (error) {
      console.error('Error restoring project:', error);
      setError({
        show: true,
        message: 'Failed to restore project',
        title: 'Connection Error'
      });
    } finally {
      setPausingProjects(prev => {
        const newSet = new Set(prev);
        newSet.delete(project.id);
        return newSet;
      });
    }
  };

  const handleShowProjects = () => {
    setIsProjectsDropdownOpen(!isProjectsDropdownOpen);
    if (!projects.length) {
      fetchSupabaseProjects();
    }
  };

  const onRetryDuplicate = async () => {
    await fetchSupabaseProjects().then(async ()=>{
      setErrorSupabaseDialogOpen(false)
      setError({ show: false, message: '', title: 'Error' });
      if (typeOfRequest === 'duplicate-project') {
        await handleDuplicateProject();
      } else if (typeOfRequest === 'restore' && specificProjectId) {
        const failedProject = projects.find(p => p.id === specificProjectId);
        if (failedProject) await handleRestoreProject(failedProject);
      }
      else if (typeOfRequest === 'pause' && specificProjectId) {
        const failedProject = projects.find(p => p.id === specificProjectId);
        if (failedProject) await handlePauseProject(failedProject);
      }
      else {
        await handleCreateNewDatabase();
      }

      setSpecificProjectId(null);
    });
  }

  // Helper function to check if project is selectable
  const isProjectSelectable = (project: SupabaseProject) => {
    return project.status !== 'INACTIVE' &&
      project.status !== 'COMING_UP' &&
      project.status !== 'PAUSING';
  };

  // Get status display config
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'ACTIVE_HEALTHY':
        return {
          label: 'Active',
          className: 'bg-green-500/20 text-green-400',
          showButtons: true
        };
      case 'INACTIVE':
        return {
          label: 'Paused',
          className: 'bg-gray-500/20 text-gray-400',
          showButtons: true
        };
      case 'COMING_UP':
        return {
          label: 'Coming up',
          className: 'bg-blue-500/20 text-blue-400',
          showButtons: false
        };
      case 'PAUSING':
        return {
          label: 'Pausing',
          className: 'bg-yellow-500/20 text-yellow-400',
          showButtons: false
        };
      default:
        return {
          label: status,
          className: 'bg-gray-500/20 text-gray-400',
          showButtons: false
        };
    }
  };

  const user = UserStore.getInstance().getUser();
  if (!user) return null;

  return (
    <>
      <div className="relative" ref={dropdownRef}>
        <button
          onClick={handleConnect}
          className={`cursor-pointer flex items-center gap-2 px-3 py-1.5 rounded-md text-[12px] xl:text-[14px] font-normal tracking-[0.4px] transition-colors text-white/90 ${isConnected ? 'bg-[#22D3EE]/10' : isDropdownOpen ? 'bg-[#1E293B] border border-[#22D3EE]/30' : 'bg-white/5 hover:bg-white/10 border border-transparent hover:border-[#22D3EE]/20'} ${creatingNewDataBase && 'cursor-not-allowed pointer-events-none'}`}
        >
          {creatingNewDataBase ? (
            <>
              <FaSpinner className="animate-spin text-gray-400 text-sm" />
              <span>{t('CreatingDatabase')}</span>
            </>
          ) : (
            <>
              <CircleStackIcon className={'text-[#22D3EE] w-[16px] h-[16px] '} />
              <span className={`${isConnected && 'text-[#22D3EE] font-light text-sm'}`}>
               {isConnected ? t('DatabaseDashboard') : t('ConnectDatabase')}
              </span>
              {hasRefreshToken && !isConnected && (
                <ChevronDownIcon className={`w-4 h-4 text-[#22D3EE] transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`} />
              )}
            </>
          )}
        </button>

        {/* Dropdown Menu - FIXED POSITION */}
        {isDropdownOpen && hasRefreshToken && !isConnected && (
          <div className="absolute top-0 right-0 w-90 bg-[#0F172A] border border-[#22D3EE]/30 rounded-lg shadow-2xl z-50 overflow-hidden">
            <div className="p-1">
              {/* Header */}
              <div className="px-3 py-2 text-[#22D3EE] border-b border-[#22D3EE]/20 font-light text-md">
                {t('DatabaseConnectionOptions')}
              </div>

              <button
                onClick={handleCreateNewDatabase}
                className="w-full text-left px-3 py-2.5 font-light text-sm text-white hover:bg-[#1E293B] rounded-md flex items-center my-1 transition-all duration-200"
              >
                <CircleStackIcon className="w-5 h-5 mr-2 text-[#22D3EE]" />
                <span>{t('CreateNewSupabaseDB')}</span>
              </button>
              {projects.length > 0 && (
                <button
                  onClick={handleShowProjects}
                  className="w-full text-left px-3 py-2.5  text-white hover:bg-[#1E293B] rounded-md flex items-center justify-between my-1 group transition-all duration-200"
                >
                  <div className="flex items-center font-light text-sm">
                    <CircleStackIcon className="w-5 h-5 mr-2 text-[#22D3EE]" />
                    <span>{t('UseExistingSupabaseDB')}</span>
                  </div>
                  <ChevronDownIcon className={`w-4 h-4 text-[#22D3EE] transition-transform duration-200 ${isProjectsDropdownOpen ? 'rotate-180' : ''}`} />
                </button>
              )}

              {/* Projects Dropdown - MOVED HERE */}
              {isProjectsDropdownOpen && (
                <div className="mx-2 mb-2 bg-[#1E293B]/70 rounded-md overflow-hidden border border-[#22D3EE]/10">
                  {/* Loading State */}
                  {isLoading ? (
                    <div className="px-4 py-3 text-sm text-white flex items-center">
                      <ArrowPathIcon className="w-4 h-4 mr-2 font-light text-sm animate-spin text-[#22D3EE]" />
                      {t('LoadingYourSupabaseProjects')}
                    </div>
                  ) : projects.length > 0 ? (
                    <>
                      {/* Project List */}
                      <div className="max-h-48 overflow-y-auto scrollbar-thin scrollbar-thumb-[#22D3EE]/20 scrollbar-track-transparent">
                        {projects.map((project) => {
                          const statusDisplay = getStatusDisplay(project.status);
                          const isSelectable = isProjectSelectable(project);

                          return (
                            <div
                              key={project.id}
                              className={`w-full text-left px-3 py-2 text-sm font-light flex items-center justify-between transition-all ${
                                isSelectable ? 'hover:bg-[#2D3748]' : 'opacity-60 cursor-not-allowed'
                              } ${
                                selectedProject?.id === project.id ? 'bg-[#22D3EE]/10 text-white' : 'text-gray-300'
                              }`}
                            >
                              <button
                                onClick={() => isSelectable && setSelectedProject(project)}
                                disabled={!isSelectable}
                                className={`flex-grow text-left flex items-center ${
                                  !isSelectable ? 'cursor-not-allowed' : 'cursor-pointer'
                                }`}
                              >
                                <span className="truncate max-w-[180px]">{project.name}</span>
                                {selectedProject?.id === project.id && isSelectable && (
                                  <CheckIcon className="w-4 h-4 text-[#22D3EE] flex-shrink-0 ml-2" />
                                )}
                              </button>

                              <div className="flex items-center gap-2 ml-2">
                                {/* Status indicator */}
                                <span className={`px-2 py-0.5 text-xs rounded-full ${statusDisplay.className}`}>
                                  {statusDisplay.label}
                                </span>

                                {(project.status === 'COMING_UP' || project.status === 'PAUSING' || project.status === 'RESTORING') && (
                                  <div className={'p-1 rounded transition-all opacity-50 cursor-not-allowed'}>
                                    <ArrowPathIcon className="w-4 h-4 animate-spin text-[#22D3EE]" />
                                  </div>
                                )}


                                {/* Pause/Play button - only show if status allows */}
                                {statusDisplay.showButtons && (
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      if (project.status === 'ACTIVE_HEALTHY') {
                                        handlePauseProject(project);
                                      } else if (project.status === 'INACTIVE') {
                                        handleRestoreProject(project);
                                      }
                                    }}
                                    disabled={pausingProjects.has(project.id)}
                                    className={`p-1 rounded transition-all ${
                                      pausingProjects.has(project.id)
                                        ? 'opacity-50 cursor-not-allowed'
                                        : 'hover:bg-[#22D3EE]/20'
                                    }`}
                                    title={project.status === 'ACTIVE_HEALTHY' ? 'Pause project' : 'Resume project'}
                                  >
                                    {pausingProjects.has(project.id) ? (
                                      <ArrowPathIcon className="w-4 h-4 animate-spin text-[#22D3EE]" />
                                    ) : project.status === 'ACTIVE_HEALTHY' ? (
                                      <svg className="w-4 h-4 text-[#22D3EE]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                      </svg>
                                    ) : (
                                      <svg className="w-4 h-4 text-[#22D3EE]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                      </svg>
                                    )}
                                  </button>
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </div>

                      {/* Project Selected Options */}
                      {selectedProject && isProjectSelectable(selectedProject) && (
                        <div className="border-t border-[#22D3EE]/10 p-3 pt-4">

                          <div className="flex flex-col gap-3">
                            {/* Link Option */}
                            <button
                              onClick={handleLinkExistingProject}
                              className="w-full justify-center px-3 py-2 text-sm font-light  text-white bg-[#22D3EE]/20 rounded-md hover:bg-[#22D3EE]/30 transition-colors flex items-center"
                              disabled={isLoading}
                            >
                              <span className="mr-2"><LinkIcon size={16} /></span>
                              <span className="font-light">{t('LinkExistingProject')}</span>
                            </button>
                            <p className={'flex gap-2 items-center'}><span className={'flex-1 h-[1px] bg-gray-500'}></span>{t('Or')}<span className={'flex-1 h-[1px] bg-gray-500'}></span></p>
                            {/* Duplicate Option */}
                            <div className="bg-[#22D3EE]/10 rounded-md p-2 border border-[#22D3EE]/20">
                              <div className="flex items-center mb-2">
                                <div className="relative h-4 w-4 mr-2">
                                  <input
                                    type="checkbox"
                                    id="duplicate-checkbox"
                                    checked={isDuplicateWithData}
                                    onChange={() => setIsDuplicateWithData(!isDuplicateWithData)}
                                    className="opacity-0 absolute h-4 w-4 cursor-pointer z-10"
                                  />
                                  <div className={`border ${isDuplicateWithData ? 'bg-[#22D3EE] border-[#22D3EE]' : 'border-gray-500'} rounded h-4 w-4 flex items-center justify-center transition-colors`}>
                                    {isDuplicateWithData && <CheckIcon className="h-3 w-3 text-[#0F172A]" />}
                                  </div>
                                </div>
                                <label htmlFor="duplicate-checkbox" className="text-sm text-white font-light cursor-pointer select-none">
                                  {t('IncludeDataWhenDuplicating')}
                                </label>
                              </div>
                              <button
                                onClick={handleDuplicateProject}
                                className="w-full px-3 py-2 text-sm font-light bg-[#22D3EE]/20 text-white rounded-md hover:bg-[#22D3EE]/30 transition-colors flex items-center justify-center"
                                disabled={isLoading}
                              >
                                <span className="mr-2"><Copy size={16}/></span>
                                {t('DuplicateProject')}
                              </button>
                            </div>
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="px-4 py-3 text-sm text-gray-300">
                      {t('NoSupabaseProjectsFound')}
                    </div>
                  )}
                </div>
              )}
              <button
                onClick={() => setShowDisconnectModal(true)}
                className="w-full text-left px-3 py-2.5 font-light text-sm text-red-400 hover:bg-[#1E293B] rounded-md flex items-center my-1 transition-all duration-200"
              >
                <CircleStackIcon className="w-5 h-5 mr-2 text-red-400" />
                <span>{t('DisconnectSupabaseDB')}</span>
              </button>
            </div>
          </div>
        )}

        {isDropdownOpen && isConnected && (
          <div className="absolute top-0 right-0 w-90 bg-[#0F172A] border border-[#22D3EE]/30 rounded-lg shadow-2xl z-50 overflow-hidden">
            <div className="p-1">
              <div className="px-3 py-2 text-[#22D3EE] border-b border-[#22D3EE]/20 font-light text-md">
                {t('DatabaseOptions')}
              </div>

              <button
                onClick={() => setShowDisconnectModal(true)}
                className="w-full text-left px-3 py-2.5 font-light text-sm text-red-400 hover:bg-[#1E293B] rounded-md flex items-center my-1 transition-all duration-200"
              >
                <CircleStackIcon className="w-5 h-5 mr-2 text-red-400" />
                <span>{t('DisconnectDatabase')}</span>
              </button>

              <button
                onClick={() => {
                  if (projectId) window.open(`https://supabase.com/dashboard/project/${projectId}/editor`, '_blank');
                  setIsDropdownOpen(false);
                }}
                className="w-full text-left px-3 py-2.5 font-light text-sm text-white hover:bg-[#1E293B] rounded-md flex items-center my-1 transition-all duration-200"
              >
                <CircleStackIcon className="w-5 h-5 mr-2 text-[#22D3EE]" />
                <span>{t('OpenSupabaseDashboard')}</span>
              </button>
            </div>
          </div>
        )}
      </div>

      <ErrorSupabaseDialog
        isOpen={isErrorSupabaseDialogOpen || error.show}
        onEdit={()=>{
          onRetryDuplicate()
        }}
        onClose={() => {
          setErrorSupabaseDialogOpen(false)
          setTypeOfRequest(null)
          setError({ show: false, message: '', title: 'Error' });
        }}
        isLoading={isLoading}
        errorTitle={error.show ? error.title : undefined}
        errorMessage={error.show ? error.message : undefined}
      />

      <DisconnectModal
        isOpen={showDisconnectModal}
        onClose={() => setShowDisconnectModal(false)}
        onConfirm={async () => {
          setShowDisconnectModal(false);
          await handleDisconnect();
        }}
      />
    </>
  );
};

export default SupabaseConnectClient;
