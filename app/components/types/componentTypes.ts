import React, { <PERSON>actN<PERSON>, UIEvent } from 'react';

export interface HeaderProps {
  handleSendMessage?: (event: UIEvent, messageInput?: string) => void;
  isStreaming?: boolean;
  handleStop?: () => void;
  isDashboardPage?: boolean;
  isSettingsPage?: boolean;
  isProfilePage?: boolean;
}

export interface ErrorPageProps {
  title: string;
  description: string;
  buttonText: string;
  buttonLink: string;
  children?: React.ReactNode;
}

export interface BaseIconButtonProps {
  size?: IconSize;
  className?: string;
  iconClassName?: string;
  disabledClassName?: string;
  title?: string;
  disabled?: boolean;
  onClick?: (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
}

export type IconSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';

export type IconButtonProps = {
  icon?: string;
  children?: ReactNode;
} & BaseIconButtonProps;
