site_name: biela.diy Docs
site_dir: ../site
theme:
  name: material
  palette:
    - scheme: default
      toggle:
        icon: material/toggle-switch-off-outline
        name: Switch to dark mode
    - scheme: slate
      toggle:
        icon: material/toggle-switch
        name: Switch to light mode
  features:
    - navigation.tabs
    - navigation.sections
    - toc.follow
    - toc.integrate
    - navigation.top
    - search.suggest
    - search.highlight
    - content.tabs.link
    - content.code.annotation
    - content.code.copy
    # - navigation.instant
    # - navigation.tracking
    # - navigation.tabs.sticky
    # - navigation.expand
    # - content.code.annotate
  icon:
    repo: fontawesome/brands/github
  # logo: assets/logo.png
  # favicon: assets/logo.png
repo_name: biela.diy
edit_uri: ""

extra:
  generator: false
  social:
    - icon: fontawesome/brands/github
      name: biela.diy
    - icon: fontawesome/brands/discourse
      link: https://thinktank.ottomator.ai/
      name: biela.diy Discourse
    - icon: fontawesome/brands/x-twitter
      link: https://x.com/biela_diy
      name: biela.diy on X
    - icon: fontawesome/brands/bluesky
      link: https://bsky.app/profile/biela.diy
      name: biela.diy on Bluesky




markdown_extensions:
  - pymdownx.highlight:
      anchor_linenums: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.arithmatex:
      generic: true
  - footnotes
  - pymdownx.details
  - pymdownx.superfences
  - pymdownx.mark
  - attr_list
