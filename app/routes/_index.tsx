import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ClientOnly from '~/components/common/ClientOnly';
import { BaseChat } from '~/ai/components/BaseChat';
import { Chat } from '~/ai/components/Chat.client';

function formatId(id: string) {
  if (/^\d+$/.test(id)) {
    return id;
  }

  return id
    .split('-')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}



export function ClientChat() {
  const { t } = useTranslation('translation');
  const [resolvedId, setResolvedId] = useState(() => {
    const path = window.location.pathname;
    return path.split('/').pop() ?? '';
  });

  useEffect(() => {
    let currentPath = window.location.pathname;

    const checkUrlChange = () => {
      const newPath = window.location.pathname;

      if (newPath !== currentPath) {
        currentPath = newPath;

        const newId = newPath.split('/').pop() ?? '';
        setResolvedId(newId);
      }
    };

    const interval = setInterval(checkUrlChange, 200);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const formatted = formatId(resolvedId);
    const title = formatted
      ? `${formatted} - biela.dev`
      : 'biela.dev | AI-Powered Web & App Builder – Build with Prompts';

    document.title = title;

    let meta = document.querySelector('meta[name="description"]');

    if (!meta) {
      meta = document.createElement('meta');
      meta.setAttribute('name', 'description');
      document.head.appendChild(meta);
    }

    meta.setAttribute(
      'content',
      t(
        'meta.index.description',
        'Transform your ideas into live websites or apps with biela.dev. Use AI-driven prompts to build custom digital products effortlessly',
      ),
    );
  }, [resolvedId, t]);

  return <Chat />;
}

export default function Page() {
 
  return (
    <div className="flex flex-col h-full w-full bg-[#0A0F1C]">
      <ClientOnly fallback={<BaseChat />}>{() => <ClientChat />}</ClientOnly>
    </div>
  );
}
