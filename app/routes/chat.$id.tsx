import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { BaseChat } from '~/ai/components/BaseChat';
import { Chat } from '~/ai/components/Chat.client';
import { useChatHistory } from '~/ai/lib/persistence/useChatHistory.client';

const ChatId = () => {
  const { id } = useParams<{ id: string }>();
  const { ready, initialMessages } = useChatHistory();
  const [isClient, setIsClient] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    const handler = () => setRefreshKey((k) => k + 1);
    window.addEventListener('biela:refresh-chat', handler);

    return () => window.removeEventListener('biela:refresh-chat', handler);
  }, []);

  return (
    <div className="flex flex-col h-full w-full bg-biela-elements-background-depth-1">
      {isClient && ready ? (
        <Chat id={id} key={id + '-' + initialMessages.length + '-' + refreshKey} />
      ) : (
        <BaseChat />
      )}
    </div>
  );
};

export default ChatId;