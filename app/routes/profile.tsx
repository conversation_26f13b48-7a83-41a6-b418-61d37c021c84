import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useUser } from '~/ai/lib/context/userContext';
import { AnimatePresence, motion } from 'framer-motion';
import { FaCheck, FaTimes } from 'react-icons/fa';
import { backendApiFetch } from '~/ai/lib/backend-api';
import { TooltipProvider } from '@radix-ui/react-tooltip';
import { toast } from 'react-toastify';
import UserInformation from '~/backOffice/components/profile/UserInformation';
import LocationAndSite from '~/backOffice/components/profile/LocationAndSite';
import Wrapper from '~/components/common/Wrapper';

export interface IUserData {
  firstName: string;
  lastName: string;
  username: string;
  phone: string;
  phoneNumber?: string;
  address1: string;
  address2: string;
  city: string;
  zipCode: string;
  preferredLanguage: string;
  country: string;
  referralCode: string;
  referralLink: string;
  email: string;
  website?: string;
  instagram?: string;
  twitter?: string;
  linkedin?: string;
  youtube?: string;
  tiktok?: string;
  facebook?: string;
  profilePicture?: string | File;
}

interface IStatusPopupProps {
  isOpen: boolean;
  type: 'success' | 'error';
  message: string;
  onClose: () => void;
  autoCloseMs?: number;
}

type ReferralResponse = { code: string };

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 0.5 },
  exit: { opacity: 0 },
};

const popupVariants = {
  hidden: { y: -20, opacity: 0 },
  visible: { y: 0, opacity: 1 },
  exit: { y: -20, opacity: 0 },
};

const REFERRAL_URL = 'https://biela.dev';


export default function Dashboard() {
  const [saveStatus, setSaveStatus] = useState<'idle'|'saving'|'success'|'error'>('idle');
  const closeSavePopup = useCallback(() => setSaveStatus('idle'), []);

  const { isLoggedIn } = useUser();
  const navigate = useNavigate();

  const { t, i18n } = useTranslation('profile');

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const urlRegex   = /^(https?:\/\/)?([\w-]+\.)+[\w-]+(\/[\w-]*)*\/?$/i;

  const requiredFields: (keyof IUserData)[] = [
    'firstName',
    'lastName',
    'username',
    'phone',
    'address1',
    'city',
    'zipCode',
    'country',
    'email',
  ];

  const validateField = (
    field: keyof IUserData,
    value: string
  ): string | null => {
    if (!value.trim()) {
      return requiredFields.includes(field)
        ? (t(`${field}Required`, 'Field Required') as string)
        : null;
    }
    if (field === 'email' && !emailRegex.test(value)) {
      return t('EmailInvalid', 'Please enter a valid email address');
    }
    if (
      ['website', 'instagram', 'twitter', 'linkedin', 'facebook', 'youtube', 'tiktok'].includes(field) &&
      value.trim() &&
      !urlRegex.test(value)
    ) {
      return t('URLInvalid', 'Please enter a valid URL');
    }
    return null;
  };

  // Check only the changed values
  const validateAllChangedValues = (): boolean => {
    const changedFields: Partial<IUserData> = getChangedFields();

    for (const key in changedFields) {
      const value         = changedFields[key as keyof IUserData];
      const validationVal = typeof value === 'string' ? value : '';
      const error         = validateField(key as keyof IUserData, validationVal);

      if (error) return false;
    }

    return true;
  };

  // Get the changed fields
  const getChangedFields = (): Partial<IUserData> => {
    const changes: Partial<IUserData> = {};

    for (const key in userData) {
      const keyString = key as keyof IUserData;

      if (typeof userData[keyString] === 'string' && typeof initialUserData[keyString] === 'string') {
        if (userData[keyString] !== initialUserData[keyString]) {
          changes[keyString] = userData[keyString];
        }
      }
    }

    return changes;
  }

  const initializeUserData = (profile: IUserData | null): IUserData => ({
    firstName: profile?.firstName ?? '',
    lastName: profile?.lastName ?? '',
    username: profile?.username ?? '',
    phone: profile?.phone ?? profile?.phoneNumber ?? '',
    address1: profile?.address1 ?? '',
    address2: profile?.address2 ?? '',
    city: profile?.city ?? '',
    zipCode: profile?.zipCode ?? '',
    preferredLanguage: profile?.preferredLanguage ?? 'English',
    country: profile?.country ?? '',
    referralCode: profile?.referralCode ?? '',
    referralLink: profile?.referralLink ?? '',
    email: profile?.email ?? '',
    website: profile?.website ?? '',
    instagram: profile?.instagram ?? '',
    twitter: profile?.twitter ?? '',
    linkedin: profile?.linkedin ?? '',
    youtube: profile?.youtube ?? '',
    tiktok: profile?.tiktok ?? '',
    facebook: profile?.facebook ?? '',
    profilePicture: profile?.profilePicture ?? undefined,
  });

  const [profile, setProfile] = useState<IUserData | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [password, setPassword] = useState('');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [errors, setErrors] = useState<Partial<Record<keyof IUserData, string>>>({});
  const [userData, setUserData] = useState<IUserData>(() => initializeUserData(null));
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [repeatNewPassword, setRepeatNewPassword] = useState('');
  const [isEditingPassword, setIsEditingPassword] = useState(false);
  const [isUpdatingPassword, setIsUpdatingPassword] = useState(false);
  const [passwordErrorMessage, setPasswordErrorMessage] = useState<string | null>(null);

  const [initialUserData, setInitialUserData] = useState<IUserData>(() => initializeUserData(null));
  const [loading, setLoading] = useState(true);
  const { logout } = useUser();

  const fetchProfileData = useCallback(async () => {
    try {
      const response = await backendApiFetch('/user/profile', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user profile');
      }

      const data = await response.json();
      const mappedData = {
        ...data,
        phone: data.phone || data.phoneNumber || '',
      };

      setProfile(mappedData as IUserData);
      setUserData(initializeUserData(mappedData as IUserData));
    } catch (err) {
      console.error('Error fetching profile data:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    void fetchProfileData();
  }, [fetchProfileData]);

  useEffect(() => {
    if (!isLoggedIn()) {
      navigate('/Access-Denied-Page');
    }
  }, [navigate, isLoggedIn]);


  useEffect(() => {
    if (profile) {
      const initialized = initializeUserData(profile);
      setUserData((prev) => ({ ...prev, ...initialized }));

      setContactInfo((prev) => {
        // prevent overwriting if user already started typing
        if (prev.email || prev.phone) return prev;

        const email = initialized.email;
        const phone = initialized.phone;

        setOriginalEmail(email);
        setOriginalPhone(phone);
        setNewEmail(email);
        setNewPhone(phone);

        return { email, phone };
      });
    }
  }, [profile]);


  useEffect(() => {
    if (isEditing) {
      setInitialUserData({ ...userData });
      setHasChanges(false);
    }
  }, [isEditing]);

  useEffect(() => {
    if (isEditing && initialUserData) {
      const hasFieldChanged = Object.entries(userData).some(([key, value]) => {
        if (key === 'referralCode' || key === 'referralLink') {
          return false;
        }

        return value !== initialUserData[key as keyof IUserData];
      });

      setHasChanges(hasFieldChanged);
    }
  }, [userData, initialUserData, isEditing]);

  // Function To Get Referral Code:
  const fetchReferralCode = async () => {
    try {
      const response = await backendApiFetch('/referrals/default', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch referral code: ${errorText}`);
      }

      const data: ReferralResponse = await response.json();

      if (!data?.code) {
        return;
      }

      const referralCode = data.code;
      const referralLink = `${REFERRAL_URL}/?referral=${referralCode}`;

      // Update User Data with ReferralCode:
      setUserData((prev) => ({
        ...prev,
        referralCode,
        referralLink,
      }));

      setProfile((prev) => (prev ? { ...prev, referralCode, referralLink } : prev));
    } catch (error) {
      console.error('Error while getting referral code:', error);
    }
  };

  useEffect(() => {
    if (userData && !userData.referralCode) {
      fetchReferralCode();
    }
  }, [userData]);

  const [contactInfo, setContactInfo] = useState<{ email: string; phone: string }>({ email: '', phone: '' });
  const [originalEmail, setOriginalEmail] = useState(userData.email);
  const [originalPhone, setOriginalPhone] = useState(userData.phone);
  const [newEmail, setNewEmail] = useState(userData.phone);
  const [newPhone, setNewPhone] = useState(userData.phone);
  const [isEmailVerified, setIsEmailVerified] = useState(false);
  const [isPhoneVerified, setIsPhoneVerified] = useState(false);

  const handleChangeContactInfo = (field: keyof IUserData, value: string) => {
    if (field === 'email') {
      setIsEmailVerified(false);
      setNewEmail(value);
    }

    if (field === 'phone') {
      setIsPhoneVerified(false);
      setNewPhone(value);
    }

    setContactInfo((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const [isOTPDialogOpen, setOTPDialogOpen] = useState(false);

  const openOTPDialog = () => setOTPDialogOpen(true);
  const [verificationField, setVerificationField] = useState<'email' | 'phone' | null>(null);

  const handleVerify = async (field: 'email' | 'phone') => {
    try {
      let success = false;

      // Verify the appropriate field (email or phone)
      if (field === 'email') {
        success = await verifyEmail(contactInfo.email);
      } else {
        success = await verifyPhone(contactInfo.phone);
      }

      if (success) {
        // Open OTP dialog and pass the field that is being verified
        openOTPDialog();
        setVerificationField(field); // Set the field being verified
      }
    } catch (error) {
      console.error('Verification error:', error);
    }
  };

  const handleChange = (field: keyof IUserData, value: string) => {
    setUserData((prev) => {
      const updatedData = { ...prev, [field]: value };

      // If the field is 'preferredLanguage', handle language change
      if (field === 'preferredLanguage') {
        i18n
          .changeLanguage(value)
          .then(() => {
            console.log(`Language changed to: ${value}`);
          })
          .catch((error) => {
            console.error('Error changing language:', error);
          });
      }

      return updatedData;
    });
  };

  const getProfilePictureUrl = (profilePicture?: string | File) => {
    if (!profilePicture) {
      return '';
    }

    if (typeof profilePicture === 'string') {
      if (profilePicture.startsWith('http')) {
        return profilePicture;
      }

      return `https://biela-storage-dev.s3.eu-central-1.amazonaws.com/${profilePicture.replace(/^\//, '')}`;
    }

    return '';
  };

  const handleSave = async () => {
    if (!validateAllChangedValues()) return false;

    setSaveStatus('saving');

    try {
      setSaveStatus('saving');
      setErrorMessage(null);

      if (hasChanges) {
        const { profilePicture, ...rest } = userData;
        const response = await backendApiFetch('/user/profile', {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(rest),
        });

        if (!response.ok) {
          throw new Error(await response.text());
        }
        const savedData = await response.json();

        const normalizedData = {
          ...savedData,
          profilePicture: getProfilePictureUrl(savedData.profilePicture),
        };

        setProfile(normalizedData as IUserData);
        setUserData(initializeUserData(normalizedData as IUserData));
        setInitialUserData(initializeUserData(normalizedData as IUserData));

        setIsEditing(false);
        setSaveStatus('success');
      } else {
        setErrors({});
        setIsEditing(false);
        setSaveStatus('idle');
      }
    } catch (error: any) {
      setSaveStatus('error');
      console.error('Eroare la salvare profil:', error);

      let errorData;

      try {
        errorData = JSON.parse(error);
      } catch {
        const content = JSON.parse(error.message || '{}');
        errorData = {
          message: content.message || 'Unknown error',
          error: content.error || 'Error',
          statusCode: content.statusCode || '---',
        };
      }

      toast.error(`${errorData.statusCode} ${errorData.error}: ${errorData.message}`);
    }
  };

  interface ApiResponse {
    message?: string;
    error?: string;
    statusCode?: number;
  }

  const verifyEmail = async (email: string): Promise<boolean> => {
    try {
      const response = await backendApiFetch('/user-verification/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data: ApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Email verification failed');
      }

      toast.success('Email verification sent successfully.');

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      toast.error(errorMessage);

      return false;
    }
  };

  const verifyPhone = async (phone: string): Promise<boolean> => {
    try {
      const response = await backendApiFetch('/user-verification/phone', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phoneNumber: phone }),
      });

      if (!response.ok) {
        throw new Error('Phone verification failed');
      }

      toast.success('Phone verification sent successfully.');

      return true;
    } catch (error) {
      console.error('Error occured:', error);
      toast.error('Failed to send phone verification');

      return false;
    }
  };

  const handleConfirmDeleteAccount = async () => {
    if (!password) {
      return;
    }

    try {
      const response = await backendApiFetch(`/user/delete-account`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password }),
      });

      if (!response.ok) {
        const responseText = await response.text();
        console.error('Error Response: ', responseText);
        throw new Error(responseText);
      }

      setPassword('');
      setOpenDeleteModal(false);
      await logout();
      navigate('/');
    } catch (error: any) {
      let errorData;

      try {
        errorData = JSON.parse(error);
      } catch {
        const content = JSON.parse(error.message);

        errorData = { message: content.message, error: content.error, statusCode: content.statusCode };
      }

      toast.error(`${errorData.statusCode} ${errorData.error}: ${errorData.message}`);
    }
  };

  const getErrorsTranslated = (error: string) => {
    switch (error) {
      case 'New passwords do not match':
        return t('NewPasswordsDoNotMatch', 'New passwords do not match.');
      case 'User not found':
        return t('UserNotFound', 'User not found.');
      case 'Current password is incorrect':
        return t('CurrentPasswordIsIncorrect', 'Current password is incorrect.');
      default:
        return t('ErrorWhileUpdatingPassword', 'Error while updating password');
    }
  };

  const handleUpdatePassword = async () => {
    if (!currentPassword && !newPassword && !repeatNewPassword) {
      setIsEditingPassword(false);
      return;
    }

    if (!currentPassword || !newPassword || !repeatNewPassword) {
      setPasswordSaveStatus('error');
      setPasswordErrorMessage(t('AllPasswordFieldsAreRequired', 'All password fields are required.'));

      return;
    }

    if (newPassword !== repeatNewPassword) {
      setPasswordSaveStatus('error');
      setPasswordErrorMessage(t('NewPasswordsDoNotMatch', 'New passwords do not match'));

      return;
    }

    if (newPassword.length < 8) {
      setPasswordSaveStatus('error');
      setPasswordErrorMessage(
        t('PasswordMustBeAtLeastCharactersLong', 'Password must be at least {{minLength}} characters long', {
          minLength: 8,
        }),
      );

      return;
    }

    setPasswordSaveStatus('saving');
    setIsUpdatingPassword(true);

    try {
      const response = await backendApiFetch('/user/update-password', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword,
          newPassword,
          repeatNewPassword,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t('ErrorWhileUpdatingPassword', 'Error while updating password'));
      }

      setCurrentPassword('');
      setNewPassword('');
      setRepeatNewPassword('');
      setIsEditingPassword(false);

      setPasswordSaveStatus('success');
    } catch (error) {
      console.error('Error updating password:', error);
      setPasswordSaveStatus('error');
      setPasswordErrorMessage(
        error instanceof Error ? getErrorsTranslated(error.message) : t('ErrorWhileUpdatingPassword', 'Error while updating password'),
      );
    } finally {
      setIsUpdatingPassword(false);
    }
  };

  const handleToggleEditPassword = () => {
    setIsEditingPassword(!isEditingPassword);

    if (!isEditingPassword) {
      setCurrentPassword('');
      setNewPassword('');
      setRepeatNewPassword('');
    }
  };

  useEffect(() => {
    document.title = t('meta.profile.title', 'Your Profile – biela.dev');
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        'content',
        t('meta.profile.description', 'View and update your biela.dev account details, manage preferences, and personalize your AI development experience.')
      );
    }
  }, [t]);
  return (
    <Wrapper
      handleSendMessage={(e, messageInput) => console.log('Message sent:', messageInput)}
      isStreaming={false}
      isDashboardPage={false}
      isProfilePage={true}
      handleStop={() => console.log('Stream stopped')}
    >
      {/* // in Dashboard's return() */}
      <StatusPopup
        isOpen={saveStatus === 'success'}
        type="success"
        message={t('updateAccount', 'Your account has been updated successfully!')}
        onClose={closeSavePopup}
        autoCloseMs={3000}
      />

      <StatusPopup
        isOpen={saveStatus === 'error'}
        type="error"
        message={errorMessage || t('failedUpdateAccount', 'Something went wrong. Please try again.')}
        onClose={closeSavePopup}
        autoCloseMs={3000}
      />


      <div
        className="w-full min-h-screen mx-auto mt-[80px]"
        style={{
          backgroundImage: 'url(/hero-bg-shade-empty.png)',
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
        }}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 p-[2rem]">
          <TooltipProvider>
          <UserInformation
            userData={userData}
            handleChange={handleChange}
            handleBlur={(field) => {
              const err = validateField(field, String(userData[field] || ''));
              setErrors(prev => ({ ...prev, [field]: err }));
            }}
            errors={errors}
            isEditing={isEditing}
            setIsEditing={setIsEditing}
            handleSave={handleSave}
            loading={loading}
            contactInfo={contactInfo}
            newPhone={newPhone}
            newEmail={newEmail}
            originalEmail={originalEmail}
            originalPhone={originalPhone}
            isPhoneVerified={isPhoneVerified}
            setIsPhoneVerified={setIsPhoneVerified}
            isEmailVerified={isEmailVerified}
            setIsEmailVerified={setIsEmailVerified}
            verificationField={verificationField}
            setVerificationField={setVerificationField}
            handleChangeContactInfo={handleChangeContactInfo}
            handleVerify={handleVerify}
            isOTPDialogOpen={isOTPDialogOpen}
            setOTPDialogOpen={setOTPDialogOpen}
          />
          </TooltipProvider>
          <LocationAndSite
            userData={userData}
            handleChange={handleChange}
            handleBlur={(field) => {
              const err = validateField(field, String(userData[field] || ''));
              setErrors(prev => ({ ...prev, [field]: err }));
            }}
            errors={errors}
            handleSave={handleSave}
            isEditing={isEditing}
            setOpenDeleteModal={setOpenDeleteModal}
            openDeleteModal={openDeleteModal}
            password={password}
            setPassword={setPassword}
            handleConfirmDeleteAccount={handleConfirmDeleteAccount}
            currentPassword={currentPassword}
            setCurrentPassword={setCurrentPassword}
            newPassword={newPassword}
            setNewPassword={setNewPassword}
            repeatNewPassword={repeatNewPassword}
            setRepeatNewPassword={setRepeatNewPassword}
            isEditingPassword={isEditingPassword}
            isUpdatingPassword={isUpdatingPassword}
            handleToggleEditPassword={handleToggleEditPassword}
            handleUpdatePassword={handleUpdatePassword}
          />
        </div>
      </div>
    </Wrapper>
  );
}


export const StatusPopup: React.FC<IStatusPopupProps & { errorMessage?: string }> = ({ isOpen, type, message, onClose, autoCloseMs = 3000, }) => {
  const isSuccess = type === 'success';
  const { t } = useTranslation('profile');

  useEffect(() => {
    if (!isOpen || autoCloseMs <= 0) return;
    const timer = setTimeout(onClose, autoCloseMs);
    return () => clearTimeout(timer);
  }, [isOpen, autoCloseMs, onClose]);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
          initial="hidden"
          animate="visible"
          exit="exit"
        >
          {/* Overlay */}
          <motion.div className="absolute inset-0 bg-black" variants={overlayVariants} onClick={onClose} />

          {/* Popup Content */}
          <motion.div
            className="relative bg-gradient-to-r from-[#1f293780] to-[#11182780] p-6 shadow-md z-10 flex flex-col items-center rounded-2xl max-w-md w-full overflow-hidden"
            variants={popupVariants}
            transition={{ duration: 0.4 }}
          >
            {/* Icon Circle */}
            <div
              className={`w-16 h-16 ${isSuccess ? 'bg-green-600' : 'bg-red-600'} rounded-full flex items-center justify-center mb-4 text-white`}
            >
              {isSuccess ? <FaCheck className="w-10 h-10" /> : <FaTimes className="w-10 h-10" />}
            </div>

            {/* Title */}
            <h2 className={`text-2xl font-bold mb-2 ${isSuccess ? 'text-green-600' : 'text-red-600'}`}>
              {isSuccess ? t('Success', 'Success') : t('Error', 'Error')}
            </h2>

            {/* Message */}
            <p className="text-center text-white mb-6">{message}</p>

            {/* Action Button */}
            <button
              onClick={onClose}
              className={`px-6 py-2 text-white rounded-full font-semibold transition-colors ${
                isSuccess ? 'bg-green-500 hover:bg-green-600' : 'bg-red-500 hover:bg-red-600'
              }`}
            >
              {t('Close', 'Close')}
            </button>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
