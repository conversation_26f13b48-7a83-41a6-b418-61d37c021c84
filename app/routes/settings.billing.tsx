import { useTranslation } from 'react-i18next';
import React, { useState } from 'react';
import { PlusCircle } from 'lucide-react';
import { InvoicePDF } from '~/backOffice/components/billings/InvoicePDF';
import { FaCcVisa, FaCcMastercard, FaCcAmex, FaCcDiscover, FaCcDinersClub, FaCcJcb } from 'react-icons/fa';
import { BillingHeader } from '~/backOffice/components/billings/components/BillingHeader';
import { BillingToggle } from '~/backOffice/components/billings/components/BillingToggle';
import { PlansList } from '~/backOffice/components/billings/components/PlansList';
import { RecentInvoices } from '~/backOffice/components/billings/components/RecentInvoices';
import TokenCalculator from '~/backOffice/components/billings/components/TokenCalculator';
import InvoicePreviewModal from '~/backOffice/components/settings/BillingSettings/InvoicePreviewModal';
import ThankYouModal from '~/backOffice/components/billings/components/ThankYouModal';
import { useScreenSize } from '~/backOffice/components/billings/utils/useScreenSize';
import { usePaymentFlow } from '~/backOffice/components/billings/utils/usePaymentFlow';
import { useBillingData } from '~/backOffice/components/billings/utils/useBillingData';

export const SettingsBilling: React.FC = () => {
  const { t } = useTranslation('profile');
  const { isMobile } = useScreenSize();
  const [notLoadedInvoices, setNotLoadedInvoices] = useState<boolean>(true);

  const { billingCycle, setBillingCycle, stripePlans, plansLoading, activeTopUp, loadingTopups } = useBillingData();

  const {
    showInvoiceModal,
    setShowInvoiceModal,
    showThankYouModal,
    setShowThankYouModal,
    showTokenCalculator,
    setShowTokenCalculator,
    selectedInvoice,
    setSelectedInvoice,
    receiptData,
    handleTokenPurchase,
  } = usePaymentFlow({ loadingTopups, plansLoading, notLoadedInvoices });

  return (
    <div className="container mx-auto px-4 py-4 sm:px-8 sm:py-8 max-w-[1600px] rounded-xl">
      <div className="text-white space-y-12">
        <BillingHeader />

        <div className="flex items-center justify-center gap-4 mb-8 sm:flex-row flex-col">
          <BillingToggle stripePlans={stripePlans} billingCycle={billingCycle} setBillingCycle={setBillingCycle} />

          <TokenCalculatorButton
            onClick={() => setShowTokenCalculator(true)}
            label={t('buttonNeedMoreTokens', 'Need more tokens?')}
          />
        </div>

        <PlansList plans={stripePlans} loading={plansLoading} isMobile={isMobile} billingCycle={billingCycle} />

        <PaymentMethodIcons />

        <RecentInvoices
          setSelectedInvoice={setSelectedInvoice}
          setShowInvoiceModal={setShowInvoiceModal}
          setNotLoadedInvoices={setNotLoadedInvoices}
        />

        <TokenCalculator
          showTokenCalculator={showTokenCalculator}
          setShowTokenCalculator={setShowTokenCalculator}
          activeTopUp={activeTopUp}
          onPurchase={handleTokenPurchase}
        />
      </div>

      <ThankYouModal isOpen={showThankYouModal} onClose={() => setShowThankYouModal(false)} receiptData={receiptData} />

      <InvoicePreviewModal
        isOpen={showInvoiceModal}
        onClose={() => setShowInvoiceModal(false)}
        invoiceData={selectedInvoice ? <InvoicePDF invoiceData={selectedInvoice} /> : null}
      />
    </div>
  );
};

const TokenCalculatorButton: React.FC<{ onClick: () => void; label: string }> = ({ onClick, label }) => (
  <button
    onClick={onClick}
    className="group flex items-center gap-2 px-4 py-2 rounded-full bg-gray-800/30 hover:bg-gray-700/50 border border-green-500/20 hover:border-green-500/40 transition-all duration-300"
  >
    <span className="text-gray-300 group-hover:text-white transition-colors">{label}</span>
    <PlusCircle className="w-5 h-5 text-green-500 group-hover:scale-110 transition-all" />
  </button>
);

const PaymentMethodIcons: React.FC = () => (
  <div className="grid grid-cols-6 gap-4 w-max mx-auto">
    <FaCcVisa className="text-2xl" />
    <FaCcMastercard className="text-2xl" />
    <FaCcAmex className="text-2xl" />
    <FaCcDiscover className="text-2xl" />
    <FaCcDinersClub className="text-2xl" />
    <FaCcJcb className="text-2xl" />
  </div>
);

export default SettingsBilling;
