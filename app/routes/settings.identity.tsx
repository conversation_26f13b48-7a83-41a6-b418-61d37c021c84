import { useEffect, useState } from 'react';
import { backendApiFetch } from '~/ai/lib/backend-api';
import LoadingIdentity from '~/backOffice/components/settings/IdentitySettings/LoaderIdentity';
import IdentitySettings from '~/backOffice/components/settings/IdentitySettings/identitySettings';

export interface CustomerData {
  card: string;
  isVerified: boolean;
}

function SettingsIdentity() {
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [isAnimationComplete, setIsAnimationComplete] = useState(false);
  const [shouldStartExit, setShouldStartExit] = useState(false);
  const [customerData, setCustomerData] = useState<CustomerData>({
    card: '',
    isVerified: false,
  });

  const handleLoadingComplete = () => {
    setIsAnimationComplete(true);
  };

  useEffect(() => {
    const fetchIdentityData = async () => {
      try {
        const response = await backendApiFetch(`/payments/stripe/identity-data`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch identity data');
        }

        const data = await response.json();
        setCustomerData(data as CustomerData);
        setShouldStartExit(true);
      } catch (error) {
        console.error('Error fetching identity data:', error);
        setShouldStartExit(true);
      }
    };

    void fetchIdentityData();
  }, []);

  return (
    <>
      {isInitialLoading && !isAnimationComplete ? (
        <div className="min-h-[400px] flex items-center justify-center">
          <div className="w-[200px] h-[200px] flex items-center justify-center relative">
            <LoadingIdentity
              onLoadingComplete={handleLoadingComplete}
              shouldStartExit={shouldStartExit}
              message={'Loading your Identity...'}
            />
          </div>
        </div>
      ) : (
        <IdentitySettings customerData={customerData} />
      )}
    </>
  );
}

export default SettingsIdentity;
