import { Outlet } from 'react-router-dom';
import Wrapper from '~/components/common/Wrapper';
import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import SettingsIdentity from '~/routes/settings.identity';
import SettingsBilling from '~/routes/settings.billing';
import '../components/styles/spacelogo.css';
import { SettingsNav } from '~/backOffice/components/settings/SettingsNav';

export default function Settings() {
  const { t } = useTranslation('translation');
  const [activeTab, setActiveTab] = useState<string>('billing');

  const tabs = [
    { id: 'billing', label: t('settings.tabs.billing') },
    // { id: 'identity', label: t('settings.tabs.identity', 'Identity') },
    // { id: 'profile', label: t('settings.tabs.profile') },
    // { id: 'deployment', label: t('settings.tabs.deployment') },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'billing':
        return <SettingsBilling />;
      // case 'profile':
      //   return <SettingsProfile />;
      // case 'deployment':
      //   return <SettingsDeployment />;
      // case 'identity':
      //   return <SettingsIdentity />;
      default:
        return <SettingsIdentity />;
    }
  };
  useEffect(() => {
    document.title = t('meta.billing.title') || 'Billing – biela.dev';
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        'content',
        t('meta.billing.description') ||
          'View and update your biela.dev account details, manage preferences, and personalize your AI development experience.Add your credit card to verify your identity and unlock full access to biela.dev features—no charges will be made.',
      );
    }
  }, [t]);
  return (
    <Wrapper
      handleSendMessage={(e, messageInput) => console.log('Message sent:', messageInput)}
      isStreaming={false}
      isSettingsPage={true}
      handleStop={() => console.log('Stream stopped')}
    >
      <div
        className="w-full min-h-screen mx-auto bg-[#0B0E14] text-white mt-20"
        style={{
          backgroundImage: 'url(/hero-bg-shade-empty.png)',
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
        }}
      >
        <div>
          <div className="p-8">
            <div>
              <div className="flex gap-8 border-b border-gray-800">
                {tabs.map((tab) => {
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`pb-4 font-light transition-colors bg-transparent ${
                        activeTab === tab.id
                          ? 'text-green-400 border-b-2 border-green-400'
                          : 'text-gray-400 hover:text-gray-300'
                      }`}
                    >
                      {tab.label}
                    </button>
                  );
                })}
              </div>

              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="mt-6"
              >
                {renderContent()}
              </motion.div>
            </div>
            <main className="mt-8">
              <Outlet />
            </main>
          </div>
        </div>
      </div>
    </Wrapper>
  );
}
