export interface Image {
  id: string;
  userId: string;
  title: string;
  url: string;
  folderId: string;
  tags: string[];
  dimensions: string;
  size: number;
  type: string;
  folderPath: string[];
  createdAt: string;
  updatedAt: string;
  thumbnailUrl?: string;
}

export interface Folder {
  id: string;
  userId: string;
  name: string;
  parentId: string | null;
  count: number;
  isRoot: boolean;
  path: string[];
  createdAt: string;
  updatedAt: string;
}

export interface Tag {
  id: string;
  userId: string;
  name: string;
  createdAt: string;
}

export interface Filters {
  dateFrom: string;
  dateTo: string;
  tags: string[];
  searchTerm: string;
}

export interface UploadFile {
  file: File;
  name: string;
  title: string;
  size: number;
  type: string;
  dimensions: string;
  url: string;
  tags: string[];
  thumbnailUrl?: string;
}