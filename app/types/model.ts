export interface TranslationObject {
  key: string;
  defaultValue: string;
}

export interface ModelFeature {
  key: string;
  defaultValue: string;
}

export interface Model {
  id: string;
  value: string;
  name: string;
  description: TranslationObject;
  features: ModelFeature[];
  performance: TranslationObject;
  cost: TranslationObject;
  icon: string;
  color: string;
  isContextAlternative?: boolean;
  contextWindowInfo: TranslationObject;
  supportsExtendedThinking?: boolean | 'always' | 'forced';
}
