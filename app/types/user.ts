export interface UserSession {
  id: string;
  username: string;
  email: string;
  token?: string; // short-lived token
  refreshToken: string; // long-lived token, used to get new token,

  // expiration data
  tokenExpiresAt: Date;
  refreshTokenExpiresAt: Date;

  profilePicture?: string;
}

export type UserContextType = {
  user: UserSession | null;
  getUser: () => UserSession | null;
  getToken: () => string | null;
  setUser: (user: UserSession | null) => void;
  setGoogleState: (state: string | null) => void;
  getGoogleState: () => string | null;
  logout: () => Promise<void>;
  login: (data: UserLoginContextType, turnstileToken: string) => Promise<void>;
  githubLogin: (code: string) => Promise<void>;
  paypalLogin: (code: string) => Promise<void>;
  googleLogin: (code: string, state: string) => Promise<void>;
  confirmEmail: (token: string) => Promise<void>;
  isLoggedIn: () => boolean;
  authenticateToken: () => Promise<void>;
  resendConfirmation: (usernameOrEmail: string) => Promise<void>;
};

export type UserLoginContextType = {
  username: string;
  password: string;
};

export type UserSignupContextType = {
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  phoneNumber: string;
  password: string;
  confirmPassword: string;
  referral?: string;
  acceptTerms: boolean;
};

export const expirationSettings = {
  tokenExpirationMinutes: 14,
  refreshTokenExpirationDays: 7,
};
