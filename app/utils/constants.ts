import type { Template } from '~/types/template';
import type { Model } from '~/types/model';

export const WORK_DIR_NAME = 'project';
export const WORK_DIR = `/home/<USER>
export const MODIFICATIONS_TAG_NAME = 'biela_file_modifications';
export const MODEL_REGEX = /^\[Model: (.*?)\]\n\n/;
export const PROVIDER_REGEX = /\[Provider: (.*?)\]\n\n/;
export const PROMPT_COOKIE_KEY = 'cachedPrompt';
export const AI_MODEL_STORAGE_KEY = 'bielaSelectedModel';

// Starter Templates
export const STARTER_TEMPLATES: Template[] = [
  {
    name: 'biela-astro-basic',
    label: 'Astro Basic',
    description: 'Lightweight Astro starter template for building fast static websites',
    githubRepo: 'thecodacus/biela-astro-basic-template',
    tags: ['astro', 'blog', 'performance'],
    icon: 'i-biela:astro',
  },
  {
    name: 'biela-nextjs-shadcn',
    label: 'Next.js with shadcn/ui',
    description: 'Next.js starter fullstack template integrated with shadcn/ui components and styling system',
    githubRepo: 'thecodacus/biela-nextjs-shadcn-template',
    tags: ['nextjs', 'react', 'typescript', 'shadcn', 'tailwind'],
    icon: 'i-biela:nextjs',
  },
  {
    name: 'biela-qwik-ts',
    label: 'Qwik TypeScript',
    description: 'Qwik framework starter with TypeScript for building resumable applications',
    githubRepo: 'thecodacus/biela-qwik-ts-template',
    tags: ['qwik', 'typescript', 'performance', 'resumable'],
    icon: 'i-biela:qwik',
  },
  {
    name: 'biela-remix-ts',
    label: 'Remix TypeScript',
    description: 'Remix framework starter with TypeScript for full-stack web applications',
    githubRepo: 'thecodacus/biela-remix-ts-template',
    tags: ['remix', 'typescript', 'fullstack', 'react'],
    icon: 'i-biela:remix',
  },
  {
    name: 'biela-slidev',
    label: 'Slidev Presentation',
    description: 'Slidev starter template for creating developer-friendly presentations using Markdown',
    githubRepo: 'thecodacus/biela-slidev-template',
    tags: ['slidev', 'presentation', 'markdown'],
    icon: 'i-biela:slidev',
  },
  {
    name: 'biela-sveltekit',
    label: 'SvelteKit',
    description: 'SvelteKit starter template for building fast, efficient web applications',
    githubRepo: 'biela-sveltekit-template',
    tags: ['svelte', 'sveltekit', 'typescript'],
    icon: 'i-biela:svelte',
  },
  {
    name: 'vanilla-vite',
    label: 'Vanilla + Vite',
    description: 'Minimal Vite starter template for vanilla JavaScript projects',
    githubRepo: 'thecodacus/vanilla-vite-template',
    tags: ['vite', 'vanilla-js', 'minimal'],
    icon: 'i-biela:vite',
  },
  {
    name: 'biela-vite-react',
    label: 'React + Vite + typescript',
    description: 'React starter template powered by Vite for fast development experience',
    githubRepo: 'thecodacus/biela-vite-react-ts-template',
    tags: ['react', 'vite', 'frontend'],
    icon: 'i-biela:react',
  },
  {
    name: 'biela-vite-ts',
    label: 'Vite + TypeScript',
    description: 'Vite starter template with TypeScript configuration for type-safe development',
    githubRepo: 'thecodacus/biela-vite-ts-template',
    tags: ['vite', 'typescript', 'minimal'],
    icon: 'i-biela:typescript',
  },
  {
    name: 'biela-vue',
    label: 'Vue.js',
    description: 'Vue.js starter template with modern tooling and best practices',
    githubRepo: 'thecodacus/biela-vue-template',
    tags: ['vue', 'typescript', 'frontend'],
    icon: 'i-biela:vue',
  },
  {
    name: 'biela-angular',
    label: 'Angular Starter',
    description: 'A modern Angular starter template with TypeScript support and best practices configuration',
    githubRepo: 'thecodacus/biela-angular-template',
    tags: ['angular', 'typescript', 'frontend', 'spa'],
    icon: 'i-biela:angular',
  },
];

export const AI_MODELS: Model[] = [
  {
    id: 'claude-4',
    value: 'anthropic/claude-sonnet-4',
    name: 'Claude 4 Sonnet',
    description: {
      key: 'Claude4Description',
      defaultValue: 'Most powerful AI for premium websites',
    },
    features: [
      { key: 'Claude4FeatureFirst', defaultValue: 'High-quality code' },
      { key: 'Claude4FeatureSecond', defaultValue: 'Advanced functionality' },
      { key: 'Claude4FeatureThird', defaultValue: 'Superior design' },
    ],
    performance: {
      key: 'Claude4Performance',
      defaultValue: 'Excellent',
    },
    cost: {
      key: 'Claude4Cost',
      defaultValue: 'Premium',
    },
    icon: '⭑',
    color: '#4ADE80',
    contextWindowInfo: {
      key: 'Claude4ContextWindow',
      defaultValue: 'Standard context window (200K tokens)',
    },
    supportsExtendedThinking: true,
  },
  {
    id: 'gemini-2-5-pro',
    value: 'google/gemini-2.5-pro-preview',
    name: 'Gemini 2.5 Pro',
    description: {
      key: 'GeminiProDescription',
      defaultValue: 'Versatile AI with wide context',
    },
    features: [
      { key: 'GeminiProFeatureFirst', defaultValue: 'Reliable code output' },
      { key: 'GeminiProFeatureSecond', defaultValue: 'Handles complex inputs' },
      { key: 'GeminiProFeatureThird', defaultValue: 'Good design quality' },
    ],
    performance: {
      key: 'GeminiProPerformance',
      defaultValue: 'Very good',
    },
    cost: {
      key: 'GeminiProCost',
      defaultValue: 'Mid-premium',
    },
    icon: '❖',
    color: '#FACC15',
    isContextAlternative: true,
    contextWindowInfo: {
      key: 'GeminiProContextWindow',
      defaultValue: 'Large context window (1M+ tokens)',
    },
    supportsExtendedThinking: 'forced',
  },
  {
    id: 'gemini-2-5-flash',
    value: 'google/gemini-2.5-flash-preview',
    name: 'Gemini 2.5 Flash',
    description: {
      key: 'GeminiFlashDescription',
      defaultValue: 'Faster, cost-effective option',
    },
    features: [
      { key: 'GeminiFlashFeatureFirst', defaultValue: 'Quick generation' },
      { key: 'GeminiFlashFeatureSecond', defaultValue: 'Basic functionality' },
      { key: 'GeminiFlashFeatureThird', defaultValue: 'Simple designs' },
    ],
    performance: {
      key: 'GeminiFlashPerformance',
      defaultValue: 'Good',
    },
    cost: {
      key: 'GeminiFlashCost',
      defaultValue: 'Budget-friendly',
    },
    icon: '✦',
    color: '#60a5fa',
    isContextAlternative: true,
    contextWindowInfo: {
      key: 'GeminiFlashContextWindow',
      defaultValue: 'Large context window (1M+ tokens)',
    },
    supportsExtendedThinking: false,
  },
];

export const DEFAULT_MODEL = AI_MODELS[0];
export const CONTEXT_ALTERNATIVE_MODELS = AI_MODELS.filter((model) => model.isContextAlternative);
export const BIG_CONTEXT_WINDOW = 'BIG_CONTEXT_WINDOW';
