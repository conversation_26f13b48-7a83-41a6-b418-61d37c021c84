/**
 * Utility functions for country detection
 */

interface IPApiResponse {
  country_name: string;
  country_code: string;
  city: string;
  region: string;
}

/**
 * Detects user's country by IP using the ipapi.co service
 * @returns {Promise<string|null>} The detected country name or null if detection failed
 */
export const detectCountryByIP = async (): Promise<string | null> => {
  try {
    const response = await fetch('https://ipapi.co/json/');

    if (!response.ok) {
      throw new Error('Failed to detect country');
    }

    const data = (await response.json()) as IPApiResponse;

    if (data && data.country_name) {
      return data.country_name;
    }

    return null;
  } catch (error) {
    console.error('Error detecting country:', error);
    return null;
  }
};

/**
 * Validates if a country name exists in the provided country list
 * @param countryName The country name to validate
 * @param countryList List of countries to check against
 * @returns {string|null} The validated country name or null if not found
 */
export const validateCountryName = (
  countryName: string,
  countryList: Array<{ name: string; flag: string; code: string }>,
): string | null => {
  if (!countryName) {
    return null;
  }

  const countryExists = countryList.some((c) => c.name === countryName);

  if (countryExists) {
    return countryName;
  }

  return null;
};
