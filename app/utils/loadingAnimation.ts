export function loadingFunctionBiela(isLoadingComplete: () => boolean): void {
  const logo = document.getElementById('biela-logo') as HTMLElement | null;
  const throttleLines = document.querySelectorAll<SVGPathElement>('.throttle-line');
  const stars = document.querySelectorAll<SVGPathElement>('.star');
  const circleElements = document.querySelectorAll<SVGPathElement>('.circle-element path');
  const bLetter = document.querySelector<SVGPathElement>('.b-letter');
  const trail = document.querySelector<HTMLElement>('.trail');

  // Get progress elements
  const progressFill = document.querySelector<HTMLElement>('.progress-fill');
  const progressPercentage = document.querySelector<HTMLElement>('.progress-percentage');
  const progressStatus = document.querySelector<HTMLElement>('.progress-status');
  const continueButton = document.querySelector<HTMLElement>('.continue-button');

  // Ensure required elements exist
  if (!logo || !progressFill || !progressPercentage || !progressStatus || !trail) {
    console.warn('Some elements required for the loading animation are missing.');
    return;
  }

  // Set initial states
  throttleLines.forEach((line) => {
    line.style.strokeDasharray = '1000';
    line.style.strokeDashoffset = '1000';
    line.style.opacity = '0.3';
  });

  let progress = 0;
  const loopDuration = 2000; // 3 seconds for the loop phase
  const finalDuration = 1000; // 2 seconds for the final phase
  const updateInterval = 50; // Update every 50ms
  const loopIncrement = (updateInterval / loopDuration) * 60; // Increment for 0% to 60%
  const finalIncrement = (updateInterval / finalDuration) * 60; // Increment for 60% to 100%

  let isFinalPhase = false;

  const loadingInterval = setInterval(() => {
    if (isFinalPhase) {
      // Final phase: progress from 60% to 100%
      progress += finalIncrement;
  
      if (progress >= 100) {
        progress = 100;
        clearInterval(loadingInterval);
        completeLoading();
      }
    } else {
      // Loop phase: progress from 0% to 60% and reset
      progress += loopIncrement;
  
      if (progress >= 60) {
        progress = 0; // Reset to 0 to restart the loop
      }
  
      // Check if loading is complete
      if (isLoadingComplete()) {
        isFinalPhase = true;
        progress = 60; // Start final phase from 60%
      }
    }
  
    // Update progress bar
    progressFill.style.width = `${progress}%`;
    progressPercentage.textContent = `${Math.round(progress)}%`;
  
    // Update loading status text
    if (progress < 25) {
      progressStatus.textContent = 'Initializing...';
    } else if (progress < 50) {
      progressStatus.textContent = 'Loading assets...';
    } else if (progress < 75) {
      progressStatus.textContent = 'Preparing interface...';
    } else {
      progressStatus.textContent = 'Almost ready...';
    }
  
    // Sync logo animation with progress
    syncLogoWithProgress(progress);
  }, updateInterval);

  function syncLogoWithProgress(progress: number): void {
    // Animate stars with random delays
    stars.forEach((star) => {
      if (progress > 10 && !star.style.animation) {
        const delay = Math.random() * 2;
        const duration = 1 + Math.random() * 2;
        star.style.animation = `starTwinkle ${duration}s ease-in-out ${delay}s infinite`;
      }
    });

    // Animate circle elements
    circleElements.forEach((circle, index) => {
      if (progress < 60) {
        if (!circle.classList.contains('circle-fade')) {
          circle.style.animationDelay = `${index * 0.2}s`;
          circle.classList.add('circle-fade');
        }
      } else {
        circle.classList.remove('circle-fade');
        circle.style.animationDelay = '';
      }
    });

    // Animate throttle lines
    if (progress > 30 && throttleLines[0].style.animation === '') {
      throttleLines.forEach((line, index) => {
        setTimeout(() => {
          line.style.animation = `throttle 1.5s ease-in-out forwards`;
        }, 200 * index);
      });
    }

    // Pulse effect on the B letter
    if (progress > 50 && bLetter && bLetter.style.animation === '') {
      bLetter.style.animation = 'pulse 0.8s ease-in-out infinite';

      // Create throttle loading effect
      createThrottleLoadingEffect();
    }

    // Vibration and glow effects
    if (progress > 60) {
      const vibrationIntensity = (progress - 60) / 10; // Scale from 0 to 4
      const randomX = (Math.random() - 0.5) * vibrationIntensity;
      const randomY = (Math.random() - 0.5) * vibrationIntensity;
      logo.style.transform = `rotate(-72deg) translate(${randomX}px, ${randomY}px)`;
    }

    // Add glow effect that intensifies with progress
    if (progress > 40) {
      const glowIntensity = (progress - 40) / 15; // Scale from 0 to 4
      circleElements.forEach((circle) => {
        circle.style.filter = `drop-shadow(0 0 ${glowIntensity * 5}px rgba(74, 222, 128, 0.8))`;
      });
    }
  }

  function createThrottleLoadingEffect(): void {
    // Simulate throttle loading with vibration and glow
    let intensity = 0;
    const maxIntensity = 5;
    const interval = setInterval(() => {
      if (intensity >= maxIntensity) {
        clearInterval(interval);
        return;
      }

      // Increase vibration intensity
      const randomX = (Math.random() - 0.5) * intensity;
      const randomY = (Math.random() - 0.5) * intensity;

      // Apply vibration without rotation
      logo.style.transform = `rotate(-72deg) translate(${randomX}px, ${randomY}px)`;

      // Apply glow directly to circle elements
      circleElements.forEach((circle) => {
        circle.style.filter = `drop-shadow(0 0 ${intensity * 3}px rgba(74, 222, 128, 0.8))`;
      });

      // Also apply glow to stars for additional effect
      stars.forEach((star) => {
        star.style.filter = `drop-shadow(0 0 ${intensity * 2}px rgba(74, 222, 128, 0.8))`;
      });

      intensity += 0.5;
    }, 100);
  }

  function completeLoading(): void {
    // Update status text
    progressStatus.textContent = 'Ready!';

    // Create trail effect
    createTrailEffect();

    // Show continue button
    if (continueButton) {
      setTimeout(() => {
        continueButton.classList.add('visible');
      }, 1000);
    }

    // Add fly-away animation
    logo.style.animation = 'flyAway 1s ease-out forwards';
  }

  function createTrailEffect(): void {
    // Create a trail effect when the logo completes loading
    trail.style.left = '60%';
    trail.style.top = '60%';
    trail.style.animation = 'trailEffect 1s ease-out forwards';
  }
}