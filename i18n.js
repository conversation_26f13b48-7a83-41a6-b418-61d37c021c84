// i18n.js
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import HttpApi from 'i18next-http-backend';
import LanguageDetector from 'i18next-browser-languagedetector';

const version = '1.0.0';

const supportedLngs = ['en', 'es', 'fr', 'de', 'az', 'ru', 'ar', 'it', 'pt', 'ro', 'zh', 'ja', 'ko'];

i18n
  .use(HttpApi)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    supportedLngs,
    fallbackLng: 'en',
    debug: false,
    interpolation: {
      escapeValue: false,
    },
    detection: {
      order: ['localStorage', 'navigator'],
      lookupLocalStorage: 'i18nextLng',
      caches: ['localStorage'],
    },
    ns: [],
    defaultNS: 'translation',
    backend: {
      loadPath: `/locales/{{lng}}/{{ns}}.json?v=${version}`,
      cache: false,
    },
    react: {
      wait: true,
      useSuspense: false,
    },
  });

export default i18n;
