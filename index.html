<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>biela.dev</title>
    <link rel="stylesheet" href="/css/ReactToastify.css" />
    <link rel="stylesheet" href="/css/tailwind-compat.css" />
    <link rel="stylesheet" href="/css/xterm.css" />
    <link rel="stylesheet" href="/app/components/styles/index.scss" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous" />
    <link rel="icon" type="image/svg+xml" href="/biela_favicon_light.svg" media="(prefers-color-scheme: light)" />
    <link rel="icon" type="image/svg+xml" href="/biela_favicon_dark.svg" media="(prefers-color-scheme: dark)" />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
    />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Rexton:wght@400;500;600&display=swap" />
    <script>
      function setTutorialKitTheme() {
        let theme = localStorage.getItem('biela_theme') ?? localStorage.getItem('data-theme');
        if (!theme) {
          theme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'dark';
        }
        document.querySelector('html')?.setAttribute('data-theme', theme);
      }
      setTutorialKitTheme();
    </script>
   
    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/app/root.tsx"></script>
  </body>
</html>
