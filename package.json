{"name": "biela.dev", "description": "", "private": true, "license": "", "sideEffects": false, "type": "module", "version": "0.0.5", "scripts": {"build": "vite build", "dev": "vite --host", "preview": "vite preview --host --port 5173", "test": "vitest --run", "test:watch": "vitest", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint app", "lint:fix": "npm run lint -- --fix && prettier app --write"}, "engines": {"node": ">=18.18.0"}, "dependencies": {"@codemirror/autocomplete": "^6.18.6", "@codemirror/commands": "^6.8.1", "@codemirror/lang-cpp": "^6.0.2", "@codemirror/lang-css": "^6.3.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.3", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/lang-python": "^6.2.0", "@codemirror/lang-sass": "^6.0.2", "@codemirror/lang-vue": "^0.1.3", "@codemirror/lang-wast": "^6.0.2", "@codemirror/language": "^6.11.0", "@codemirror/search": "^6.5.10", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.36.8", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@iconify-json/ph": "^1.2.2", "@iconify-json/svg-spinners": "^1.2.2", "@lezer/highlight": "^1.2.1", "@mui/material": "^6.4.11", "@nanostores/react": "^0.7.3", "@octokit/rest": "^21.1.1", "@octokit/types": "^13.10.0", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tooltip": "^1.2.6", "@react-pdf/renderer": "^4.3.0", "@tanstack/react-query": "^5.76.1", "@types/lodash": "^4.17.16", "@uiw/codemirror-theme-vscode": "^4.23.12", "@unocss/reset": "^0.61.9", "@vitejs/plugin-react": "^4.4.1", "@webcontainer/api": "1.5.3-internal.2", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "ai": "4.1.6", "autoprefixer": "^10.4.21", "babel-plugin-react-compiler": "19.1.0-rc.1", "chalk": "^5.4.1", "clsx": "^2.1.1", "copy-to-clipboard": "^3.3.3", "dat.gui": "^0.7.9", "date-fns": "^3.6.0", "diff": "^5.2.0", "dotenv": "^16.5.0", "file-saver": "^2.0.5", "framer-motion": "^11.18.2", "googleapis": "^144.0.0", "i18n": "^0.15.1", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "ignore": "^6.0.2", "isbot": "^4.4.0", "isomorphic-git": "^1.30.1", "istextorbinary": "^9.5.0", "jose": "^5.10.0", "js-cookie": "^3.0.5", "jszip": "^3.10.1", "lodash": "^4.17.21", "lucide-react": "^0.468.0", "nanostores": "^0.10.3", "postcss": "^8.5.3", "react": "^19.1.0", "react-audio-visualize": "^1.2.0", "react-audio-voice-recorder": "^2.2.0", "react-circular-progressbar": "^2.2.0", "react-cropper": "^2.3.3", "react-dom": "^19.1.0", "react-helmet": "^6.1.0", "react-hotkeys-hook": "^4.6.2", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-markdown": "^9.1.0", "react-phone-number-input": "^3.4.12", "react-qr-code": "^2.0.15", "react-resizable-panels": "^2.1.9", "react-router-dom": "^7.6.0", "react-select": "^5.10.1", "react-toastify": "^10.0.6", "react-tooltip": "^5.28.1", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "shiki": "^1.29.2", "unist-util-visit": "^5.0.0", "viem": "^2.29.2", "zustand": "^5.0.4"}, "devDependencies": {"@blitz/eslint-plugin": "0.1.0", "@types/dat.gui": "^0.7.13", "@types/diff": "^5.2.3", "@types/dom-speech-recognition": "^0.0.4", "@types/file-saver": "^2.0.7", "@types/js-cookie": "^3.0.6", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@types/react-helmet": "^6.1.11", "@types/react-scroll-to-bottom": "^4.2.5", "fast-glob": "^3.3.3", "husky": "9.1.7", "is-ci": "^3.0.1", "node-fetch": "^3.3.2", "pnpm": "^9.15.9", "prettier": "^3.5.3", "sass": "^1.88.0", "sass-embedded": "^1.88.0", "tailwindcss": "^4.1.6", "typescript": "^5.8.3", "unified": "^11.0.5", "unocss": "^0.61.9", "vite": "npm:rolldown-vite@latest", "vite-plugin-node-polyfills": "^0.22.0", "vite-plugin-optimize-css-modules": "^1.2.0", "vite-tsconfig-paths": "^4.3.2", "vitest": "^2.1.9", "zod": "^3.24.4"}, "overrides": {"vite": "npm:rolldown-vite@latest"}, "resolutions": {"@typescript-eslint/utils": "^8.0.0-alpha.30"}, "packageManager": "pnpm@9.4.0"}