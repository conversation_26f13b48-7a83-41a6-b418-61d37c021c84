<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<rect x="0" y="0" width="100%" height="100%" fill="transparent"></rect>
<g transform="matrix(1 0 0 1 540 540)" id="9fedaccb-8700-4c1b-a86a-81277c657a30"  >
</g>
<g transform="matrix(1 0 0 1 540 540)" id="b214862f-6b57-4570-8570-5d413978bb59"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(21.14 0 0 21.14 696.63 675.89)" id="115621c9-181a-4ae1-8210-67f631ef4b78"  >
<rect style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(10,23,48); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  x="-33.085" y="-33.085" rx="0" ry="0" width="66.17" height="66.17" />
</g>
<g transform="matrix(3.01 0 0 3.01 539.35 539.42)"  >
<g style=""   >
		<g transform="matrix(1 0 0 1 74.87 106.5)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-266.13, -265.8)" d="M 336.31 226.91 C 336.29 226.99 336.26 227.07 336.22 227.16 C 335.87 227.97 327.48 247.23 306.18 266.86 C 293.68 278.38 279.32 287.6 263.5 294.26 C 243.75 302.57 221.66 306.9 197.84 307.11 C 196.78 307.11 195.91 306.27000000000004 195.9 305.21000000000004 C 195.89000000000001 304.15000000000003 196.74 303.28000000000003 197.8 303.27000000000004 C 250.47000000000003 302.8 284.32 281.75000000000006 303.44 264.17 C 324.2 245.09000000000003 332.6 225.85000000000002 332.68 225.66000000000003 C 333.1 224.69000000000003 334.23 224.24000000000004 335.2 224.65000000000003 C 336.09 225.03000000000003 336.55 226.01000000000005 336.28999999999996 226.92000000000004 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -22.44 141.14)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-168.81, -300.45)" d="M 183.61 303.89 C 183.37 304.76 182.54000000000002 305.36 181.61 305.28999999999996 C 168.41000000000003 304.23999999999995 155.72000000000003 299.53 155.18 299.33 C 154.19 298.96 153.68 297.84999999999997 154.06 296.85999999999996 C 154.43 295.86999999999995 155.54 295.35999999999996 156.53 295.72999999999996 C 156.66 295.78 169.27 300.45 181.92000000000002 301.46 C 182.98000000000002 301.53999999999996 183.76000000000002 302.46999999999997 183.68 303.53 C 183.68 303.65 183.65 303.78 183.62 303.89 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 152.74 43.19)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-344, -202.5)" d="M 348.54 195.94 C 345.67 206.32 342.08000000000004 214.42 341.91 214.81 C 341.48 215.78 340.34000000000003 216.21 339.38000000000005 215.78 C 338.41 215.35 337.97 214.22 338.41 213.25 C 338.46000000000004 213.15 343.11 202.62 345.98 190.52 C 346.21000000000004 189.48000000000002 347.26 188.85000000000002 348.29 189.10000000000002 C 349.32000000000005 189.35000000000002 349.96000000000004 190.38000000000002 349.71000000000004 191.41000000000003 C 349.34000000000003 192.96000000000004 348.95000000000005 194.48000000000002 348.54 195.95000000000002 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 66.07 -140.91)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-257.33, -18.4)" d="M 267.9 22.3 C 267.84999999999997 22.48 267.77 22.650000000000002 267.66999999999996 22.82 C 267.09999999999997 23.71 265.91999999999996 23.98 265.03 23.42 C 264.95 23.37 260.79999999999995 20.860000000000003 248.03999999999996 16.830000000000002 C 247.02999999999997 16.51 246.46999999999997 15.430000000000001 246.78999999999996 14.420000000000002 C 247.10999999999996 13.410000000000002 248.18999999999997 12.850000000000001 249.19999999999996 13.170000000000002 C 262.65999999999997 17.42 266.90999999999997 20.07 267.08 20.18 C 267.81 20.64 268.12 21.52 267.9 22.31 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -128.59 -23.13)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-62.67, -136.18)" d="M 58.81 183.06 C 58.620000000000005 183.76 58.04 184.32 57.28 184.44 C 56.230000000000004 184.60999999999999 55.25 183.91 55.07 182.85999999999999 C 50.4 154.89999999999998 54.36 130.86999999999998 58.51 115.62999999999998 C 63.01 99.08999999999997 68.56 89.27999999999997 68.78999999999999 88.86999999999998 C 69.32 87.94999999999997 70.49 87.62999999999998 71.41 88.15999999999998 C 72.33 88.68999999999998 72.64999999999999 89.85999999999999 72.11999999999999 90.77999999999999 C 72.03999999999999 90.91999999999999 66.52999999999999 100.68999999999998 62.16999999999999 116.76999999999998 C 58.139999999999986 131.60999999999999 54.29999999999999 155.01 58.84999999999999 182.21999999999997 C 58.899999999999984 182.49999999999997 58.87999999999999 182.78999999999996 58.80999999999999 183.04999999999998 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -35.15 -117.6)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-156.1, -41.7)" d="M 234.89 11.43 C 234.61999999999998 12.42 233.6 13.02 232.6 12.78 C 232.32 12.709999999999999 208.65 7.27 178.25 12.37 C 150.26 17.07 110.62 31.97 80.76 76.23 C 80.17 77.11 78.97 77.34 78.10000000000001 76.75 C 77.23000000000002 76.16 76.99000000000001 74.96 77.58000000000001 74.09 C 91.11000000000001 54.03 107.73000000000002 38.13 126.97000000000001 26.82 C 142.37 17.77 159.46 11.63 177.77 8.57 C 208.93 3.3600000000000003 232.5 8.82 233.48000000000002 9.05 C 234.51000000000002 9.3 235.15 10.33 234.9 11.360000000000001 C 234.9 11.38 234.89000000000001 11.410000000000002 234.88 11.430000000000001 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 102.13 -29.86)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-293.38, -129.45)" d="M 309.67 36.4 C 321.65000000000003 41.4 329.46000000000004 54.09 329.69 66.63 C 330.01 73.46 326.77 79.22 323.48 85.39999999999999 C 322.42 89.16999999999999 325.73 94.00999999999999 329.90000000000003 93.1 C 340.17 91.00999999999999 350.85 92.14999999999999 358.83000000000004 99.21 C 368.51000000000005 107.88999999999999 372.90000000000003 123.13 369.06000000000006 136.18 C 362.18000000000006 161.63 335.06000000000006 174.97 312.34000000000003 187.60000000000002 C 312.34000000000003 187.60000000000002 312.34000000000003 187.60000000000002 312.34000000000003 187.60000000000002 C 312.00000000000006 187.89000000000001 311.63000000000005 188.15000000000003 311.23 188.38000000000002 L 246.92000000000002 224.81000000000003 C 243.69000000000003 226.64000000000004 239.60000000000002 225.50000000000003 237.77 222.28000000000003 C 235.94 219.05000000000004 237.07000000000002 214.95000000000002 240.3 213.12000000000003 L 296.7 181.17000000000004 L 296.7 181.17000000000004 C 313.01 171.06000000000006 337.78 161.15000000000003 348.11 145.03000000000003 C 357.42 132.02000000000004 351.38 112.54000000000002 337.12 106.31000000000003 C 324.75 100.91000000000003 311.13 107.04000000000003 299.17 113.96000000000004 C 293.76 116.32000000000004 283.58000000000004 126.22000000000004 278.75 117.87000000000003 C 274.26 109.74000000000004 286.63 106.26000000000003 291.43 103.00000000000003 C 303.02 96.15000000000003 309.17 91.57000000000002 312.6 81.05000000000003 C 315.70000000000005 69.71000000000002 309.13 56.29000000000002 298.77000000000004 51.03000000000003 C 286.77000000000004 44.88000000000003 272.75000000000006 51.97000000000003 261.02000000000004 58.70000000000003 C 261.02000000000004 58.70000000000003 261.00000000000006 58.71000000000003 260.99000000000007 58.720000000000034 C 260.6700000000001 58.900000000000034 260.36000000000007 59.080000000000034 260.05000000000007 59.26000000000003 L 260.05000000000007 59.26000000000003 C 260.05000000000007 59.26000000000003 256.80000000000007 61.110000000000035 256.80000000000007 61.110000000000035 L 226.36000000000007 78.35000000000004 C 223.13000000000008 80.18000000000004 219.03000000000006 79.04000000000003 217.20000000000007 75.82000000000004 C 215.37000000000006 72.59000000000003 216.50000000000009 68.49000000000004 219.73000000000008 66.66000000000004 L 254.10000000000008 47.19000000000004 C 254.32000000000008 47.06000000000004 254.55000000000007 46.95000000000004 254.7800000000001 46.85000000000004 C 272.68000000000006 36.610000000000035 291.0400000000001 28.470000000000038 309.68000000000006 36.390000000000036 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 67.16 -99.11)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-258.42, -60.2)" d="M 260.04 59.27 C 258.94 59.910000000000004 257.86 60.53 256.81 61.14 L 256.79 61.11 L 260.04 59.269999999999996 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -13.2 54.88)" id="Layer_4_copy_8"  >
<line style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  x1="91.21" y1="-51.67500000000001" x2="-91.21" y2="51.67500000000001" />
</g>
		<g transform="matrix(1 0 0 1 -15.4 57.07)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-175.85, -216.38)" d="M 269.99 163.78 L 89.69 270.87 C 86.96 272.49 83.42 271.59000000000003 81.8 268.86 C 80.13 266.06 81.14 262.40000000000003 84.02 260.85 C 84.02 260.85 268.56 161.25000000000003 268.56 161.25000000000003 C 269.27 160.87000000000003 270.15 161.13000000000002 270.53000000000003 161.84000000000003 C 270.90000000000003 162.53000000000003 270.66 163.38000000000002 269.99 163.78000000000003 L 269.99 163.78000000000003 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -75.9 16.74)" id="Layer_4_copy_8"  >
<line style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  x1="58.61" y1="-33.205" x2="-58.61" y2="33.205" />
</g>
		<g transform="matrix(1 0 0 1 -78.18 18.99)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-113.07, -178.3)" d="M 174.65 144.05 L 59.59 214.27 C 56.870000000000005 215.93 53.330000000000005 215.07000000000002 51.67 212.35000000000002 C 50.010000000000005 209.63000000000002 50.870000000000005 206.09000000000003 53.59 204.43000000000004 C 53.61000000000001 204.36000000000004 173.16 141.74000000000004 173.29000000000002 141.64000000000004 C 174.79000000000002 140.88000000000005 176.19000000000003 143.01000000000005 174.65000000000003 144.04000000000005 L 174.65000000000003 144.04000000000005 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -70.43 51.89)" id="Layer_4_copy_8"  >
<line style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  x1="102.91" y1="-58.3" x2="-102.91" y2="58.3" />
</g>
		<g transform="matrix(1 0 0 1 -72.62 54.04)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-118.64, -213.36)" d="M 224.48 154.21 L 20.76 274.51 C 18.020000000000003 276.13 14.490000000000002 275.21999999999997 12.870000000000001 272.48 C 11.21 269.68 12.22 266.04 15.080000000000002 264.49 C 15.080000000000002 264.49 222.99 151.59 222.99 151.59 C 223.72 151.19 224.63 151.46 225.03 152.19 C 225.42 152.9 225.17 153.79 224.47 154.2 L 224.47 154.2 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 69.24 -100.31)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-260.5, -59)" d="M 260.98 58.73 C 260.89000000000004 58.79 260.8 58.839999999999996 260.71000000000004 58.88999999999999 L 260.03000000000003 59.269999999999996 C 260.34000000000003 59.089999999999996 260.66 58.91 260.97 58.73 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -8.61 93.37)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-182.64, -252.68)" d="M 231.85 232.25 L 133.16 283.76 C 130.99 284.84 129.16 281.78 131.26 280.4 C 131.26 280.4 226.18 222.22999999999996 226.18 222.22999999999996 C 228.89000000000001 220.56999999999996 232.44 221.41999999999996 234.1 224.12999999999997 C 235.85 226.94999999999996 234.79999999999998 230.71999999999997 231.85 232.24999999999997 L 231.85 232.24999999999997 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -15.13 -61.48)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-176.13, -97.83)" d="M 210.01 86.09 L 141.85 120.30000000000001 C 140.9 120.78000000000002 139.73999999999998 120.39000000000001 139.26 119.44000000000001 C 138.81 118.55000000000001 139.13 117.47000000000001 139.95 116.94000000000001 L 204.33999999999997 76.06 C 207.02999999999997 74.35000000000001 210.58999999999997 75.15 212.29999999999998 77.84 C 214.01 80.53 213.20999999999998 84.09 210.51999999999998 85.8 C 210.35999999999999 85.89999999999999 210.17999999999998 86 210.01999999999998 86.09 C 210.01999999999998 86.09 210.18999999999997 86.01 210.01999999999998 86.09 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -72.79 -153.75)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-118.47, -5.55)" d="M 124.02 7.09 L 124.02 7.09 C 120.95 6.24 119.15 3.07 120 0 L 120 0 C 119.15 3.07 115.98 4.87 112.91 4.02 L 112.91 4.02 C 115.97999999999999 4.869999999999999 117.78 8.04 116.92999999999999 11.11 L 116.92999999999999 11.11 C 117.77999999999999 8.04 120.94999999999999 6.239999999999999 124.02 7.09 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -24.32 -105.06)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-166.94, -54.25)" d="M 174.57 56.36 L 174.57 56.36 C 170.35 55.19 167.88 50.83 169.04999999999998 46.62 L 169.04999999999998 46.62 C 167.88 50.839999999999996 163.51999999999998 53.309999999999995 159.30999999999997 52.14 L 159.30999999999997 52.14 C 163.52999999999997 53.31 165.99999999999997 57.67 164.82999999999998 61.88 L 164.82999999999998 61.88 C 165.99999999999997 57.660000000000004 170.35999999999999 55.190000000000005 174.57 56.36 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -95.07 -43.57)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-96.19, -115.74)" d="M 103.82 117.85 L 103.82 117.85 C 99.6 116.67999999999999 97.13 112.32 98.3 108.11 L 98.3 108.11 C 97.13 112.33 92.77 114.8 88.56 113.63 L 88.56 113.63 C 92.78 114.8 95.25 119.16 94.08 123.36999999999999 L 94.08 123.36999999999999 C 95.25 119.14999999999999 99.61 116.67999999999999 103.82 117.85 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -13.6 122.89)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-177.66, -282.2)" d="M 183.16 283.72 L 183.16 283.72 C 180.12 282.88000000000005 178.34 279.74 179.18 276.70000000000005 L 179.18 276.70000000000005 C 178.34 279.74000000000007 175.20000000000002 281.52000000000004 172.16 280.68000000000006 L 172.16 280.68000000000006 C 175.2 281.52000000000004 176.98 284.6600000000001 176.14 287.70000000000005 L 176.14 287.70000000000005 C 176.98 284.66 180.11999999999998 282.88000000000005 183.16 283.72 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 48.73 110.43)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-240, -269.75)" d="M 249.69 272.43 L 249.69 272.43 C 244.34 270.95 241.2 265.41 242.68 260.05 L 242.68 260.05 C 241.20000000000002 265.40000000000003 235.66 268.54 230.3 267.06 L 230.3 267.06 C 235.65 268.54 238.79000000000002 274.08 237.31 279.44 L 237.31 279.44 C 238.79 274.09 244.33 270.95 249.69 272.43 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 111.06 60.09)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-302.33, -219.41)" d="M 310.11 221.56 L 310.11 221.56 C 305.81 220.37 303.29 215.92000000000002 304.48 211.62 L 304.48 211.62 C 303.29 215.92000000000002 298.84000000000003 218.44 294.54 217.25 L 294.54 217.25 C 298.84000000000003 218.44 301.36 222.89 300.17 227.19 L 300.17 227.19 C 301.36 222.89 305.81 220.37 310.11 221.56 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 99.03 150.66)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-290.28, -309.97)" d="M 298.94 312.36 L 298.94 312.36 C 294.16 311.04 291.36 306.09000000000003 292.68 301.31 L 292.68 301.31 C 291.36 306.09 286.41 308.89 281.63 307.57 L 281.63 307.57 C 286.40999999999997 308.89 289.21 313.84 287.89 318.62 L 287.89 318.62 C 289.21 313.84000000000003 294.15999999999997 311.04 298.94 312.36 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -98.88 86.57)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-92.38, -245.88)" d="M 96.1 246.91 L 96.1 246.91 C 94.03999999999999 246.34 92.83999999999999 244.21 93.41 242.16 L 93.41 242.16 C 92.84 244.22 90.71 245.42 88.66 244.85 L 88.66 244.85 C 90.72 245.42 91.92 247.54999999999998 91.35 249.6 L 91.35 249.6 C 91.91999999999999 247.54 94.05 246.34 96.1 246.91 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -142.16 71.9)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(74,222,128); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-49.11, -231.2)" d="M 52.67 232.19 L 52.67 232.19 C 50.7 231.65 49.550000000000004 229.60999999999999 50.09 227.64 L 50.09 227.64 C 49.550000000000004 229.60999999999999 47.510000000000005 230.76 45.540000000000006 230.22 L 45.540000000000006 230.22 C 47.510000000000005 230.76 48.660000000000004 232.8 48.120000000000005 234.77 L 48.120000000000005 234.77 C 48.660000000000004 232.8 50.7 231.65 52.67 232.19 Z" stroke-linecap="round" />
</g>
</g>
</g>
<g transform="matrix(0 0 0 0 0 0)"  >
<g style=""   >
</g>
</g>
<g transform="matrix(0 0 0 0 0 0)"  >
<g style=""   >
</g>
</g>
<g transform="matrix(0 0 0 0 0 0)"  >
<g style=""   >
</g>
</g>
<g transform="matrix(0 0 0 0 0 0)"  >
<g style=""   >
</g>
</g>
<g transform="matrix(0 0 0 0 0 0)"  >
<g style=""   >
</g>
</g>
<g transform="matrix(0 0 0 0 0 0)"  >
<g style=""   >
</g>
</g>
<g transform="matrix(0 0 0 0 0 0)"  >
<g style=""   >
</g>
</g>
<g transform="matrix(0 0 0 0 0 0)"  >
<g style=""   >
</g>
</g>
<g transform="matrix(0 0 0 0 0 0)"  >
<g style=""   >
</g>
</g>
</svg>