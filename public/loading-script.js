export function loadingFunctionBiela() {

  const logo = document.getElementById("biela-logo");
  const throttleLines = document.querySelectorAll(".throttle-line");
  const stars = logo.querySelectorAll(".star");
  const circleElements = document.querySelectorAll(".circle-element path");
  const bLetter = document.querySelector(".b-letter");
  const trail = document.querySelector(".trail");

  // Set initial states
  throttleLines.forEach((line) => {
    line.style.strokeDasharray = "1000";
    line.style.strokeDashoffset = "1000";
    line.style.opacity = "0.3";
  });

  // Simulate loading process
  let progress = 0;
  const totalDuration = 3000; // 5 seconds total loading time
  const updateInterval = 50; // Update every 50ms
  const increment = (updateInterval / totalDuration) * 100;

  // Start the loading animation
  const loadingInterval = setInterval(updateProgress, updateInterval);

  function updateProgress() {
    progress += increment;

    if (progress >= 100) {
      progress = 100;
      clearInterval(loadingInterval);
      completeLoading();
    }

    // Sync logo animation with progress
    syncLogoWithProgress(progress);
  }

  function syncLogoWithProgress(progress) {
    // Animate stars with random delays
    stars.forEach((star) => {
      if (progress > 10 && !star.style.animation) {
        const delay = Math.random() * 2;
        const duration = 1 + Math.random() * 2;
        star.style.animation = `starTwinkle ${duration}s ease-in-out ${delay}s infinite`;
      }
    });

    // Animate circle elements
    circleElements.forEach((circle, index) => {
      if (progress > 20 && circle.style.opacity === "") {
        circle.style.opacity = "0";
        setTimeout(() => {
          circle.style.opacity = "1";
          circle.style.transition = "opacity 0.5s ease-in-out";
        }, 100 * index);
      }
    });

    // Animate throttle lines
    if (progress > 30 && throttleLines[0].style.animation === "") {
      throttleLines.forEach((line, index) => {
        setTimeout(() => {
          line.style.animation = `throttle 1.5s ease-in-out forwards`;
        }, 200 * index);
      });
    }

    if (progress > 50 && bLetter.style.animation === "") {
      bLetter.style.animation = "bGlow 1.2s ease-in-out infinite";

      createThrottleLoadingEffect();
    }

    // Instead of rotation, use vibration and glow effects like in the reel version
    if (progress > 60) {
      const vibrationIntensity = (progress - 60) / 10; // Scale from 0 to 4
      const randomX = (Math.random() - 0.5) * vibrationIntensity;
      const randomY = (Math.random() - 0.5) * vibrationIntensity;
      logo.style.transform = `rotate(-72deg) translate(${randomX}px, ${randomY}px)`;
    }

    // Add glow effect that intensifies with progress
    if (progress > 40) {
      const glowIntensity = (progress - 40) / 15; // Scale from 0 to 4
      circleElements.forEach((circle) => {
        circle.style.filter = `drop-shadow(0 0 ${glowIntensity * 5}px rgba(74, 222, 128, 0.8))`;
      });
    }
  }

  function createThrottleLoadingEffect() {
    // Simulate throttle loading with vibration and glow
    let intensity = 0;
    const maxIntensity = 5;
    const interval = setInterval(() => {
      if (intensity >= maxIntensity) {
        clearInterval(interval);
        return;
      }

      // Increase vibration intensity
      const randomX = (Math.random() - 0.5) * intensity;
      const randomY = (Math.random() - 0.5) * intensity;

      // Apply vibration without rotation
      logo.style.transform = `rotate(-72deg) translate(${randomX}px, ${randomY}px)`;

      // Apply glow directly to circle elements
      circleElements.forEach((circle) => {
        circle.style.filter = `drop-shadow(0 0 ${intensity * 3}px rgba(74, 222, 128, 0.8))`;
      });

      // Also apply glow to stars for additional effect
      stars.forEach((star) => {
        star.style.filter = `drop-shadow(0 0 ${intensity * 2}px rgba(74, 222, 128, 0.8))`;
      });

      intensity += 0.5;
    }, 100);
  }

  function completeLoading() {
    // Create trail effect
    createTrailEffect();

    // Add fly-away animation like in the reel version
    logo.style.animation = "flyAway 1s ease-out forwards";
  }

  function createTrailEffect() {
    // Create a trail effect when the logo completes loading
    trail.style.left = "50%";
    trail.style.top = "50%";
    trail.style.animation = "trailEffect 1s ease-out forwards";
  }
}
