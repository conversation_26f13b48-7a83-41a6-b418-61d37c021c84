{"userInformation": "İstifadəçi Məlumatları", "firstName": "Ad", "lastName": "Soyad", "username": "İstifadəçi Adı", "phone": "Telefon", "preferredLanguage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "billingInformation": "Ödəmə Məlumatları", "addressLine1": "Ünvan Sətiri 1", "addressLine2": "Ünvan Sətiri 2", "city": "Ş<PERSON><PERSON><PERSON><PERSON>", "zipCode": "Poçt Kodu", "referralCode": "<PERSON><PERSON><PERSON>", "referralLink": "<PERSON><PERSON><PERSON>", "referral": "Referans", "location": "Yer", "site": "<PERSON><PERSON>", "email": "E-poçt", "contactInfo": "<PERSON><PERSON><PERSON><PERSON> məlumatları", "websiteUrl": "Saytın URL", "accountDeletion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deleteAccount": "Hesabı Sil", "deleteAccountWarning": "Silinmə halında bütün layihələriniz və şəxsi məlumatlarınız silinəcəkdir. Həmçinin, təşkilatlarınızda layihələr üzərində nəzarəti itirəcəksiniz.", "connectAccount": "Hesabınızı {{platform}} ilə bağlayın", "connectNow": "İndi Bağlayın", "connected": "Bağlandı", "personalWebsite": "<PERSON><PERSON><PERSON><PERSON>", "facebook": "Facebook", "twitterX": "Twitter/X", "linkedin": "LinkedIn", "youtube": "YouTube", "instagram": "Instagram", "tiktok": "TikTok", "Country": "<PERSON><PERSON><PERSON>", "ChangePassword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Updating": "Yenilənir...", "Save": "<PERSON><PERSON>a saxla", "Edit": "Redaktə et", "CurrentPassword": "<PERSON>i şifrə", "NewPassword": "<PERSON><PERSON>", "RepeatNewPassword": "<PERSON><PERSON>əni təkrarla", "AllPasswordFieldsAreRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON> şifrə sahələri tələb olunur", "NewPasswordsDoNotMatch": "<PERSON><PERSON> u<PERSON>ğ<PERSON> gəlmir", "PasswordMustBeAtLeastCharactersLong": "Şifrə ən az {{minLength}} simvol uzunluğunda olmalıdır", "UserNotFound": "İstifadəçi tapılmadı", "CurrentPasswordIsIncorrect": "Cari şifrə yanlışdır", "PasswordUpdated": "Şifrəniz uğurla yeniləndi!", "ErrorWhileUpdatingPassword": "Şifrəni yeniləyərkən xəta baş verdi", "Success": "<PERSON><PERSON><PERSON>", "Error": "<PERSON><PERSON><PERSON>", "updateAccount": "Hesabınız uğurla yeniləndi!", "failedUpdateAccount": "<PERSON>ir xəta baş verdi. <PERSON><PERSON><PERSON><PERSON><PERSON>, yenidən cəhd edin.", "Close": "Bağla", "copyReferralCode": "Tövsi<PERSON>ə kodunu kopyala", "copyReferralLink": "Tövsiyə bağlantısını kopyala", "purchaseDetails": "<PERSON><PERSON>ş məlumatları", "dateLabel": "<PERSON><PERSON>", "planLabel": "Plan", "amountLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>ğ", "tokensLabel": "Tokenlər", "whatsNext": "Nə olacaq?", "yourTokensAvailable": "To<PERSON><PERSON><PERSON><PERSON> artıq he<PERSON> mövcuddur", "purchaseDetailsStored": "<PERSON><PERSON>ş məlumatları hesabınıza qeyd edilib", "viewPurchaseHistory": "Satınalma tarixçənizi ödəniş bölməsində görə bilərsiniz", "thankYou": "Təş<PERSON>kk<PERSON>r edirik!", "purchaseSuccessful": "Alış uğurla tamamlandı", "startCoding": "Kodlaşdırmaya başlayın", "tokenTopUpComingSoon": "Token yükləmə tezliklə gəlir", "finalisingPricing": "Ən yaxşı qiymətləri və həcmi endirimlərini təqdim etmək üçün qiymət sistemimizi yekunlaşdırırıq.", "getReady": "İnkişaf ehtiyaclarınızı qarşılamaq üçün çevik token paketlərinə hazır olun.", "gotIt": "Başa düşdüm", "checkBackSoon": "Token almaq üçün tezliklə yenidən baxın", "buyExtraTokens": "<PERSON><PERSON><PERSON> token al", "paymentMethod": "Ödəniş üsulu", "howManyTokens": "Nə qədər token almaq istəyirs<PERSON>z?", "placeholderEnterTokens": "Token miqdarını daxil edin...", "units": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "availableDiscounts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "priceLabel": "Qiymət:", "discountAppliedLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> olunan endirim:", "at": "ilə", "perMillion": "hər milyon token üçün", "purchaseTokens": "Token al", "minimumPurchaseError": "Minimum alış {minTokens} token ($1)", "minimumTokenError": "Minimum {minTokens} token alınmalıdır", "maximumTokenError": "Maksimum {maxTokens} token alınabilir", "modalTitle": "Plan seçin və ya ehtiyac olduqda token yükləyin", "modalSubtitle": "Üç çevik planımızdan birini seçin və ya istədiyiniz zaman əlavə token alın.", "highlightExtraTokens": "ə<PERSON>ə tokenlər", "toggleMonthly": "Aylıq", "toggleYearly": "İllik (10% qənaət)", "buttonNeedMoreTokens": "Daha çox token lazımdır?", "recentInvoicesTitle": "<PERSON> faktura<PERSON>", "recentInvoicesDescription": "Faktura tarixçəsini izləyin və fakturaları endirin", "tableHeaderDate": "<PERSON><PERSON>", "tableHeaderDescription": "<PERSON><PERSON><PERSON><PERSON>", "tableHeaderAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON>ğ", "tableHeaderStatus": "Status", "tableHeaderActions": "Əm<PERSON>liyyatlar", "noInvoicesFound": "Faktura tapılmadı", "actionPreviewInvoice": "Fakturanın ön baxışı", "actionDownloadInvoice": "Fakturanı yüklə", "planDescription": "İnkişafı öyrənənlər, yeni bacarıqlar əldə edənlər və şəxsi layihələr üzərində işləyənlər üçün idealdır.", "planNameBasicPlus": "Basic Plus", "planNameStarterPlus": "Starter Plus", "planNamePremiumPlus": "Premium Plus", "planPriceBasicPlus": "$100/il", "planPriceStarterPlus": "$200/il", "planPricePremiumPlus": "$300/il", "planTokensBasicPlus": "Aylıq 5M token", "planTokensStarterPlus": "Aylıq 25M token", "planTokensPremiumPlus": "Aylıq 80M token", "priceSeparator": "/", "currentPlanLabel": "Cari plan", "upgradePlanLabel": "Planı yüksəlt", "nextPage": "Növbəti Səhifə", "previousPage": "Əvvəlki Səhifə", "paginationStatus": "{{from}} - {{to}} a<PERSON><PERSON>, cəmi {{total}} nəticə"}