{
  "whatWouldYouLikeToBuild": "İdeyanızı dəqiqələr ərzində canlı vebsayt və ya tətbiqə çevirin",
  "whatWouldYouLikeToBuildSubtitle": "Əgər onu <1>təsəvvür edə bilirsiniz</1>, onu <5>kodlaya bilərsiniz</5>.",
  "fromIdeaToDeployment": "Pulsuz başlayın. Hər şeyi kodlaşdırın. Bacarıqlarınızı hər sorğu ilə fürsətə çevirin.",
  "codePlaceholder": "Əgər xəyal edə bilirsinizsə, BIELA onu kodlaşdıra bilər, bu gün nə edək?",
  "defaultPlaceholder": "Bu gün sizə necə kömək edə bilərəm? Gəlin birlikdə möhtəşəm bir şey edək",
  "checkingFeatures": "Xüsusiyyətlər yoxlanılır",
  "checklists": "Yo<PERSON><PERSON><PERSON> siyahılar<PERSON>",
  "runUnitTestsSuggestionTitle": "Təkli<PERSON>",
  "runUnitTestsSuggestionMessage": "Layihəniz üçün vahid testləri işə salmaq istəyirsinizmi?",
  "runUnitTestsPrimaryButton": "Vahid testləri işə salın",
  "runUnitTestsSecondaryButton": "İmtina et",
  "createDatabaseTitle": "Verilənlər Bazası Yaratma",
  "createDatabaseMessage": "Layihəniz üçün verilənlər bazası yaratmaq istəyirsinizmi?",
  "createDatabasePrimaryButton": "Verilənlər bazası yaradın",
  "createDatabaseSecondaryButton": "İmtina et",
  "extendedThinking": "Genişləndirilmiş düşüncə",
  "extendedThinkingTooltip": "Süni intellektin cavab verməzdən əvvəl daha dərindən düşünməsinə icazə verin",
  "firstResponseOnly": "Yalnız ilk cavab",
  "always": "Həmişə",
  "AIReasoning": "Süni İntellekt Mühakiməsi",
  "thinking": "Düşünmə",
  "attachFile": "Fayl əlavə et",
  "voiceInput": "Səsli giriş",
  "selectLanguage": "Səsli giriş üçün dili seçin",
  "languageSelectionDisabled": "Yazı zamanı dil seçimi deaktiv edilib",
  "notAvailableInFirefox": "Bu funksiya Firefox-da mövcud deyil, lakin Chrome və Safari-də mövcuddur",
  "enhancePrompt": "Sorğunu təkmilləşdir",
  "cleanUpProject": "Layihəni təmizlə",
  "clearChatHistory": "Söhbət tarixçəsini sil",
  "ChatNotFound": "Söhbət və ya istifadəçi tapılmadı",
  "ChatClearFailedServer": "Söhbət tarixçəsi təmizlənə bilmədi (server xətası).",
  "NoMessagesToClear": "Təmizləmək üçün mesaj yoxdur",
  "ChatClearSuccess": "Söhbət tarixçəsi təmizləndi!",
  "ChatClearFailedUnexpected": "Söhbət tarixçəsi təmizlənə bilmədi (gözlənilməz xəta).",
  "ClearChatTitle": "Söhbət tarixçəsini təmizlə",
  "ClearChatConfirm": "Söhbət tarixçəsini təmizləmək istədiyinizə əminsiniz?",
  "ClearChatIrreversible": "Bu əməliyyatı geri qaytarmaq mümkün deyil.",
  "ClearChatPro": "Daha yaxşı performans üçün tokenləri azaldır.",
  "ClearChatCon": "Süni intellekt əvvəlki mesajları xatırlamayacaq, lakin kodu görməyə davam edəcək.",
  "Advantage": "Üstünlük",
  "Disadvantage": "Dezavantaj",
  "showPrompt": "Təklif göstər",
  "hidePrompt": "Təklif gizlət",
  "sendButton": "Göndər",
  "abortButton": "Ləğv et",
  "inspirationTitle": "İlham lazımdır? Bunlardan birini sınayın:",
  "cleanUpPrompt": "Layihəni təmizləyin: Heç bir faylın 300 sətirdən çox kod ehtiva etmədiyinə əmin olun. Böyük faylları daha kiçik, modul komponentlərə refaktor edin, eyni funksionallığı qoruyaraq. Artıq istifadə edilməyən faylları, kodu, komponentləri və lazımsız məlumatları müəyyən edib silin. Bütün komponentlərin düzgün əlaqəli və işlək qalmasına diqqət yetirin, mövcud sistemdə kəsintilərə yol verməyin. Kodun bütövlüyünü qoruyun və dəyişikliklərin səhvlərə və ya mövcud xüsusiyyətlərin pozulmasına səbəb olmadığını yoxlayın. Məqsəd layihəni səmərəlilik, asan saxlanılma və aydınlıq üçün optimallaşdırmaqdır.",
  "checklistPrompt": "Əsas təlimatımı nəzərdən keçirin, məqsədi addım-addım başa düşün və mənim üçün yoxlanma siyahısı hazırlayın: Edilən hər şey üçün yaşıl işarə və hələ görülməsi lazım olanlar üçün qırmızı işarə ilə.",
  "personalPortfolioIdea": "Qaranlıq mövzulu şəxsi portfel veb saytı yaradın",
  "recipeFinderIdea": "Ərzaqlara əsaslanaraq yeməkləri təklif edən resept tapma tətbiqi yaradın",
  "weatherDashboardIdea": "Animasiya fonları ilə hava vəziyyəti paneli dizayn edin",
  "habitTrackerIdea": "İrəliləyişin vizuallaşdırıldığı vərdiş izləyici yaradın",
  "loading": "Yüklənir",
  "checkingYourAuthenticity": "Həqiqiliyiniz yoxlanılır",
  "error": "Xəta",
  "succes": "Uğur!",
  "tryAgain": "Təkrar Cəhd Et",
  "dashboard": "Panel",
  "getStartedTitle": "Başlayın",
  "getStartedSub": "Biela.dev-in necə işlədiyini kəşf edin",
  "createProject": "Yeni Layihə Yaradın",
  "createProjectSub": "Sıfırdan başlayın",
  "uploadProject": "Layihəni Yükləyin",
  "uploadProjectSub": "Mövcud layihəni idxal edin",
  "importChat": "Sohbeti idxal edin",
  "importChatSub": "Mövcud sohbeti idxal edin",
  "createFolder": "Yeni qovluq yaradın",
  "createFolderSub": "Layihələrinizi təşkil edin",
  "editProjectName": "Layihə adını redaktə et",
  "editName": "Adı redaktə et",
  "cancel": "Ləğv et",
  "changeFolder": "Qovluğu dəyişdirin",
  "save": "Yadda saxla",
  "importing": "İdxal olunur...",
  "importFolder": "Qovluğu idxal edin",
  "giveTitle": "Başlıq verin",
  "projects": "Layihələr",
  "searchProjects": "Layihələri axtarın...",
  "becomeAffiliate": "Tərəfdaş olun",
  "exclusiveGrowth": "Eksklüziv İnkişaf Faydaları",
  "lifetimeEarnings": "Ömür Boyu Qazanc",
  "highCommissions": "Yüksək Komissiyalar",
  "earnCommission": "İlk satışınızdan 50% komissiya qazanın",
  "joinAffiliateProgram": "Tərəfdaş Proqramımıza Qoşulun",
  "folders": "Qovluqlar",
  "organizeProjects": "Layihələrinizi kateqoriyalara görə təşkil edin",
  "createNewFolder": "Yeni qovluq yaradın",
  "enterFolderName": "Qovluq adını daxil edin",
  "editFolder": "Qovluğu redaktə edin",
  "deleteFolder": "Qovluğu silin",
  "all": "Hamısı",
  "webProjects": "Veb Layihələr",
  "mobilepps": "Mobil Tətbiqlər",
  "developmentComparison": "İnkişaf Müqayisəsi",
  "traditionalVsAI": "Ənənəvi vs Süni İntellekt",
  "traditional": "Ənənəvi",
  "standardApproach": "Standart Yanaşma",
  "developmentCost": "İnkişaf Xərcləri",
  "developmentTime": "İnkişaf Vaxtı",
  "costSavings": "Xərclərin Azaldılması",
  "reducedCosts": "Azaldılmış Xərclər",
  "timeSaved": "Qənaət Edilmiş Vaxt",
  "fasterDelivery": "Daha Sürətli Təslim",
  "bielaDevAI": "Biela.dev Süni İntellekt",
  "nextGenDevelopment": "Növbəti Nəsil İnkişaf",
  "developmentCosts": "İnkişaf Xərcləri",
  "openInGitHub": "GitHub-da açın",
  "downloadProject": "Layihəni Yükləyin",
  "duplicateProject": "Layihəni Təkrarlayın",
  "openProject": "Layihəni Açın",
  "deleteProject": "Layihəni Silin",
  "confirmDelete": "Bu qovluğu silək?",
  "invoicePreview": "Sizin fakturanız",
  "settings": {
    "title": "Ayarlar",
    "deployment": {
      "AdvancedSettings": {
        "advanced-settings": "Ətraflı Ayarlar",
        "configure-advanced-deployment-options": "Ətraflı yerləşdirmə seçimlərini tənzimləyin",
        "server-configuration": "Server Konfiqurasiyası",
        "memory-limit": "Yaddaş Həcmi",
        "region": "Region",
        "security-settings": "Təhlükəsizlik Ayarları",
        "enable-ddos-protection": "DDoS Qorumasını aktivləşdirin",
        "protect-against-distributed": "Dağıdılmış xidməti rədd etmə hücumlarına qarşı qoruyun",
        "ip-whitelisting": "IP Ağ Siyahısı",
        "restrict-acces-to": "Xüsusi IP ünvanlarına giriş məhdudlaşdırın",
        "deployment-options": "Yerləşdirmə Seçimləri",
        "auto-deploy": "Avtomatik Yerləşdirmə",
        "automatically-deploy-when": "Əsas budağa göndərərkən avtomatik yerləşdirmə",
        "preview-deployments": "Əvvəlcədən Yerləşdirmələri Görün",
        "create-preview-deployments": "Pull requestlər üçün əvvəlcədən yerləşdirmə yaradın"
      },
      "BuildSettings": {
        "build-and-deployment-settings": "Yığma və Yerləşdirmə Ayarları",
        "build-command": "Yığma Əmri",
        "override": "Üstünə yaz",
        "output-directory": "Çıxış Kataloqu",
        "override2": "Üstünə yaz"
      },
      "DatabaseConfiguration": {
        "database-configuration": "Verilənlər Bazası Konfiqurasiyası",
        "configure-your-db-connections": "Verilənlər bazası bağlantılarınızı və ayarlarınızı tənzimləyin",
        "database-type": "Verilənlər Bazası Növü",
        "connection-string": "Bağlantı Sətiri",
        "your-db-credentials": "Verilənlər bazası giriş məlumatlarınız şifrələnir və təhlükəsiz saxlanılır",
        "database-settings": "Verilənlər Bazası Ayarları",
        "pool-size": "Hovuz Ölçüsü",
        "require": "Tələb et",
        "prefer": "Əvvəl tut",
        "disable": "Deaktiv et",
        "add-database": "Verilənlər Bazası Əlavə Et"
      },
      "DomainSettings": {
        "domain-settings": "Domen Ayarları",
        "configure-your-custom-domain": "Xüsusi domenlərinizi və SSL sertifikatlarınızı tənzimləyin",
        "custom-domain": "Xüsusi Domen",
        "add": "Əlavə et",
        "ssl-certificate": "SSL Sertifikatı",
        "auto-renew-ssl-certificates": "SSL sertifikatlarını avtomatik yenilə",
        "auto-renew-before-expiry": "Sertifikatların müddəti bitmədən avtomatik yenilə",
        "force-https": "HTTPS-i məcburi et",
        "redirect-all-http-traffic": "Bütün HTTP trafiki HTTPS-ə yönləndir",
        "active-domain": "Aktiv Domenlər",
        "remove": "Sil"
      },
      "EnvironmentVariables": {
        "environment-variables": "Ətraf Mühit Dəyişənləri",
        "configure-environment-variables": "Yerləşdirmələriniz üçün ətraf mühit dəyişənlərini tənzimləyin",
        "all-enviroments": "Bütün Mühitlər",
        "environment-variables2": "Ətraf Mühit Dəyişənləri",
        "preview": "Əvvəlcədən baxış",
        "development": "İnkişaf",
        "create-new": "Yeni Yarat",
        "key": "Açar",
        "value": "Dəyər",
        "save-variable": "Dəyişəni yadda saxla",
        "you-can-also-import": ".env faylından da dəyişənləri idxal edə bilərsiniz:",
        "import-env-file": ".env Faylını İdxal Et"
      },
      "ProjectConfiguration": {
        "project-configuration": "Layihə Konfiqurasiyası",
        "config-your-project-settings": "Layihə ayarlarınızı və yerləşdirmə seçimlərinizi tənzimləyin",
        "project-url": "Layihə URL-i",
        "framework": "Çərçivə",
        "repo": "Depo",
        "branch": "Budaq",
        "main": "əsas",
        "development": "inkişaf",
        "staging": "staging"
      }
    },
    "identity": {
      "unverified": {
        "title": "Şəxsiyyət Təsdiqi",
        "description": "Biela.dev istifadə etmək üçün şəxsiyyətinizi təsdiq edin",
        "subtitle": "Təhlükəsiz Təsdiq Prosesi",
        "processServers": "Bütün kart məlumatları Biela-nın serverlərində deyil, Stripe serverlərində təhlükəsiz saxlanılır",
        "processCharge": "Abunəlik üçün açıq razılığınız olmadan kartınızdan ödəniş alınmayacaq",
        "processBenefits": "Biela.dev, təsdiq edilmiş hesablar üçün 15 May 2025-ci ilə qədər tamamilə pulsuzdur",
        "verifyStripe": "Kredit və ya Debit Kart ilə Təsdiqlə",
        "verifyStripeDescription": "Təsdiq üçün ödəniş üsulunuzu bağlayın",
        "verifyNow": "İndi Təsdiq Edin"
      },
      "verified": {
        "title": "Şəxsiyyət Təsdiqləndi",
        "description": "Şəxsiyyətiniz müvəffəqiyyətlə təsdiqləndi",
        "paymentMethod": "Ödəniş üsulu",
        "cardEnding": "Kartın sonu",
        "updatePayment": "Ödəniş üsulunu yenilə",
        "untilDate": "15 May 2025-ci ilə qədər",
        "freeAccess": "Pulsuz Giriş",
        "freeAccessDescription": "Biela.dev-ə tam daxil olmaq imkanı ilə heç bir xərc ödəmədən faydalanın",
        "secureStorage": "Təhlükəsiz Saxlama",
        "secureStorageDescription": "Kart məlumatlarınız Stripe serverlərində təhlükəsiz şəkildə saxlanılır, Biela-nın serverlərində deyil. Abunəlik üçün açıq razılığınız olmadan kartınızdan ödəniş tutulmayacaq.",
        "subscriptionAvailable": "Abunəliklər 15 may 2025 tarixindən etibarən əlçatan olacaq."
      },
      "connectingToStripe": "Stripe ilə əlaqə qurulur..."
    },
    "tabs": {
      "billing": "Faturalama",
      "profile": "Profil",
      "deployment": "Yerləşdirmə",
      "identity": "Şəxsiyyət"
    }
  },
  "help": {
    "title": "Sizə necə kömək edə bilərik?",
    "searchPlaceholder": "Sənədləşməni axtarın...",
    "categories": {
      "getting-started": {
        "title": "Başlanğıc üçün",
        "description": "Biela.dev-dən istifadə etməyin əsaslarını öyrənin",
        "articles": [
          "Sürətli Başlanğıc Təlimatı",
          "Platforma Ümumi Baxışı",
          "İlk Layihənizi Yaratmaq",
          "Süni İntellekt İnkişafını Anlamaq"
        ]
      },
      "ai-development": {
        "title": "Süni İntellekt İnkişafı",
        "description": "Süni intellektlə gücləndirilən inkişafı mənimsəyin",
        "articles": [
          "Effektiv Təkliflərin Yazılması",
          "Kod Yaradılması Üçün Ən Yaxşı Təcrübələr",
          "Süni İntellektin Səhvlərinin Aradan Qaldırılması Üçün Məsləhətlər",
          "Ətraflı Süni İntellekt Xüsusiyyətləri"
        ]
      },
      "project-management": {
        "title": "Layihə İdarəetməsi",
        "description": "Layihələrinizi təşkil edin və idarə edin",
        "articles": ["Layihə Strukturlaşdırması", "Komanda Əməkdaşlığı", "Versiya İdarəetməsi", "Yerləşdirmə Seçimləri"]
      }
    },
    "channels": {
      "docs": {
        "name": "Sənədləşmə",
        "description": "Ətraflı təlimatlar və API istinadları"
      },
      "community": {
        "name": "İcma",
        "description": "Digər inkişaf etdiricilərlə əlaqə qurun"
      },
      "github": {
        "name": "GitHub",
        "description": "Məsələləri bildirin və töhfə verin"
      }
    },
    "support": {
      "title": "Hələ də köməyə ehtiyacınız var?",
      "description": "Dəstək komandamız 24/7 hər hansı sual və ya problem üçün sizə kömək etməyə hazırdır.",
      "button": "Dəstəyə Müraciət Edin"
    }
  },
  "getStarted": {
    "title": "Biela.dev-in necə işlədiyini araşdırın",
    "description": "Biela.dev ilə tətbiqinizi dəqiqələr ərzində yaradın. Süni intellektimiz quraşdırmadan yerləşdirməyə qədər bütün inkişaf prosesini avtomatlaşdırır. Süni intellektimizin tətbiqinizi asanlıqla necə yaratdığını burada görürsünüz!",
    "features": {
      "docs": {
        "title": "İnkişaf etdirici Sənədləşməsi",
        "description": "Biela.dev-dən necə istifadə edəcəyinizi asan başa düşülən təlimatlar, məsləhətlər və ən yaxşı təcrübələrlə öyrənin. Həm başlanğıc, həm də təcrübəli inkişaf etdiricilər üçün mükəmməldir!",
        "cta": "İnkişaf etdirici sənədləşməsini araşdırın"
      },
      "support": {
        "title": "Geribildirim və Dəstək",
        "description": "Köməyə ehtiyacınız var və ya geribildirim vermək istəyirsiniz? Dəstəklə əlaqə saxlayın və Biela.dev-i təkmilləşdirməyə kömək edin!",
        "cta": "Geribildirim göndərin"
      },
      "platform": {
        "title": "Platforma Xüsusiyyətləri",
        "description": "Biela.dev-in, veb saytlar və tətbiqləri asanlıqla yaratmağa kömək edən güclü alətlərini kəşf edin. Kodlaşdırmanı süni intellektə buraxın!",
        "cta": "Xüsusiyyətləri araşdırın"
      }
    },
    "video": {
      "title": "Sürətli Başlanğıc Videosu",
      "description": "Biela.dev-in sizin üçün necə tətbiq və veb yarattığını izləyin — asanlıqla!",
      "cta": "Təlimatı izləyin"
    },
    "guide": {
      "title": "Sürətli Başlanğıc Təlimatı",
      "steps": {
        "setup": {
          "title": "Layihənizi dərhal qurun",
          "description": "İnkişaf mühitinizi saniyələr ərzində hazır edin"
        },
        "generate": {
          "title": "Süni intellektlə tam yığma kod yaradın",
          "description": "Süni intellektin sizin üçün istehsalata hazır kod yazmasına icazə verin"
        },
        "features": {
          "title": "Dərhal Xüsusiyyət Yaradılması",
          "description": "Sadə təkliflərlə mürəkkəb xüsusiyyətlər əlavə edin"
        },
        "editor": {
          "title": "Kod yazmadan və ya az kodla redaktor",
          "description": "Tətbiqinizi vizual və ya kod vasitəsilə dəyişdirin"
        },
        "optimize": {
          "title": "Real-vaxtda optimallaşdırın və test edin",
          "description": "Tətbiqinizin mükəmməl işlədiyinə əmin olun"
        },
        "deploy": {
          "title": "Bir kliklə yerləşdirin",
          "description": "Avtomatik yerləşdirmə ilə dərhal yayımlanın"
        }
      }
    },
    "faq": {
      "title": "Tez-tez Verilən Suallar",
      "questions": {
        "what": {
          "question": "Biela.dev nədir?",
          "answer": "Biela.dev, kodlaşdırma bilməsəniz belə, veb saytlar və tətbiqlər yaratmağa kömək edən süni intellektlə işləyən bir platformadır. O, kod yazılmasından yerləşdirməyə qədər bütün inkişaf prosesini avtomatlaşdırır."
        },
        "experience": {
          "question": "Biela.dev-dən istifadə etmək üçün kodlaşdırma təcrübəsinə ehtiyac varmı?",
          "answer": "Xeyr! Biela.dev həm başlanğıc, həm də təcrübəli inkişaf etdiricilər üçün nəzərdə tutulub. Siz süni intellektin köməyi ilə qura bilərsiniz və ya yaranan kodu ehtiyacınıza görə düzəldə bilərsiniz."
        },
        "projects": {
          "question": "Hansı növ layihələr yarada bilərəm?",
          "answer": "Veb saytlar, veb tətbiqlər, mobil tətbiqlər, SaaS platformaları, e-ticarət mağazaları, idarəetmə panelləri və daha çox şey yarada bilərsiniz."
        },
        "edit": {
          "question": "Biela.dev tərəfindən yaradılan kodu redaktə edə bilərəmmi?",
          "answer": "Bəli! Biela.dev, süni intellekt tərəfindən yaradılan kodu fərdiləşdirməyə və ya asan redaktə üçün kod yazmadan/az kodlu redaktordan istifadə etməyə imkan verir."
        },
        "deployment": {
          "question": "Yerləşdirmə necə işləyir?",
          "answer": "Bir kliklə, Biela.dev layihənizi yerləşdirir, onu canlı və istifadəyə hazır edir. Əl ilə server quraşdırmağa ehtiyac yoxdur!"
        },
        "pricing": {
          "question": "Biela.dev pulsuz istifadə edilə bilərmi?",
          "answer": "Biela.dev əsas xüsusiyyətlərlə pulsuz plan təklif edir. Daha inkişaf etmiş alətlər və resurslar üçün isə ödənişli plana keçid edə bilərsiniz."
        },
        "integrations": {
          "question": "Üçüncü tərəf alətləri və ya verilənlər bazalarını inteqrasiya edə bilərəmmi?",
          "answer": "Bəli! Biela.dev, populyar verilənlər bazaları (MongoDB, Firebase, Supabase) və üçüncü tərəf API-ləri ilə inteqrasiyanı dəstəkləyir."
        },
        "help": {
          "question": "Çətinlik çəkirəmsə, kömək haradan ala bilərəm?",
          "answer": "İnkişaf etdirici sənədləşməmizə, Sürətli Başlanğıc Təlimatına baxa və ya kömək mərkəzimiz vasitəsilə dəstəklə əlaqə saxlaya bilərsiniz."
        }
      }
    },
    "cta": {
      "title": "Başlamağa hazırsınız?",
      "description": "Biela.dev ilə ilk layihənizi yaradın və inkişafın gələcəyini yaşayın.",
      "button": "Yeni Layihə Yaradın"
    }
  },
  "confirmDeleteProject": "Layihənin silinməsini təsdiqləyin",
  "confirmRemoveFromFolder": "Qovluqdan çıxarılmasını təsdiqləyin",
  "deleteProjectWarning": "{{projectName}} layihəsini daimi olaraq silmək istədiyinizə əminsinizmi?",
  "removeFromFolderWarning": "{{projectName}} layihəsini {{folderName}} qovluğundan çıxarmaq istədiyinizə əminsinizmi?",
  "confirm": "Təsdiqlə",
  "confirmDeleteFolder": "Qovluğun silinməsini təsdiqləyin",
  "deleteFolderWarning": "{{folderName}} qovluğunu silmək istədiyinizə əminsinizmi? Bu əməliyyatı geri qaytarmaq mümkün deyil.",
  "folderDeletedSuccessfully": "Qovluq uğurla silindi",
  "downloadChat": "Söhbəti endir",
  "inactive.title": "Bu vərəq qeyri-aktivdir",
  "inactive.description": "Bu vərəqi aktiv etmək və tətbiqdən istifadəni davam etdirmək üçün aşağıdakı düyməyə klikləyin.",
  "inactive.button": "Bu vərəqi istifadə et",
  "suggestions": {
    "weatherDashboard": "Hava proqnozu lövhəsi yaradın",
    "ecommercePlatform": "E-ticarət platforması qurun",
    "socialMediaApp": "Sosial media tətbiqi dizayn edin",
    "portfolioWebsite": "Portfolio vebsaytı yaradın",
    "taskManagementApp": "Tapşırıq idarəetmə tətbiqi yaradın",
    "fitnessTracker": "Fitness izləyicisi qurun",
    "recipeSharingPlatform": "Resept paylaşım platforması dizayn edin",
    "travelBookingSite": "Səyahət rezervasiya saytı yaradın",
    "learningPlatform": "Öyrənmə platforması qurun",
    "musicStreamingApp": "Musiqi axını tətbiqi dizayn edin",
    "realEstateListing": "Əmlak siyahısı yaradın",
    "jobBoard": "İş elanları saytı yaradın"
  },
  "pleaseWait": "Zəhmət olmasa gözləyin...",
  "projectsInAll": "Bütün layihələr",
  "projectsInCurrentFolder": "{{folderName}} qovluğundakı layihələr",
  "createYourFirstProject": "İlk layihənizi yaradın",
  "startCreateNewProjectDescription": "Möhtəşəm bir şey yaratmağa başlayın. Layihələriniz yaradıldıqdan sonra burada göstəriləcək.",
  "createProjectBtn": "Yeni Layihə",
  "publishedToHackathon": "Hakatona nəşr olundu",
  "publishToHackathon": "Hakatona nəşr et",
  "refreshSubmission": "Təqdimatı yenilə",
  "hackathonInformation": "Hakaton Məlumatı",
  "selectFolder": "Qovluq seçin",
  "folder": "Qovluq",
  "selectAFolder": "Bir qovluq seçin",
  "thisProject": "bu layihə",
  "projectDeletedSuccessfully": "Layihə uğurla silindi",
  "projectRemovedFromFolder": "Layihə qovluqdan çıxarıldı",
  "unnamedProject": "Adsız layihə",
  "permanentDeletion": "Daimi silmə",
  "removeFromFolder": "{{folderName}}-dən çıxarın",
  "verifiedAccount": "Təsdiqlənmiş hesab",
  "hasSubmittedAMinimumOfOneProject": "Ən azı bir layihə təqdim edib",
  "haveAtLeastActiveReferrals": "Ən azı {{number}} aktiv yönləndirmələriniz var",
  "GoToAffiliateDashBoard": "Tərəfdaş panelinə keçin",
  "LikeOneProjectThatBelongsToAnotherUser": "Başqa bir istifadəçiyə məxsus bir layihəni bəyənin",
  "GoToHackathonPage": "Hackathon səhifəsinə keçin",
  "VibeCodingHackathonStatus": "Vibe Coding Hackathon Statusu",
  "ShowcaseYourBestWork": "Ən yaxşı işinizi nümayiş etdirin",
  "submissions": "Təqdimatlar",
  "qualifyConditions": "Uyğunluq şərtləri",
  "completed": "Tamamlandı",
  "collapseQualifyConditions": "Uyğunluq şərtlərini yığışdırın",
  "expandQualifyConditions": "Uyğunluq şərtlərini genişləndirin",
  "basicParticipation": "Əsas iştirak",
  "complete": "Tamamlandı",
  "incomplete": "Tamamlanmayıb",
  "forTop3Places": "İlk 3 yer üçün",
  "deleteSubmission": "Təqdimatı silin",
  "view": "Baxış",
  "submitMoreProjectsToIncreaseYourChangesOfWining": "Qazanma şansınızı artırmaq üçün daha çox layihə təqdim edin!",
  "removeFromHackathon": "Hackathondan çıxarın?",
  "removeFromHackathonDescription": "Vibe Coding Hackathon-dan <project>{{project}}</project> çıxarmaq istədiyinizə əminsinizmi? Bu əməliyyat geri alına bilməz."
  "cardVerificationRequired": "Kart təsdiqi tələb olunur",
  "pleaseVerifyCard": "Davam etmək üçün kartınızı təsdiqləyin",
  "unlockFeaturesMessage": "Bütün platforma funksiyalarına tam giriş əldə etmək üçün kartınızı yoxlamağınızı tələb edirik. Bu proses tamamilə təhlükəsizdir və Biela.dev-də rahat təcrübə təmin edir. Yoxlama zamanı kartınızdan heç bir ödəniş tutulmayacaq.",
  "freeVerificationNotice": "Doğrulama Faydaları",
  "accessToAllFeatures": "Biela.dev funksiyalarına tam giriş.",
  "enhancedFunctionality": "Təkmilləşdirilmiş platforma performansı.",
  "quickSecureVerification": "Təhlükəsiz və şifrələnmiş yoxlama prosesi.",
  "noCharges": "*Heç bir ödəniş və ya gizli xərclər yoxdur",
  "verificationOnly": " — yalnız yoxlama üçün.",
  "verifyNow": "İndi təsdiqlə",
  "DropFilesToUpload": "Yükləmək üçün faylları buraxın",
  "inactiveTitle": "Bu tab aktiv deyil",
  "inactiveDescription": "Tətbiqdən istifadə etməyə davam etmək üçün aşağıdakı düyməni klikləyin və bu tabı aktiv edin.",
  "inactiveButton": "Bu tabı istifadə et",
  "projectInfo": {
    "information": "Layihə Məlumatı",
    "type": "Layihə Növü",
    "complexity": "Çətinlik",
    "components": "Komponentlər",
    "features": "Xüsusiyyətlər",
    "confidenceScore": "İnam Nöqtəsi",
    "estimationUncertainty": "Təxmini qeyri-müəyyənlik",
    "keyTechnologies": "Əsas Texnologiyalar"
  },
  "projectMetrics": {
    "teamComposition": "Komanda Tərkibi",
    "hoursBreakdown": "Saatların Bölünməsi",
    "timeToMarket": "Bazarə Yüksəlmə Vaxtı",
    "maintenance": "Baxım",
    "aiPowered": "AI ilə Gücləndirilmiş",
    "developerLevel": "Təkmilləşdirici Səviyyəsi",
    "nextGenAI": "Növbəti Nəsil AI",
    "keyBenefits": "Əsas Faydalar",
    "instantDevelopment": "Təcili İnkişaf",
    "noMaintenanceCosts": "Baxım Xərcləri Yoxdur",
    "highConfidence": "Yüksək İnam",
    "productionReadyCode": "İstehsalata Hazır Kodu",
    "immediate": "Təcili",
    "uncertainty": "Qeyri-Müəyyənlik",
    "minimal": "Minimal"
  },
  "loadingMessages": {
    "publish": ["Layihə məlumatlarını əldə edir...", "Ekran şəkli çəkilir...", "Xülasə və təsvir yaradılır..."],
    "refresh": [
      "Cari təqdimat məlumatlarını əldə edir...",
      "Layihə ekran görüntüsü yenilənir...",
      "Layihə detallarını yeniləyir..."
    ]
  },
  "errorMessages": {
    "publish": "\"{{projectName}}\" layihəsini hakatona göndərmək mümkün olmadı. Sorğunuzu işləyərkən problem baş verdi.",
    "refresh": "\"{{projectName}}\" təqdimatını yeniləmək mümkün olmadı. Server layihə məlumatlarınızı yeniləyə bilmədi."
  },
  "actions": {
    "refresh": {
      "title": "Təqdimatı yeniləyir",
      "successMessage": "Layihə təqdimatınız uğurla yeniləndi.",
      "buttonText": "Yenilənmiş təqdimatı baxın"
    },
    "publish": {
      "title": "Hakatona göndərilir",
      "successMessage": "Layihəniz hakatona təqdim edildi.",
      "buttonText": "Hakaton səhifəsində baxın"
    }
  },
  "SupabaseConnect": "Supabase",
  "SupabaseDashboard": "Supabase",
  "RestoreApp": "Bərpa et",
  "SaveApp": "Yadda saxla",
  "ForkChat": "Çəp etmək",
  "BielaTerminal": "Biela Terminalı",
  "UnitTesting": "Vahid testlər",
  "InstallDependencies": "Asılılıqları quraşdır",
  "InstallDependenciesDescription": "Layihə üçün lazım olan bütün paketləri istifadə edərək quraşdırır",
  "BuildProject": "Layihəni qur",
  "BuildProjectDescription": "Layihəni istehsal üçün tərtib və optimallaşdırır",
  "StartDevelopment": "İnkişafı başlat",
  "StartDevelopmentDescription": "Canlı önizləmə üçün inkişaf serverini işə salır",

  "ContextLimitReached": "Kontekst limiti aşılıb",
  "ClaudeContextDescription1": "Bu layihə üçün Claude ilə kontekst həddinə çatmısınız. Narahat olmayın – daha geniş kontekst pəncərəsinə sahib Gemini modelinə keçərək davam edə bilərik.",
  "ClaudeContextDescription2": "Gemini modelləri 5 dəfə daha çox kontekst tutumu təklif edir, bu da bütün kodunuzu, söhbət tarixçənizi saxlamağa və layihənizi fasiləsiz davam etdirməyə imkan verir.",
  "SelectModelToContinue": "Davam etmək üçün model seçin:",
  "Performance": "Performans",
  "UpgradePlan": "premium plan",
  "PremiumFeatureRequired": "Premium xüsusiyyət tələb olunur",
  "LargeContextUpgradeInfo": "Böyük kontekst pəncərəsi modelləri daha yüksək abunə tələb edir. Bu modellərin kilidini açmaq üçün yüksəldin.",
  "PremiumModelsAvailable": "Yüksəltmə ilə mövcud premium modellər:",
  "UpgradeTooltip": "Yüksəltmə ilə aktivləşdirin ",
  "UpgradeTooltipSuffix": "paketi",
  "UpgradeToContinue": "Davam etmək üçün yüksəldin",
  "PremiumBadge": "Premium",
  "AlternativeLimitExplanation": "Görünür, bu layihə üçün süni intellektin emal limiti dolub. Əksər yer idxal edilmiş fayllar tərəfindən istifadə olunur, söhbətin özü deyil.",
  "SuggestedSolutions": "Təklif olunan həllər:",
  "ReimportProject": "Yeni layihə kimi yenidən idxal et",
  "ReimportProjectDescription": "Bu, söhbət tarixçəsini siləcək və kontekst sahəsini boşaldacaq, lakin fayllarınız qorunacaq.",
  "BreakIntoProjects": "Bir neçə layihəyə böl",
  "BreakIntoProjectsDescription": "İşinizi ayrıca inkişaf etdirilə bilən kiçik hissələrə bölün.",
  "ExportWork": "Tamamlanmış işi ixrac et",
  "ExportWorkDescription": "Kontekst sahəsini boşaltmaq üçün tamamlanmış faylları endirin və arxivləşdirin.",
  "AlternativeContextNote": "Mövcud kontekstdən maksimum istifadə etmək üçün istifadə olunmayan faylları və kitabxanaları silməyi və cari inkişaf mərhələsi üçün lazım olan əsas fayllara diqqət yetirməyi düşünün.",
  "ContinueWithSelectedModel": "Seçilmiş modellə davam et",
  "Close": "Bağla",
  "AIModel": "Süni İntellekt Modeli",
  "Active": "Aktiv",
  "Stats": "Statistika",
  "Cost": "Məbləğ",
  "ExtendedThinkingDisabledForModel": "Bu model üçün mövcud deyil",
  "ExtendedThinkingAlwaysOn": "Bu model ilə həmişə aktivdir",

  "NoProjectFound": "Bu adla heç bir layihə tapılmadı.",
  "NoProjectFoundDescription": " Zəhmət olmasa yazılış səhvlərini yoxlayın və yenidən cəhd edin.",
  "limitReached": "Limitinizə çatdınız!",
  "deleteProjectsSupabase": "Zəhmət olmasa bəzi layihələri silin və ya Supabase limitinizi artırın.",
  "goTo": "Keçid et",
  "clickProjectSettings": " layihəyə klikləyin, Layihə Parametrləri, aşağı diyirləyin və klikləyin",
  "delete": "Sil",
  "retrying": "Yenidən cəhd edilir…",
  "retryConnection": "Əlaqəni yenidən sınayın",

  "RegisterPageTitle": "Qeydiyyat – biela.dev",
  "RegisterPageDescription": "biela.dev platformasında hesab yaradın və bütün funksiyalara çıxış əldə edin.",
  "SignUpHeading": "Qeydiyyatdan keç",
  "AlreadyLoggedInRedirectHome": "Artıq daxil olmusunuz! Ana səhifəyə yönləndirilirsiniz...",
  "PasswordsMismatch": "Parollar uyğun gəlmir.",
  "FirstNameRequired": "Ad tələb olunur",
  "LastNameRequired": "Soyad tələb olunur",
  "UsernameRequired": "İstifadəçi adı tələb olunur",
  "EmailRequired": "E-poçt tələb olunur",
  "EmailInvalid": "Zəhmət olmasa, düzgün e-poçt ünvanı daxil edin",
  "TooManyRequests": "Çox sayda sorğu göndərildi, zəhmət olmasa bir az sonra yenidən cəhd edin",
  "SomethingWentWrongMessage": "Bir xəta baş verdi, zəhmət olmasa bir az sonra yenidən cəhd edin",
  "PasswordRequired": "Parol tələb olunur",
  "ConfirmPasswordRequired": "Zəhmət olmasa, parolu təsdiq edin",
  "AcceptTermsRequired": "Xidmət şərtləri və Məxfilik siyasəti ilə razılaşmalısınız",
  "CaptchaRequired": "Zəhmət olmasa CAPTCHA-nı tamamlayın",
  "RegistrationFailed": "Qeydiyyat uğursuz oldu",
  "EmailConfirmationSent": "Təsdiq e-poçtu göndərildi! Zəhmət olmasa e-poçtunuzu təsdiqləyin və daxil olun.",
  "RegistrationServerError": "Qeydiyyat uğursuz oldu (server false cavab verdi).",
  "SomethingWentWrong": "Nəsə yanlış getdi",
  "CheckEmailHeading": "Qeydiyyatı təsdiqləmək üçün e-poçtunuzu yoxlayın",
  "CheckEmailDescription": "Təsdiq linki olan e-poçt göndərmişik.",
  "GoToHomepage": "Ana səhifəyə keç",
  "ReferralCodeOptional": "Tövsiyə kodu (isteğe bağlı)",
  "EnterReferralCode": "Əgər sizi dəvət edən biri varsa, onun göndərdiyi kodu daxil edin.",
  "PasswordPlaceholder": "Parol",
  "ConfirmPasswordPlaceholder": "Parolu təsdiqlə",
  "CreateAccount": "Hesab yarat",
  "AlreadyHaveAccountPrompt": "Artıq hesabınız var?",
  "Login": "Daxil ol",
  "AcceptTermsPrefix": "Mən razıyam",
  "TermsOfService": "Xidmət şərtləri",
  "AndSeparator": "və",
  "PrivacyPolicy": "Məxfilik siyasəti",

  "LoginPageTitle": "Daxil ol – biela.dev",
  "LoginPageDescription": "Hesabınıza daxil olun və ya biela.dev-də giriş edərək bütün funksiyalardan istifadə edin.",
  "LogInHeading": "Daxil ol",
  "EmailOrUsernamePlaceholder": "E-poçt / İstifadəçi adı",
  "ForgotPassword?": "Parolu unutmusunuz?",
  "LoginToProfile": "Profilə daxil olun",
  "UserNotConfirmed": "İstifadəçi hesabı təsdiqlənməyib",
  "ConfirmEmailNotice": "Hesabınızı aktivləşdirmək üçün e-poçtunuzu təsdiqləməlisiniz.",
  "ResendConfirmationEmail": "Təsdiq e-poçtunu yenidən göndər",
  "ResendConfirmationSuccess": "Təsdiq e-poçtu yenidən göndərildi! Zəhmət olmasa e-poçtunuzu yoxlayın.",
  "ResendConfirmationError": "Təsdiq e-poçtu göndərilə bilmədi.",
  "LoginSuccess": "Uğurla daxil oldunuz! Yönləndirilirsiniz...",
  "LoginFailed": "Giriş uğursuz oldu",
  "LoginWithGoogle": "Google ilə daxil ol",
  "LoginWithGitHub": "GitHub ilə daxil ol",
  "SignUpWithGoogle": "Google ilə qeydiyyatdan keçin",
  "SignUpWithGitHub": "GitHub ilə qeydiyyatdan keçin",
  "Or": "və ya",
  "NoAccountPrompt": "Hesabınız yoxdur?",
  "SignMeUp": "Qeydiyyatdan keç",

  "ForgotPasswordPageTitle": "Parolu unutdunuz – biela.dev",
  "ForgotPasswordPageDescription": "biela.dev hesabınızın parolunu sıfırlayın və yenidən giriş əldə edin.",
  "BackToLogin": "Girişə qayıt",
  "ForgotPasswordHeading": "Parolu unutdunuz",
  "ForgotPasswordDescription": "E-poçtunuzu daxil edin və parolunuzu sıfırlamaq üçün təsdiq linki göndərək.",
  "VerificationLinkSent": "Təsdiq linki göndərildi! Zəhmət olmasa e-poçtunuzu yoxlayın.",
  "EnterYourEmailPlaceholder": "E-poçtunuzu daxil edin",
  "Sending": "Göndərilir...",
  "SendVerificationCode": "Təsdiq kodunu göndər",
  "SendVerificationLink": "Doğrulama linkini göndər",
  "InvalidConfirmationLink": "Təsdiq linki etibarsızdır",
  "Back": "Geri",
  "ResetPassword": "Parolu sıfırla",
  "ResetPasswordDescription": "Hesabınız üçün yeni parol yaradın",
  "NewPasswordPlaceholder": "Yeni parol",
  "ConfirmNewPasswordPlaceholder": "Yeni parolu təsdiqlə",
  "ResetPasswordButton": "Parolu sıfırla",
  "PasswordRequirements": "Parol ən az 8 simvoldan ibarət olmalı və böyük hərf, kiçik hərf, rəqəm və xüsusi simvol daxil etməlidir.",
  "PasswordUpdatedSuccess": "Parol uğurla yeniləndi!",

  "affiliateDashboard": "Tərəfdaş Paneli",
  "userDashboard": "İstifadəçi Paneli",
  "returnToAffiliateDashboard": "Tərəfdaş panelinə qayıt",
  "returnToUserDashboard": "İstifadəçi panelinə qayıt",
  "myProfile": "Profilim",
  "viewAndEditYourProfile": "Profilinizi görüntüləyin və redaktə edin",
  "billing": "Faktura",
  "manageYourBillingInformation": "Faktura məlumatlarınızı idarə edin",
  "logout": "Çıxış",
  "logoutDescription": "Hesabınızdan çıxış edin",

  "SupabaseNotAvailable": "Supabase hazırda mövcud deyil, xahiş edirik bir az sonra yenidən cəhd edin.",
  "projectActions": {
    "invalidSlug": "Layihə slug-u etibarsızdır.",
    "transferLimit": "Bu layihənin bu versiyasını artıq bu istifadəçiyə köçürmüsünüz. Yenidən köçürmək üçün dəyişikliklər edin.",
    "downloadSuccess": "Layihə uğurla endirildi!",
    "downloadError": "Layihəni endirmək alınmadı.",
    "exportSuccess": "Söhbət ixrac edildi! Yükləmələr qovluğunu yoxlayın.",
    "exportError": "Söhbəti ixrac etmək alınmadı.",
    "duplicateSuccess": "Söhbət uğurla kopyalandı!",
    "duplicateError": "Söhbəti kopyalamaq alınmadı."
  },
  "enter_new_phone_number": "Yeni telefon nömrəsini daxil edin",
  "enter_new_phone_number_below": "Zəhmət olmasa yeni telefon nömrənizi daxil edin:",
  "new_phone_placeholder": "Yeni telefon nömrəsi",
  "enter_otp_code": "OTP Kodunu Daxil Edin",
  "confirm_phone_message": "Hesabdan istifadə üçün telefon nömrənizi təsdiqləməlisiniz. Kod ({{phone}}) nömrəsinə göndərildi.",
  "wrong_phone": "Yanlış telefon nömrəsi?",
  "resend_sms": "SMS-i yenidən göndər",
  "submit": "Təsdiqlə",

  "tokensAvailable": "mövcud tokenlər",

  "sectionTitle": "Domen İdarəetməsi",
  "addDomainButton": "Domen Adı Əlavə Et",
  "connectCustomDomainTitle": "Xüsusi Domen Adı Bağla",
  "disclaimer": "Qeyd:",
  "disclaimerText": "Uğurlu təsdiqləmə üçün yuxarıdakı bütün DNS qaydalarını düzgün təyin etməlisiniz",
  "domainInputDescription": "Bu layihəyə bağlamaq istədiyiniz domen adını daxil edin.",
  "domainLabel": "Domen Adı",
  "domainPlaceholder": "example.com",
  "cancelButton": "Ləğv et",
  "continueButton": "Domen Adı Əlavə Et",
  "deployingText": "Yayımlanır...",
  "addingText": "Əlavə olunur...",
  "verifyButtonText": "Təsdiqlə",
  "configureDnsTitle": "DNS Qeydlərini Konfiqurasiya Et",
  "configureDnsDescription": "Sahibliyi təsdiqləmək və layihəyə bağlamaq üçün domeninizə aşağıdakı DNS qeydlərini əlavə edin.",
  "tableHeaderType": "Növ",
  "tableHeaderName": "Ad",
  "tableHeaderValue": "Dəyər",
  "note": "Qeyd:",
  "noteText": "DNS dəyişikliklərinin yayılması 48 saata qədər çəkə bilər. Lakin adətən bir neçə dəqiqə və ya saat ərzində qüvvəyə minir.",
  "backButton": "Geri",
  "showDnsButton": "DNS Ayarlarını Göstər",
  "hideDnsButton": "DNS Ayarlarını Gizlət",
  "removeButton": "Sil",
  "dnsSettingsTitle": "Domen DNS Ayarları",
  "removeDomainConfirmTitle": "Domen Adını Sil",
  "removeConfirmationText": "Domen adını silmək istədiyinizə əminsiniz? ",
  "importantCleanupTitle": "Vacib DNS Təmizliyi",
  "cleanupDescription": "Bu domeni layihədən sildikdən sonra, konfiqurasiya zamanı əlavə etdiyiniz DNS qeydlərini də silməyi unutmayın. Bu, təmiz DNS konfiqurasiyasını qorumağa və gələcək problemlərin qarşısını almağa kömək edir.",
  "confirmRemoveButton": "Domen Adını Sil",
  "customConfigTitle": "Xüsusi Domen Konfiqurasiyası",
  "customConfigDescription": "Layihənizə öz domen adlarınızı bağlayın. Layihə Biela-nın əsas domeni vasitəsilə əlçatan olacaq, lakin xüsusi domenlər istifadəçilər üçün peşəkar marka təcrübəsi təmin edir.",
  "defaultLabel": "Varsayılan",
  "statusActive": "Aktiv",
  "statusPending": "Gözləmədə",
  "lastVerifiedText": "Az öncə təsdiqləndi",
  "errorInvalidDomain": "Zəhmət olmasa keçərli domen adı daxil edin (məsələn, example.com)",
  "errorDuplicateDomain": "Bu domen adı artıq layihəyə qoşulub",
  "errorAddFail": "Domen adını əlavə etmək mümkün olmadı.",
  "successAdd": "Domen adı uğurla əlavə edildi! Layihəyə bağlandı.",
  "benefitsTitle": "Domen Üstünlükləri",
  "benefitSecurityTitle": "Təkmilləşdirilmiş Təhlükəsizlik",
  "benefitSecurityDesc": "Bütün xüsusi domenlər avtomatik olaraq SSL sertifikatları ilə qorunur.",
  "benefitPerformanceTitle": "Yüksək Performans",
  "benefitPerformanceDesc": "Qlobal CDN layihənizin sürətli yüklənməsini təmin edir.",
  "benefitBrandingTitle": "Peşəkar Brend",
  "benefitBrandingDesc": "Müntəzəm brend təcrübəsi üçün öz domeninizi istifadə edin.",
  "benefitAnalyticsTitle": "Analitika İnteqrasiyası",
  "benefitAnalyticsDesc": "Xüsusi domenlər analitika platformaları ilə problemsiz işləyir.",
  "meta": {
    "index": {
      "title": "biela.dev | AI-əsas Web və App Yaradıcı – Təkliflərdən Yaradın",
      "description": "Biela.dev ilə fikirlərinizi canlı veb saytlara və tətbiqlərə çevrən. AI-əsas təkliflərdən asanlıqla xüsusi digital məhsullar yaradın."
    },
    "login": {
      "title": "biela.dev Hesabınıza Daxil Olun",
      "description": "AI-əsas layihələrinizi idarə edin və biela.dev panelinə daxil olun."
    },
    "register": {
      "title": "biela.dev Qeydiyyatdan Keçin – AI ilə Yaradın",
      "description": "biela.dev hesabınızı yaradın və AI-əsas təkliflərdən istifadə edərək veb saytlar və tətbiqlər yaradın."
    },
    "dashboard": {
      "title": "Layihələriniz Paneli – biela.dev",
      "description": "AI-əsas layihələrinizi idarə edin, canlı layihələri redaktə edin və inkişaf tarixinizi izləyin."
    },
    "profile": {
      "title": "Profiliniz – biela.dev",
      "description": "biela.dev hesab məlumatlarınızı baxın və yeniləyin, seçimlərinizi idarə edin və süni intellekt təcrübənizi fərdiləşdirin."
    },
    "billing": {
      "title": "Ödəniş – Planınızı biela.dev-də təhlükəsiz idarə edin",
      "description": "Abunəliyinizi idarə etmək, ödəniş üsullarını yeniləmək və biela.dev planınıza nəzarət etmək üçün ödəniş ayarlarına daxil olun."
    }
  },
  "transferProject": "Nüsxəni paylaş",
  "transferSecurityNoteDescription": "Qəbul edən şəxs bu layihənin surətinə və onunla əlaqəli bütün resurslara tam giriş əldə edəcək.",
  "transferProjectDescription": "Layihənin bir nüsxəsini ötürmək istədiyiniz şəxsin istifadəçi adını və ya e-poçt ünvanını daxil edin.",
  "transferProjectLabel": "İstifadəçi adı və ya e-poçt",
  "transferProjectPlaceholder": "johnsmith və ya <EMAIL>",
  "transferButton": "Köçür",
  "transferSecurityNote": "Təhlükəsizlik qeydi:",
  "dontHavePermisionToTransfer": "Bu layihəni köçürmək icazəniz yoxdur",
  "transferProjectUserNotFound": "{{ user }} istifadəçisi tapılmadı!",
  "transferErrorOwnAccount": "Bir layihəni öz hesabınıza köçürə bilməzsiniz.",
  "transferError": "Layihəni köçürərkən səhv baş verdi",
  "transferSuccess": "{{ user }} istifadəçisinə uğurla köçürüldü",
  "enterValidEmailUsername": "Zəhmət olmasa istifadəçi adı və ya e-poçt daxil edin",
  "enterMinValidEmailUsername": "Zəhmət olmasa etibarlı istifadəçi adı (ən azı 3 simvol) və ya e-poçt ünvanı daxil edin",
  "youWillStillHaveAccess": "Siz hələ də orijinal layihəyə giriş əldə edəcəksiniz",
  "newChangesWillNotAffect": "Yeni dəyişikliklər digər istifadəçinin layihəsinə təsir etməyəcək",
  "ProjectInformationNotLoaded": "Layihə məlumatları yüklənə bilmədi.",
  "projectInfoTitle": "Layihə Məlumatı",
  "generalInformationProject": "Layihəniz haqqında ümumi məlumat.",
  "raname": "Adını dəyiş",
  "lastlySavedAt": "Son yadda saxlanma vaxtı:",
  "noSavedAppVersion": "Yadda saxlanmış tətbiq versiyası yoxdur.",
  "Owner": "Sahib",
  "TechStack": "Texnologiya yığını",
  "FeatureCount": "Xüsusiyyət sayı",
  "UniqueComponentCount": "Unikal komponent sayı",
  "inviteCollaborator": {
    "title": "İŞTİRAKÇINI DƏVƏT ET",
    "message": "{{email}} ünvanını bu layihəyə əməkdaşlığa dəvət etmək üzrəsiniz.",
    "action": "Dəvət göndər"
  },
  "removeCollaborator": {
    "title": "İŞTİRAKÇINI SİL",
    "message": "{{email}} ünvanını bu layihədən silmək üzrəsiniz.",
    "action": "Sil"
  },
  "database": {
    "title": "Verilənlər Bazası Bağlantıları",
    "fields": {
      "created": "Yaradılıb",
      "size": "Ölçü",
      "region": "Region",
      "version": "Versiya"
    },
    "button": {
      "connected": "Bağlıdır",
      "connect": "Bağla",
      "notConnected": "Bağlı deyil"
    }
  },
  "status": {
    "Online": "Onlayn",
    "Degraded": "Zəifləmiş",
    "Restoring": "Bərpa olunur",
    "Restored": "Bərpa edildi",
    "Creating": "Yaradılır",
    "Provisioning": "Hazırlanır",
    "Coming Up": "Başlanır",
    "Deleting": "Silinir",
    "Deleted": "Silindi",
    "Pausing": "Fasilə verilir",
    "Paused": "Fasilə verildi",
    "Inactive": "Aktiv deyil",
    "Suspended": "Dayandırılıb",
    "Resuming": "Bərpa olunur",
    "Updating": "Yenilənir",
    "Migrating": "Köçürülür",
    "Maintenance": "Texniki xidmət",
    "Restarting": "Yenidən başladılır",
    "Backup in progress": "Ehtiyat nüsxə yaradılır",
    "Restore in progress": "Bərpa edilir",
    "Failed": "Uğursuz oldu",
    "Unknown": "Naməlum",
    "Loading...": "Yüklənir...",
    "Not Found": "Tapılmadı",
    "Error": "Xəta"
  },
  "rename": "Adını dəyiş",

  "titleChat": "{{typeCapitalized}} Çatı",
  "titleMessage": "{{typeCapitalized}} Mesaj",
  "dialogDescriptionText": "Siz {{type}} {{description}} üzrə hərəkət edirsiniz.",
  "confirmText": "Bu çatı {{type}} etmək istədiyinizə əminsiniz?",

  "deleteButton": "Sil",
  "deleteInfinitive": "silmək",
  "deleteNoun": "silmə",

  "duplicateButton": "Dublikat",
  "duplicateInfinitive": "dublikat etmək",
  "duplicateNoun": "dublikat",

  "downloadButton": "Yüklə",
  "downloadInfinitive": "yükləmək",
  "downloadNoun": "yükləmə",

  "exportButton": "İxrac et",
  "exportInfinitive": "ixrac etmək",
  "exportNoun": "ixrac",

  "forkButton": "Çəkmək",
  "forkInfinitive": "çəkmək",
  "forkNoun": "çat",

  "rollbackButton": "Geri al",
  "rollbackInfinitive": "geri almaq",
  "rollbackNoun": "geri alma",

  "saveButton": "Yadda saxla",
  "saveInfinitive": "saxlamaq",
  "saveNoun": "saxlama",

  "restoreButton": "Bərpa et",
  "restoreInfinitive": "bərpa etmək",
  "restoreNoun": "bərpa",

  "loadingOverlayChat": "Zəhmət olmasa gözləyin, {{type}} çat edilir.",
  "loadingOverlayMessages": "Zəhmət olmasa gözləyin, {{type}} mesajlar edilir.",

  "exportProjectChat": "Layihə çatını ixrac et",
  "downloadProjectFiles": "Layihə fayllarını yüklə",
  "pleaseSelectCommit": "Bərpa üçün bir commit seçin:",
  "refresh": "Yenilə",
  "savingApp": "Yadda saxlanılır...",

  "confirmationDelete": "Sil",
  "confirmationDuplicate": "Dublikat",
  "confirmationDownload": "Yüklə",
  "confirmationExport": "İxrac et",
  "confirmationFork": "Çəkmək",
  "confirmationRollback": "Geri al",
  "confirmationSave": "Yadda saxla",
  "confirmationRestore": "Bərpa et",

  "dialogTitleChat": "Bu çat {{typeCapitalized}} edilsin?",
  "dialogTitleData": "Məlumatlarınız {{typeCapitalized}} edilsin?",
  "dialogLoadingChat": "Çat {{typeCapitalized}} olunur...",
  "dialogLoadingData": "Mesajlar {{typeCapitalized}} olunur...",
  "dialogDescription": "Siz {{type}} edirsiniz: {{description}}",
  "dialogConfirm": "Əminsiniz ki, {{type}} etmək istəyirsiniz?",
  "buttonCancel": "Ləğv et",
  "previousAppVersion": "əvvəlki tətbiq versiyası",
  "CreatingDatabase": "Verilənlər bazası yaradılır...",
  "DatabaseDashboard": "Verilənlər bazası paneli",
  "ConnectDatabase": "Verilənlər bazasına qoşul",
  "DatabaseConnectionOptions": "Verilənlər bazası bağlantı seçimləri",
  "LoadingYourSupabaseProjects": "Supabase layihələriniz yüklənir...",
  "NoSupabaseProjectsFound": "Heç bir Supabase layihəsi tapılmadı. İlk əvvəl Supabase-ə qoşulun.",
  "DatabaseOptions": "Verilənlər bazası seçimləri",
  "DisconnectDatabase": "Verilənlər bazasının bağlantısını kəs",
  "OpenSupabaseDashboard": "Supabase panelini aç",
  "LinkExistingProject": "Mövcud layihəni bağla",
  "IncludeDataWhenDuplicating": "Köçürərkən məlumatı daxil et",
  "DuplicateProject": "Layihəni kopyala",
  "CreateNewSupabaseDB": "Yeni Supabase DB yaradın",
  "UseExistingSupabaseDB": "Mövcud Supabase DB istifadə edin",
  "DisconnectSupabaseDB": "Supabase DB bağlantısını kəsin",
  "SharedFrom": "Paylaşıldığı yer",

  "TokensExhausted": "Tokenləriniz bitdi",
  "TokensExhaustedDescription": "Layihənizi davam etdirmək üçün bir seçim seçin:",
  "upgradePlan": "Planımı yüksəlt",
  "buyMoreTokens": "Daha çox token al",
  "seeScreenshot": "Ekran görüntüsünü görüntülə"
}
