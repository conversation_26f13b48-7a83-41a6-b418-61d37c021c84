{"Claude4Description": "Leistungsstärkste KI für Premium-Websites", "Claude4FeatureFirst": "Hochwertiger Code", "Claude4FeatureSecond": "Erweiterte Funktionalität", "Claude4FeatureThird": "Überlegenes Design", "Claude4Performance": "Ausgezeichnet", "Claude4Cost": "Premium", "Claude4ContextWindow": "Standard-Kontextfenster (200K Tokens)", "GeminiProDescription": "Vielseitige KI mit breitem Kontext", "GeminiProFeatureFirst": "Zuverlässiger Code", "GeminiProFeatureSecond": "Verarbeitet komplexe Eingaben", "GeminiProFeatureThird": "Gute Designqualität", "GeminiProPerformance": "<PERSON><PERSON> gut", "GeminiProCost": "Gehobenes Mittelklasse", "GeminiProContextWindow": "Großes Kontextfenster (1M+ Tokens)", "GeminiFlashDescription": "Schnelle und kostengünstige Option", "GeminiFlashFeatureFirst": "<PERSON><PERSON><PERSON>", "GeminiFlashFeatureSecond": "Grundfunktionen", "GeminiFlashFeatureThird": "Einfache Designs", "GeminiFlashPerformance": "Gut", "GeminiFlashCost": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GeminiFlashContextWindow": "Großes Kontextfenster (1M+ Tokens)"}