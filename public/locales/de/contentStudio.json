{"title": "Content Studio", "searchPlaceholder": "Bilder suchen...", "loading": "Wird geladen...", "cannotDeleteNonEmptyFolder": "Ordner ist nicht leer und kann nicht gelöscht werden. Bitte verschieben oder löschen Sie zuerst den Inhalt.", "confirmDeleteTitle": "Löschen bestätigen", "confirmDeleteMessage": "Möchten Sie die ausgewählten Bilder wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.", "confirmDeleteSingleImageTitle": "Löschen bestätigen", "confirmDeleteSingleImageMessage": "Möchten Sie dieses Bild wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.", "confirmDeleteFolderTitle": "Ordner löschen", "confirmDeleteFolderMessage": "Möchten Sie den Ordner \"{{folderName}}\" wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.", "delete": "Löschen", "deleteFolder": "Ordner löschen", "useInProject": "Im Projekt verwenden", "uploadImages": "Bilder hochladen", "moveToFolder": "In Ordner verschieben", "addTags": "Tags hinzufügen", "clearSelection": "Auswahl aufheben", "sort.newest": "Neueste", "sort.oldest": "Älteste", "sort.name-asc": "Name (A-Z)", "sort.name-desc": "Name (Z-A)", "sort.size-asc": "Größe (kleinste zuerst)", "sort.size-desc": "Größe (größte zuerst)", "view.grid": "Rasteransicht", "view.list": "Listenansicht", "filters": "Filter", "applyFilters": "<PERSON><PERSON> anwenden", "noImages": "<PERSON><PERSON> Bilder gefunden.", "noFolders": "<PERSON><PERSON> ve<PERSON>üg<PERSON>.", "createFolder": "Ordner er<PERSON>llen", "renameFolder": "Ordner umbenennen", "confirm": "Bestätigen", "cancel": "Abbrechen", "close": "Schließen", "folderSearchPlaceholder": "Ordner suchen...", "foldersLabel": "<PERSON><PERSON><PERSON>", "searchResults": "Suchergebnisse", "noFoldersFound": "<PERSON><PERSON>dner gefunden", "allFiles": "Alle Dateien", "myFolders": "<PERSON><PERSON>", "createSubfolder": "<PERSON><PERSON><PERSON><PERSON> erstellen", "folderNames": {"all": "Alle Dateien", "recent": "Zuletzt verwendet", "favorites": "<PERSON><PERSON>"}, "sort": {"label": "<PERSON><PERSON><PERSON><PERSON>", "newest": "<PERSON><PERSON><PERSON>", "oldest": "<PERSON><PERSON><PERSON>", "nameAsc": "Name (A-Z)", "nameDesc": "Name (Z-A)", "sizeAsc": "Größe (kleinste zuerst)", "sizeDesc": "Größe (größte zuerst)"}, "noImagesFound": "<PERSON><PERSON> Bilder gefunden", "noImagesHelp": "Laden Sie einige Bilder hoch oder wählen Sie einen anderen Ordner", "viewDetails": "Details anzeigen", "view": "Anzeigen", "tableHeader": {"name": "Name", "size": "Größe", "type": "<PERSON><PERSON>", "date": "Datum"}, "imageDetails": {"title": "Bilddetails", "videoTitle": "Videodetails"}, "videoUrl": "Video-URL", "edit": "<PERSON><PERSON><PERSON>", "save": "Speichern", "tags": "Tags", "add": "Hinzufügen", "addTagPlaceholder": "Tag hinzufügen...", "uploaded": "Hochgeladen:", "fileInfo": "Dateiinformationen", "type": "<PERSON><PERSON>", "size": "Größe", "dimensions": "Abmessungen", "location": "Speicherort", "imageUrl": "Bild-URL", "urlCopied": "URL kopiert!", "noTags": "Keine <PERSON>s", "move": "Verschieben", "deleteImageTitle": "Bild löschen", "deleteImageMessage": "Möchten Sie dieses Bild wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.", "deleteImageConfirm": "Bild löschen", "moveDialog": {"title": "Elemente verschieben", "selectDestination": "Zielordner auswählen:", "rootOption": "Stammverzeichnis (Alle Dateien)", "noFolders": "<PERSON><PERSON> weiteren Ordner verfügbar", "moveButton": "Elemente verschieben"}, "editConfirm": {"title": "Änderungen speichern", "message": "Möchten Sie die Änderungen an diesem Element wirklich speichern? Die Metadaten in Ihrer Bibliothek werden aktualisiert.", "confirm": "Änderungen speichern"}, "clearFilters": "<PERSON><PERSON>", "dateRange": "Datumsbereich", "dateFrom": "<PERSON>", "dateTo": "Bis", "searchTagsPlaceholder": "Tags suchen...", "noTagsMatchSearch": "<PERSON><PERSON> passenden Tags gefunden", "noTagsAvailable": "Keine Tags verfügbar", "selected": "ausgewählt", "uploadFiles": "<PERSON><PERSON>", "uploading": "Wird hochgeladen...", "dropFilesHere": "<PERSON><PERSON> hier <PERSON>gen", "dragDropFilesHere": "<PERSON><PERSON> hierher ziehen und ablegen", "releaseToUpload": "Zum Hochladen loslassen", "dragDropOrBrowse": "<PERSON><PERSON>hen Sie Bilddateien hierher oder klicken Sie unten auf die Schaltfläche, um zu durchsuchen", "browseFiles": "<PERSON><PERSON>", "fileTitle": "Dateititel", "tagsForFile": "Tags für", "pressEnterToAddTag": "<PERSON><PERSON><PERSON> Si<PERSON> Enter, um jedes Tag hinzuzufügen", "folderLocation": "Ordnerposition", "rootAllFiles": "Stammverzeichnis (Alle Dateien)", "noFilesSelected": "<PERSON><PERSON> ausgewählt", "selectFilesHint": "<PERSON><PERSON><PERSON>en Sie Dateien aus, indem Sie sie in den Upload-Bereich ziehen oder die Durchsuchen-Schaltfläche verwenden", "supportedFormats": "Unterstützte Formate: JPG, PNG, GIF, WebP", "maxFileSize": "Maximale Dateigröße: 5MB", "filesToUpload": "<PERSON><PERSON> zum Hochladen", "addMore": "Me<PERSON> hinzufügen", "selectFilesToUpload": "<PERSON>ien zum Hochladen auswählen", "upload": "Hochladen", "errorMaxFiles": "Sie können nur bis zu {{count}} <PERSON><PERSON> auswählen.", "errorFileSizeExceeded": "\"{{fileName}}\" überschreitet die zulässige Dateigröße. Wird übersprungen...", "errorNoValidFiles": "<PERSON><PERSON> gültigen Dateien zum Hochladen.", "errorMissingFileTitle": "Alle Dateien müssen einen Namen haben.", "errorFileTypeNotSupported": "Der Dateityp von {{fileName}} wird nicht unterstützt. Unterstützte Formate: JPG, PNG, GIF, WebP.", "uploadError": "<PERSON><PERSON> ist ein Fehler aufgetreten.", "placeholderAddTag": "Tag hinzufügen und Enter drücken", "enterTagsForSingle": "Tags für 1 Element eingeben", "enterTagsForMultiple": "Tags für mehrere Elemente eingeben", "addAnotherTagPlaceholder": "Weiteres Tag hinzufügen...", "commonlyUsedTags": "Häufig verwendete Tags", "addTagsButton": "Tags hinzufügen", "suggestedTags": {"app": "App", "code": "Code", "coding": "Programmierung", "coffee": "<PERSON><PERSON><PERSON>", "css": "CSS", "design": "Design", "development": "Entwicklung", "html": "HTML", "javascript": "JavaScript", "laptop": "Laptop", "mobile": "Mobil", "programming": "Programmierung", "react": "React", "screen": "Bildschirm", "web": "Web", "webdev": "Webentwicklung", "workspace": "Arbeitsbereich"}, "createNewFolder": "Neuen Ordner erstellen", "folderName": "Ordnername", "placeholderFolderName": "Ordnername e<PERSON>", "parentFolder": "Übergeordneter Ordner", "errorFolderNameRequired": "Ordnername ist erforderlich", "errorFolderAlreadyExists": "Ein Ordner mit diesem Namen existiert bereits", "newFolderName": "<PERSON><PERSON><PERSON> Ordnername", "placeholderNewFolderName": "Neuen Ordnernamen eingeben", "errorFileMustHaveName": "Die Datei muss einen Namen haben.", "chooseAnImage": "<PERSON><PERSON>hle ein Bild", "uploadSomeImages": "Lade einige Bilder hoch oder wähle einen anderen Ordner"}