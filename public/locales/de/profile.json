{"userInformation": "Benutzerinformationen", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Nachname", "username": "<PERSON><PERSON><PERSON><PERSON>", "phone": "Telefon", "preferredLanguage": "Bevorzugte Sprache", "billingInformation": "Rechnungsinformationen", "addressLine1": "Adresse Zeile 1", "addressLine2": "Adresse Zeile 2", "city": "Stadt", "zipCode": "<PERSON><PERSON><PERSON><PERSON>", "referralCode": "Empfehlungscode", "referralLink": "Empfehlungslink", "referral": "Empfehlung", "location": "<PERSON><PERSON>", "site": "Website", "email": "E-Mail", "contactInfo": "Kontaktinformationen", "websiteUrl": "Website-URL", "accountDeletion": "Kontolöschung", "deleteAccount": "Konto löschen", "deleteAccountWarning": "Im Falle einer Löschung werden alle Ihre Projekte und persönlichen Daten entfernt. Sie verlieren auch die Kontrolle über Projekte in Ihren Organisationen.", "connectAccount": "Verbinden Sie Ihr {{platform}}-Konto", "connectNow": "Jetzt verbinden", "connected": "Verbunden", "personalWebsite": "Persönliche Website", "facebook": "Facebook", "twitterX": "Twitter/X", "linkedin": "LinkedIn", "youtube": "YouTube", "instagram": "Instagram", "tiktok": "TikTok", "Country": "Land", "ChangePassword": "Passwort ändern", "Updating": "Aktualisieren...", "Save": "Speichern", "Edit": "<PERSON><PERSON><PERSON>", "CurrentPassword": "Aktuelles Passwort", "NewPassword": "Neues Passwort", "RepeatNewPassword": "Neues Passwort wiederholen", "AllPasswordFieldsAreRequired": "Alle Passwortfelder sind erforderlich", "NewPasswordsDoNotMatch": "Neue Passwörter stimmen nicht überein", "PasswordMustBeAtLeastCharactersLong": "Das Passwort muss mindestens {{minLength}} <PERSON><PERSON><PERSON> lang sein", "UserNotFound": "Benutzer nicht gefunden", "CurrentPasswordIsIncorrect": "Das aktuelle Passwort ist falsch", "PasswordUpdated": "Ihr Passwort wurde erfolgreich aktualisiert!", "ErrorWhileUpdatingPassword": "Fehler beim Aktualisieren des Passworts", "Success": "Erfolg", "Error": "<PERSON><PERSON>", "updateAccount": "Ihr Konto wurde erfolgreich aktualisiert!", "failedUpdateAccount": "Etwas ist schiefgelaufen. Bitte versuchen Sie es erneut.", "Close": "Schließen", "copyReferralCode": "Empfehlungscode kopieren", "copyReferralLink": "Empfehlungslink kopieren", "purchaseDetails": "Kaufdetails", "dateLabel": "Datum", "planLabel": "Plan", "amountLabel": "Betrag", "tokensLabel": "Tokens:", "whatsNext": "Wie geht es weiter?", "yourTokensAvailable": "Ihre Tokens sind jetzt in Ihrem Konto verfügbar", "purchaseDetailsStored": "Kaufdetails wurden in Ihrem Konto gespeichert", "viewPurchaseHistory": "Sie können Ihre Kaufhistorie im Abrechnungsbereich einsehen", "thankYou": "Danke!", "purchaseSuccessful": "Ihr Kauf war erfolgreich", "startCoding": "Jetzt mit dem Programmieren beginnen", "tokenTopUpComingSoon": "Token-Aufladung bald verfügbar", "finalisingPricing": "Wir finalisieren unser Preissystem, um dir die besten Preise und Mengenrabatte zu bieten.", "getReady": "Mach dich bereit für flexible Token-Pakete, die deine Entwicklungsanforderungen unterstützen.", "gotIt": "Verstanden", "checkBackSoon": "<PERSON><PERSON><PERSON> bald wieder vorbei, um To<PERSON>s zu kaufen", "buyExtraTokens": "Zusätzliche Tokens kaufen", "paymentMethod": "Zahlungsmethode", "howManyTokens": "Wie viele Tokens möchtest du?", "placeholderEnterTokens": "<PERSON><PERSON><PERSON> der Tokens eingeben...", "units": "Einheiten", "availableDiscounts": "Verfügbare <PERSON>", "priceLabel": "Preis:", "discountAppliedLabel": "Angewendeter Rabatt:", "at": "Bei", "perMillion": "pro Million Tokens", "purchaseTokens": "Tokens kaufen", "minimumPurchaseError": "Mindestkauf sind {minTokens} Tokens ($1)", "minimumTokenError": "Der minimale To<PERSON>-<PERSON><PERSON> beträ<PERSON> {{minTokens}}", "maximumTokenError": "H<PERSON>chstens {maxTokens} Tokens kaufen", "modalTitle": "<PERSON><PERSON>hle einen Plan oder lade bei Bedarf Tokens auf", "modalSubtitle": "Wähle aus drei flexiblen Plänen oder kaufe jederzeit zusätzliche Tokens, um ohne Einschränkungen weiter zu entwickeln.", "highlightExtraTokens": "zusätzliche Tokens", "toggleMonthly": "<PERSON><PERSON><PERSON>", "toggleYearly": "<PERSON><PERSON><PERSON><PERSON> (10% sparen)", "buttonNeedMoreTokens": "Benötigst du mehr Tokens?", "recentInvoicesTitle": "Letzte Rechnungen", "recentInvoicesDescription": "Verfolge deine Abrechnungshistorie und lade Rechnungen herunter", "tableHeaderDate": "Datum", "tableHeaderDescription": "Beschreibung", "tableHeaderAmount": "Betrag", "tableHeaderStatus": "Status", "tableHeaderActions": "Aktionen", "noInvoicesFound": "<PERSON><PERSON>nungen gefunden", "actionPreviewInvoice": "Rechnung anzeigen", "actionDownloadInvoice": "Rechnung herunterladen", "planDescription": "Perfekt für Einzelpersonen, die Entwicklung erkunden, neue Fähigkeiten erlernen oder an eigenen Projekten arbeiten.", "planNameBasicPlus": "Basic Plus", "planNameStarterPlus": "Starter Plus", "planNamePremiumPlus": "Premium Plus", "planPriceBasicPlus": "$100/Jahr", "planPriceStarterPlus": "$200/Jahr", "planPricePremiumPlus": "$300/Jahr", "planTokensBasicPlus": "5M Tokens pro Monat", "planTokensStarterPlus": "25M Tokens pro Monat", "planTokensPremiumPlus": "80M Tokens pro Monat", "priceSeparator": "/", "currentPlanLabel": "Aktueller Plan", "upgradePlanLabel": "Plan upgraden", "minimumBulkPurchaseError": "Der minimale To<PERSON>-<PERSON><PERSON> beträgt {{amount}}", "maximumBulkPurchaseError": "Der maximale Token-<PERSON><PERSON> beträgt {{amount}}", "million": "Million", "millions": "<PERSON>en", "nextPage": "Nächste Seite", "previousPage": "Vorherige Seite", "paginationStatus": "Anzeige von {{from}} bis {{to}} von insgesamt {{total}} <PERSON><PERSON><PERSON><PERSON><PERSON>"}