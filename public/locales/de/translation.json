{"whatWouldYouLikeToBuild": "Verwandeln Sie Ihre Idee in Minuten in eine Live-Website oder App", "whatWouldYouLikeToBuildSubtitle": "<PERSON><PERSON> du es <1>dir vorstel<PERSON></1> kannst, kannst du es <5>programmieren</5>.", "fromIdeaToDeployment": "Starte kostenlos. Programmiere alles. Verwandle mit jeder Eingabe deine Fähigkeiten in Chancen.", "codePlaceholder": "Wenn Si<PERSON> es sich vorstellen können, kann BIELA es programmieren. Was sollen wir heute tun?", "defaultPlaceholder": "Wie kann ich Ihnen heute helfen? <PERSON>sen <PERSON> uns gemeinsam etwas Großartiges erschaffen", "checkingFeatures": "Überprüfe die Funktionen", "checklists": "Checklisten", "runUnitTestsSuggestionTitle": "Vorschlag", "runUnitTestsSuggestionMessage": "Möchten Sie Unit Tests für Ihr Projekt ausführen?", "runUnitTestsPrimaryButton": "Unit Tests ausführen", "runUnitTestsSecondaryButton": "<PERSON><PERSON><PERSON><PERSON>", "createDatabaseTitle": "Datenbankerstellung", "createDatabaseMessage": "Möchten Sie eine Datenbank für Ihr Projekt erstellen?", "createDatabasePrimaryButton": "Datenbank erstellen", "createDatabaseSecondaryButton": "<PERSON><PERSON><PERSON><PERSON>", "extendedThinking": "Erweitertes Denken", "extendedThinkingTooltip": "Ermöglicht der KI, vor der Antwort tiefer nachzudenken", "firstResponseOnly": "Nur erste Antwort", "always": "Immer", "AIReasoning": "KI-Logik", "thinking": "Denken", "attachFile": "<PERSON><PERSON>", "voiceInput": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectLanguage": "Sprache für Spracheingabe auswählen", "languageSelectionDisabled": "Sprachauswahl während der Aufnahme deaktiviert", "notAvailableInFirefox": "Diese Funktion ist in Firefox nicht verfügbar, aber in Chrome und Safari", "enhancePrompt": "Eingabeaufforderung verbessern", "cleanUpProject": "Projekt bereinigen", "clearChatHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ChatNotFound": "<PERSON><PERSON> oder Benutzer gefunden", "ChatClearFailedServer": "Chatverlauf konnte nicht gelöscht werden (Serverfehler).", "NoMessagesToClear": "<PERSON><PERSON> Nachrichten zum Löschen", "ChatClearSuccess": "Chatverlauf <PERSON>!", "ChatClearFailedUnexpected": "<PERSON>t<PERSON><PERSON><PERSON> konnte nicht gelöscht werden (unerwarteter Fehler).", "ClearChatTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ClearChatConfirm": "Sind <PERSON> sic<PERSON>, dass Sie den Chatverlauf löschen möchten?", "ClearChatIrreversible": "Diese Aktion kann nicht rückgängig gemacht werden.", "ClearChatPro": "Spart Tokens für bessere Leistung.", "ClearChatCon": "Die KI erinnert sich nicht mehr an frühere Nachrichten, hat aber weiterhin Zugriff auf den Code.", "Advantage": "<PERSON><PERSON><PERSON>", "Disadvantage": "<PERSON><PERSON><PERSON>", "showPrompt": "Aufforderung anzeigen", "hidePrompt": "Aufforderung ausblenden", "sendButton": "Senden", "abortButton": "Abbrechen", "inspirationTitle": "Brauchen Sie Inspiration? Probieren Sie eines dieser Beispiele:", "cleanUpPrompt": "Bereinigen Sie das Projekt, indem Si<PERSON>herstellen, dass keine einzelne Datei mehr als 300 Codezeilen enthält. Zerlegen Sie große Dateien in kleinere, modulare Komponenten, während Sie die volle Funktionalität beibehalten. Entfernen Sie alle ungenutzten Dateien, Codes, Komponenten und überflüssigen Daten, die nicht mehr benötigt werden. <PERSON><PERSON><PERSON>, dass alle Komponenten weiterhin korrekt verbunden und funktionsfähig sind, um Unterbrechungen im bestehenden System zu vermeiden. Acht<PERSON> Si<PERSON> darauf, dass keine Änderungen Fehler verursachen oder bestehende Funktionen beeinträchtigen. Ziel ist es, das Projekt hinsichtlich Effizienz, Wartbarkeit und Klarheit zu optimieren.", "checklistPrompt": "<PERSON><PERSON><PERSON> Si<PERSON> sich meine ursprüngliche Eingabe an, verstehen Sie das Ziel Punkt für Punkt und erstellen Sie für mich eine Checkliste, in der alles, was er<PERSON><PERSON><PERSON> wurde, mit einem grünen H<PERSON> und alles, was noch zu tun ist, mit einem roten Häkchen markiert ist.", "personalPortfolioIdea": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> eine persönliche Portfolio-Website mit dunklem Design", "recipeFinderIdea": "Bauen Sie eine Rezeptfinder-App, die Mahlzeiten basierend auf verfügbaren Zutaten vorschlägt", "weatherDashboardIdea": "Entwerfen Sie ein Wetter-Dashboard mit animierten Hintergründen", "habitTrackerIdea": "Entwickeln Sie einen Habit Tracker mit Fortschrittsvisualisierung", "loading": "<PERSON><PERSON><PERSON>", "checkingYourAuthenticity": "Ihre Echtheit wird überprüft", "error": "<PERSON><PERSON>", "succes": "Erfolg!", "tryAgain": "<PERSON><PERSON><PERSON> versuchen", "dashboard": "Dashboard", "getStartedTitle": "Loslegen", "getStartedSub": "<PERSON>t<PERSON>cke<PERSON>, wie Biela.dev funktioniert", "createProject": "Neues Projekt erstellen", "createProjectSub": "<PERSON> auf neu starten", "uploadProject": "Projekt hochladen", "uploadProjectSub": "Bestehendes Projekt importieren", "importChat": "Importiere den Chat", "importChatSub": "Bestehendes Chat importieren", "createFolder": "Neuen Ordner erstellen", "createFolderSub": "Organisieren Sie Ihre Projekte", "editProjectName": "Projektnamen bearbeiten", "editName": "Name bearbeiten", "cancel": "Abbrechen", "changeFolder": "Ordner ändern", "save": "Speichern", "importing": "Importiere...", "importFolder": "Ordner importieren", "giveTitle": "Titel vergeben", "projects": "Projekte", "searchProjects": "Projekte durchsuchen...", "becomeAffiliate": "Partner werden", "exclusiveGrowth": "Exklusive Wachstumsvorteile", "lifetimeEarnings": "Lebenslange Einnahmen", "highCommissions": "Hohe Provisionen", "earnCommission": "Verdienen Sie 50% Provision bei Ihrem ersten Verkauf", "joinAffiliateProgram": "<PERSON><PERSON><PERSON>e an unserem Partnerprogramm teil", "folders": "<PERSON><PERSON><PERSON>", "organizeProjects": "Organisieren Sie Ihre Projekte nach Kategorie", "createNewFolder": "Neuen Ordner erstellen", "enterFolderName": "Ordnernamen eingeben", "editFolder": "Ordner bearbeiten", "deleteFolder": "Ordner löschen", "all": "Alle", "webProjects": "Webprojekte", "mobilepps": "Mobile Apps", "developmentComparison": "Entwicklungsvergleich", "traditionalVsAI": "<PERSON><PERSON> vs. KI", "traditional": "<PERSON><PERSON>", "standardApproach": "Standardansatz", "developmentCost": "Entwicklungskosten", "developmentTime": "Entwicklungszeit", "costSavings": "Kosteneinsparungen", "reducedCosts": "Reduzierte <PERSON>", "timeSaved": "Gesparte Zeit", "fasterDelivery": "Schnellere Lieferung", "bielaDevAI": "Biela.dev KI", "nextGenDevelopment": "Entwicklung der nächsten Generation", "developmentCosts": "Entwicklungskosten", "openInGitHub": "<PERSON><PERSON>", "downloadProject": "<PERSON>jekt <PERSON>", "duplicateProject": "Projekt duplizieren", "openProject": "<PERSON><PERSON><PERSON>", "deleteProject": "Projekt löschen", "confirmDelete": "Diesen Ordner löschen?", "invoicePreview": "<PERSON><PERSON><PERSON>", "settings": {"title": "Einstellungen", "deployment": {"AdvancedSettings": {"advanced-settings": "Erweiterte Einstellungen", "configure-advanced-deployment-options": "Konfigurieren Sie erweiterte Bereitstellungsoptionen", "server-configuration": "Serverkonfiguration", "memory-limit": "Speicherlimit", "region": "Region", "security-settings": "Sicherheitseinstellungen", "enable-ddos-protection": "DDoS-Schutz aktivieren", "protect-against-distributed": "<PERSON><PERSON><PERSON> gegen Distributed Denial-of-Service-Angriffe", "ip-whitelisting": "IP-Whitelist", "restrict-acces-to": "Zugriff auf bestimmte IP-Adressen einschränken", "deployment-options": "Bereitstellungsoptionen", "auto-deploy": "Automatische Bereitstellung", "automatically-deploy-when": "Automatisch bereitstellen, wenn in den Hauptbranch gepusht wird", "preview-deployments": "Bereitstellungen in der Vorschau anzeigen", "create-preview-deployments": "Vorschau-Bereitstellungen für Pull Requests erstellen"}, "BuildSettings": {"build-and-deployment-settings": "Build- und Bereitstellungseinstellungen", "build-command": "Build-Befehl", "override": "Überschreiben", "output-directory": "Ausgabeverzeichnis", "override2": "Überschreiben"}, "DatabaseConfiguration": {"database-configuration": "Datenbankkonfiguration", "configure-your-db-connections": "Konfigurieren Sie Ihre Datenbankverbindungen und -einstellungen", "database-type": "Datenbanktyp", "connection-string": "Verbindungszeichenfolge", "your-db-credentials": "Ihre Datenbankzugangsdaten werden verschlüsselt und sicher gespeichert", "database-settings": "Datenbankeinstellungen", "pool-size": "Pool-Größe", "require": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prefer": "Bevorzugen", "disable": "Deaktivieren", "add-database": "Datenbank hinzufügen"}, "DomainSettings": {"domain-settings": "Domain-Einstellungen", "configure-your-custom-domain": "Konfigurieren Sie Ihre benutzerdefinierten Domains und SSL-Zertifikate", "custom-domain": "Benutzerdefinierte Domain", "add": "Hinzufügen", "ssl-certificate": "SSL-Zertifikat", "auto-renew-ssl-certificates": "SSL-Zertifikate automatisch erneuern", "auto-renew-before-expiry": "SSL-Zertifikate vor Ablauf automatisch erneuern", "force-https": "HTTPS erzwingen", "redirect-all-http-traffic": "Alle HTTP-An<PERSON><PERSON> zu HTTPS umleiten", "active-domain": "Aktive Domains", "remove": "Entfernen"}, "EnvironmentVariables": {"environment-variables": "Umgebungsvariablen", "configure-environment-variables": "Konfigurieren Sie die Umgebungsvariablen für Ihre Bereitstellungen", "all-enviroments": "Alle Umgebungen", "environment-variables2": "Umgebungsvariablen", "preview": "Vorschau", "development": "Entwicklung", "create-new": "<PERSON><PERSON>", "key": "Schlüssel", "value": "Wert", "save-variable": "Variable speichern", "you-can-also-import": "Sie können auch Variablen aus einer .env-Datei importieren:", "import-env-file": ".env-<PERSON><PERSON> importieren"}, "ProjectConfiguration": {"project-configuration": "Projektkonfiguration", "config-your-project-settings": "Konfigurieren Sie Ihre Projekteinstellungen und Bereitstellungsoptionen", "project-url": "Projekt-URL", "framework": "Framework", "repo": "Repository", "branch": "Branch", "main": "main", "development": "development", "staging": "staging"}}, "identity": {"unverified": {"title": "Identitätsverifizierung", "description": "Verifizieren Sie Ihre Identität, um Biela.dev zu nutzen", "subtitle": "Sicherer Verifizierungsprozess", "processServers": "Alle Kartendaten werden sicher auf den Servern von Stripe gespeichert, nicht auf den <PERSON> von <PERSON>", "processCharge": "Ihre Karte wird nicht ohne Ihre ausdrückliche Zustimmung für ein Abonnement belastet", "processBenefits": "Biela.dev ist bis zum 15. Mai 2025 für verifizierte Konten vollständig kostenlos", "verifyStripe": "Mit K<PERSON>it- oder Debitkarte verifizieren", "verifyStripeDescription": "Verbinden Sie Ihre Zahlungsmethode zur Verifizierung", "verifyNow": "Jetzt verifizieren"}, "verified": {"title": "Identität verifiziert", "description": "Ihre Identität wurde erfolgreich verifiziert", "paymentMethod": "Zahlungsmethode", "cardEnding": "Karte endet mit", "updatePayment": "Zahlungsmethode aktualisieren", "untilDate": "Bis zum 15. Mai 2025", "freeAccess": "Kostenloser Zugang", "freeAccessDescription": "Genießen Sie vollen Zugang zu Biela.dev kostenlos", "secureStorage": "Sichere Speicherung", "secureStorageDescription": "Ihre Kartendaten werden sicher auf den Servern von Stripe gespeichert, nicht auf den <PERSON>n von Biela. Ihre Karte wird ohne Ihre ausdrückliche Zustimmung für ein Abonnement nicht belastet.", "subscriptionAvailable": "Abonnements sind ab dem 15. Mai 2025 verfügbar."}, "connectingToStripe": "Verbindung zu Stripe wird hergestellt..."}, "tabs": {"billing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "profile": "Profil", "deployment": "Bereitstellung", "identity": "Identität"}}, "help": {"title": "Wie können wir Ihnen helfen?", "searchPlaceholder": "Dokumentation durchsuchen...", "categories": {"getting-started": {"title": "<PERSON><PERSON><PERSON>", "description": "Erfahren Sie die Grundlagen der Nutzung von Biela.dev", "articles": ["Schnellstart-Anleitung", "Plattformübersicht", "<PERSON><PERSON><PERSON><PERSON> Ihres ersten Projekts", "Verstehen der KI-Entwicklung"]}, "ai-development": {"title": "KI-Entwicklung", "description": "Meistern Sie die KI-gestützte Entwicklung", "articles": ["Effektives Schreiben von Eingaben", "Best Practices für die Code-Erstellung", "Tipps zur Fehlersuche bei KI", "Erweiterte KI-Funktionen"]}, "project-management": {"title": "Projektmanagement", "description": "Organisieren und verwalten Sie Ihre Projekte", "articles": ["Projektstruktur", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Versionskontrolle", "Bereitstellungsoptionen"]}}, "channels": {"docs": {"name": "Dokumentation", "description": "Umfassende Anleitungen und API-Referenzen"}, "community": {"name": "Community", "description": "Vernetzen Sie sich mit anderen Entwicklern"}, "github": {"name": "GitHub", "description": "Melden Sie Probleme und tragen Sie bei"}}, "support": {"title": "<PERSON>rauchen Si<PERSON> noch Hilfe?", "description": "Unser Support-Team ist rund um die Uhr für Ihre Fragen oder Probleme erreichbar.", "button": "Support kontaktieren"}}, "getStarted": {"title": "Erkunden Sie, wie Biela.dev funktioniert", "description": "Erstellen Sie Ihre App in wenigen Minuten mit Biela.dev. Unsere KI automatisiert den gesamten Entwicklungsprozess – von der Einrichtung bis zur Bereitstellung. So baut unsere KI Ihre App mühelos!", "features": {"docs": {"title": "Entwicklerdokumentation", "description": "<PERSON><PERSON><PERSON><PERSON>, wie Sie Biela.dev nutzen können – mit leicht verständlichen Anleitungen, Tipps und Best Practices. Perfekt für Anfänger und erfahrene Entwickler!", "cta": "Entwicklerdokumentation erkunden"}, "support": {"title": "Feed<PERSON> und Support", "description": "Brauchen Sie Hilfe oder haben Sie Feedback? Kontaktieren Sie den Support und helfen Si<PERSON> mit, Biela.dev zu verbessern!", "cta": "Feed<PERSON> senden"}, "platform": {"title": "Plattformfunktionen", "description": "Entdecken Sie die leistungsstarken Tools, die Biela.dev bietet, um mühelos Web<PERSON> und Apps zu erstellen. Lassen Sie die KI für Sie coden!", "cta": "Funktionen erkunden"}}, "video": {"title": "Schnellstart-Video", "description": "<PERSON><PERSON>, wie Biela.dev für Si<PERSON> baut – mü<PERSON>ose <PERSON>- und Webentwicklung!", "cta": "Tutoria<PERSON> an<PERSON>hen"}, "guide": {"title": "Schnellstart-Anleitung", "steps": {"setup": {"title": "Richten Sie Ihr Projekt sofort ein", "description": "Machen Sie Ihre Entwicklungsumgebung in Sekunden startklar"}, "generate": {"title": "Erzeugen Sie mit KI vollständigen Code", "description": "Lassen Sie die KI produktionsreifen Code für Sie schreiben"}, "features": {"title": "Sofortige Feature-Erstellung", "description": "Fügen Sie komplexe Funktionen mit einfachen Eingaben hinzu"}, "editor": {"title": "No-Code & Low-Code Editor", "description": "Bearbeiten Sie Ihre App visuell oder über den Code"}, "optimize": {"title": "Optimieren und in Echtzeit testen", "description": "<PERSON><PERSON><PERSON>, dass Ihre App einwandfrei funktioniert"}, "deploy": {"title": "Mit einem Klick bereitstellen", "description": "Gehen Sie sofort live mit automatischer Bereitstellung"}}}, "faq": {"title": "Häufig gestellte Fragen", "questions": {"what": {"question": "Was ist Biela.dev?", "answer": "Biela.dev ist eine KI-gestützte Plattform, die Ihnen hilft, Webseiten und Apps zu erstellen – auch wenn Sie nicht programmieren können. Sie automatisiert den gesamten Entwicklungsprozess, von der Code-Erstellung bis zur Bereitstellung."}, "experience": {"question": "Benötige ich Programmiererfahrung, um Biela.dev zu nutzen?", "answer": "Nein! Biela.dev ist sowohl für Anfänger als auch für erfahrene Entwickler konzipiert. Sie können mit KI-Unterstützung bauen oder den generierten Code nach Bedarf anpassen."}, "projects": {"question": "<PERSON>e Arten von Projekten kann ich erstellen?", "answer": "Sie können Webseiten, Web-Apps, mobile Apps, SaaS-Plattformen, E-Commerce-Shops, Verwaltungs-Dashboards und vieles mehr erstellen."}, "edit": {"question": "Kann ich den von Biela.dev generierten Code bearbeiten?", "answer": "Ja! Biela.dev ermöglicht es Ihnen, den KI-generierten Code anzupassen oder den No-Code/Low-Code Editor für einfache Änderungen zu verwenden."}, "deployment": {"question": "Wie funktioniert die Bereitstellung?", "answer": "Mit einem Klick stellt Biela.dev Ihr Projekt bereit, sodass es live und nutzbar ist. Eine manuelle Servereinrichtung ist nicht erford<PERSON>lich!"}, "pricing": {"question": "Ist Biela.dev kostenlos nutzbar?", "answer": "Biela.dev bietet einen kostenlosen Plan mit Basisfunktionen. Für erweiterte Tools und Ressourcen können Sie auf einen Premium-Plan upgraden."}, "integrations": {"question": "Kann ich Drittanbieter-Tools oder Datenbanken integrieren?", "answer": "Ja! Biela.dev unterstützt Integrationen mit beliebten Datenbanken (MongoDB, Firebase, Supabase) und Drittanbieter-APIs."}, "help": {"question": "Wo kann ich Hilfe erhalten, wenn ich nicht weiterkomme?", "answer": "Sie können unsere Entwicklerdokumentation, die Schnellstart-Anleitung konsultieren oder sich über unser Hilfezentrum an den Support wenden."}}}, "cta": {"title": "Bereit zum Loslegen?", "description": "<PERSON><PERSON><PERSON>n Sie Ihr erstes Projekt mit Biela.dev und erleben Sie die Zukunft der Entwicklung.", "button": "Neues Projekt erstellen"}}, "confirmDeleteProject": "Projekt löschen bestätigen", "confirmRemoveFromFolder": "Entfernen aus Ordner bestätigen", "deleteProjectWarning": "<PERSON>öchten Sie {{projectName}} wirklich dauerhaft löschen?", "removeFromFolderWarning": "<PERSON><PERSON><PERSON>en Sie {{projectName}} wirk<PERSON> aus {{folderName}} entfernen?", "confirm": "Bestätigen", "confirmDeleteFolder": "Ordner löschen bestätigen", "deleteFolderWarning": "<PERSON>d <PERSON><PERSON> sic<PERSON>, dass Si<PERSON> {{folderName}} löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.", "folderDeletedSuccessfully": "Ordner erfolgreich <PERSON>t", "downloadChat": "<PERSON>t herunt<PERSON><PERSON>n", "inactiveTitle": "Dieser Tab ist inaktiv", "inactiveDescription": "<PERSON>licke auf die Schaltfläche unten, um diesen Tab zu aktivieren und die App weiter zu nutzen.", "inactiveButton": "<PERSON><PERSON> verwenden", "suggestions": {"weatherDashboard": "<PERSON><PERSON><PERSON>-Dashboard", "ecommercePlatform": "Baue eine E-Commerce-Plattform", "socialMediaApp": "Gestalte eine Social-Media-App", "portfolioWebsite": "<PERSON><PERSON><PERSON> eine Portfolio-Website", "taskManagementApp": "<PERSON>rst<PERSON> eine Aufgabenverwaltungs-App", "fitnessTracker": "<PERSON><PERSON> einen Fitness-Tracker", "recipeSharingPlatform": "Gestalte eine Plattform zum Teilen von Rezepten", "travelBookingSite": "<PERSON><PERSON><PERSON> eine Reisebuchungsseite", "learningPlatform": "Baue eine Lernplattform", "musicStreamingApp": "Gestalte eine Musik-Streaming-App", "realEstateListing": "<PERSON><PERSON><PERSON> eine Immobilienanzeige", "jobBoard": "Baue eine Jobbörse"}, "pleaseWait": "Bitte warten...", "projectsInAll": "Projekte in Alle", "projectsInCurrentFolder": "Projekte in {{folderName}}", "createYourFirstProject": "<PERSON><PERSON>ellen Sie Ihr erstes Projekt", "startCreateNewProjectDescription": "Beginnen Sie mit etwas Erstaunlichem. Ihre Projekte werden hier angezeigt, sobald Sie sie erstellen.", "createProjectBtn": "Neues Projekt", "publishedToHackathon": "Veröffentlicht im Hackathon", "publishToHackathon": "Zum Hackathon veröffentlichen", "refreshSubmission": "Einreichung aktualisieren", "hackathonInformation": "Hackathon-Informationen", "selectFolder": "Ordner auswählen", "folder": "<PERSON><PERSON><PERSON>", "selectAFolder": "Einen Ordner auswählen", "thisProject": "<PERSON><PERSON>", "projectDeletedSuccessfully": "Projekt erfolgreich <PERSON>", "projectRemovedFromFolder": "Projekt aus Ordner entfernt", "unnamedProject": "Unbenanntes Projekt", "permanentDeletion": "Dauerhafte Löschung", "removeFromFolder": "Aus {{folderName}} entfernen", "verifiedAccount": "Verifiziertes Konto", "hasSubmittedAMinimumOfOneProject": "Hat mindestens ein Projekt eingereicht", "haveAtLeastActiveReferrals": "Haben Sie mindestens {{number}} aktive Empfehlungen", "GoToAffiliateDashBoard": "Zum Partner-Dashboard gehen", "LikeOneProjectThatBelongsToAnotherUser": "G<PERSON><PERSON><PERSON><PERSON> ein Projekt, das einem anderen Benutzer gehört", "GoToHackathonPage": "<PERSON><PERSON>-<PERSON><PERSON> gehen", "VibeCodingHackathonStatus": "Vibe Coding Hackathon-Status", "ShowcaseYourBestWork": "Zeigen Sie Ihre besten Arbeiten", "submissions": "Einreichungen", "qualifyConditions": "Qualifikationsbedingungen", "completed": "Abgeschlossen", "collapseQualifyConditions": "Qualifikationsbedingungen einklappen", "expandQualifyConditions": "Qualifikationsbedingungen erweitern", "basicParticipation": "Grundteilnahme", "complete": "Abgeschlossen", "incomplete": "Unvollständig", "forTop3Places": "Für die Top 3 Plätze", "deleteSubmission": "Einreichung löschen", "view": "<PERSON><PERSON><PERSON>", "submitMoreProjectsToIncreaseYourChangesOfWining": "Reichen Sie mehr Projekte ein, um Ihre Gewinnchancen zu erhöhen!", "removeFromHackathon": "Vom Hackathon entfernen?", "removeFromHackathonDescription": "<PERSON>d <PERSON> sic<PERSON>, dass Si<PERSON> <project>{{project}}</project> vom Vibe Coding Hackathon entfernen möchten? Diese Aktion kann nicht rückgängig gemacht werden.", "cardVerificationRequired": "Kartenverifizierung erforderlich", "pleaseVerifyCard": "Bitte verifizieren Sie Ihre Karte, um fortzufahren", "unlockFeaturesMessage": "Um vollen Zugriff auf alle Plattformfunktionen zu erhalten, bitten wir <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON> zu verifizieren. Dieser Prozess ist vollkommen sicher und sorgt für ein reibungsloses Erlebnis auf Biela.dev. Während der Verifizierung werden keine Gebühren von Ihrer Karte abgebucht.", "freeVerificationNotice": "Verifizierungsvorteile", "accessToAllFeatures": "Voller Zugriff auf die Funktionen von Biela.dev.", "enhancedFunctionality": "Verbesserte Plattformleistung.", "quickSecureVerification": "Sicherer und verschlüsselter Verifizierungsprozess.", "noCharges": "*<PERSON><PERSON> Gebühren oder versteckte Kosten", "verificationOnly": " — nur zur Verifizierung.", "verifyNow": "Jetzt verifizieren", "DropFilesToUpload": "<PERSON><PERSON> zum Hochladen hierher ziehen", "projectInfo": {"information": "Projektinformation", "type": "Projekttyp", "complexity": "Komplexität", "components": "Komponenten", "features": "Funktionen", "confidenceScore": "Vertrauensscore", "estimationUncertainty": "Schätzunsicherheit", "keyTechnologies": "Schlüsseltechnologien"}, "projectMetrics": {"teamComposition": "Teamzusammensetzung", "hoursBreakdown": "Stundenaufteilung", "timeToMarket": "Time-to-Market", "maintenance": "Wartung", "aiPowered": "KI-unterstützt", "developerLevel": "Entwicklerlevel", "nextGenAI": "Next-Generation KI", "keyBenefits": "<PERSON><PERSON>tvor<PERSON><PERSON>", "instantDevelopment": "Sofortige Entwicklung", "noMaintenanceCosts": "<PERSON><PERSON>", "highConfidence": "Ho<PERSON> Zuversicht", "productionReadyCode": "Produktionsbereiter Code", "immediate": "Sofort", "uncertainty": "Ungewissheit", "minimal": "Minimal"}, "loadingMessages": {"publish": ["Projektinformationen werden abgerufen...", "Screenshot wird erstellt...", "Zusammenfassung und Beschreibung werden generiert..."], "refresh": ["Aktuelle Einreichungsdaten werden abgerufen...", "Projekt-Screenshot wird aktualisiert...", "Projektdetails werden aktualisiert..."]}, "errorMessages": {"publish": "\"{{projectName}}\" konnte nicht zum Hackathon veröffentlicht werden. Es gab ein Problem bei der Verarbeitung Ihrer Anfrage.", "refresh": "\"{{projectName}}\"-Einreichung konnte nicht aktualisiert werden. Der Server konnte Ihre Projektinformationen nicht aktualisieren."}, "actions": {"refresh": {"title": "Einreichung aktualisieren", "successMessage": "Ihre Projekteinsendung wurde erfolgreich aktualisiert.", "buttonText": "Aktualisierte Einreichung anzeigen"}, "publish": {"title": "Zum Hackathon veröffentlichen", "successMessage": "Ihr Projekt wurde erfolgreich zum Hackathon eingereicht.", "buttonText": "<PERSON><PERSON> <PERSON> Hackathon-Seite anzeigen"}}, "SupabaseConnect": "Supabase", "SupabaseDashboard": "Supabase", "RestoreApp": "Wiederherstellen", "SaveApp": "Speichern", "ForkChat": "Abzweigen", "BielaTerminal": "Biela-Terminal", "UnitTesting": "Komponententest", "InstallDependencies": "Abhängigkeiten installieren", "InstallDependenciesDescription": "Installiert alle benötigten Pakete für das Projekt mit", "BuildProject": "Projekt bauen", "BuildProjectDescription": "Kompiliert und optimiert das Projekt für die Produktion mit", "StartDevelopment": "Entwicklung starten", "StartDevelopmentDescription": "Startet den Entwicklungsserver für eine Live-Vorschau mit", "NoProjectFound": "<PERSON>in Projekt mit diesem Namen gefunden.", "NoProjectFoundDescription": " Bitte überprüfen Sie auf Tippfehler und versuchen Sie es erneut.", "ContextLimitReached": "Kontextlimit erreicht", "ClaudeContextDescription1": "Du hast das Kontextlimit für dieses Projekt mit <PERSON> erre<PERSON>. <PERSON><PERSON> – wir können mit einem Gemini-Modell mit deutlich größerem Kontextfenster fortfahren.", "ClaudeContextDescription2": "Gemini-<PERSON>le bieten bis zu 5-mal mehr Kontextkapazität, sodass du deinen gesamten Code und Chatverlauf behalten und dein Projekt ohne Unterbrechung weiterentwickeln kannst.", "SelectModelToContinue": "<PERSON><PERSON><PERSON><PERSON> ein Modell, um fortzufahren:", "Performance": "Le<PERSON><PERSON>", "UpgradePlan": "ein Premium-Plan", "PremiumFeatureRequired": "Premium-Funktion erforderlich", "LargeContextUpgradeInfo": "Modelle mit großem Kontextfenster erfordern eine höhere Abo-Stufe. Upgrade erforderlich, um diese Modelle freizuschalten.", "PremiumModelsAvailable": "Premium-Modelle mit Upgrade verfügbar:", "UpgradeTooltip": "Freischalten durch Upgrade auf ", "UpgradeTooltipSuffix": "<PERSON><PERSON>", "UpgradeToContinue": "Zum Fortfahren upgraden", "PremiumBadge": "Premium", "AlternativeLimitExplanation": "<PERSON><PERSON> sche<PERSON>, dass die KI das Verarbeitungs­limit für dieses Projekt erreicht hat. Der größte Teil des Speicherplatzes wird von den importierten Dateien verwendet, nicht vom Chat selbst.", "SuggestedSolutions": "Vorgeschlagene Lösungen:", "ReimportProject": "Projekt neu importieren", "ReimportProjectDescription": "<PERSON><PERSON><PERSON> wird der Chatverlauf gelöscht und Kontextplatz freigegeben, während deine Dateien erhalten bleiben.", "BreakIntoProjects": "In mehrere Projekte aufteilen", "BreakIntoProjectsDescription": "Teile deine Arbeit in kleinere Komponenten auf, die separat entwickelt werden können.", "ExportWork": "Abgeschlossene Arbeit exportieren", "ExportWorkDescription": "Lade fertige Dateien herunter und archiviere sie, um Kontextplatz freizugeben.", "AlternativeContextNote": "Um das Beste aus dem verfügbaren Kontext herauszuholen, entferne ungenutzte Dateien oder Bibliotheken und konzentriere dich auf die Kerndateien, die für die aktuelle Entwicklungsphase benötigt werden.", "ContinueWithSelectedModel": "Mit ausgewähltem Modell fortfahren", "Close": "Schließen", "AIModel": "KI-Modell", "Active": "Aktiv", "Stats": "Statistiken", "Cost": "<PERSON><PERSON>", "ExtendedThinkingDisabledForModel": "<PERSON><PERSON> diesem Modell nicht verfügbar", "ExtendedThinkingAlwaysOn": "Immer aktiv mit diesem Modell", "limitReached": "Sie haben Ihr Limit erreicht!", "deleteProjectsSupabase": "Bitte löschen Sie einige Projekte oder erhöhen Sie Ihr Limit in Supabase.", "goTo": "<PERSON><PERSON><PERSON> zu", "clickProjectSettings": " klicken Sie auf das Projekt, Projekteinstellungen, scrollen Si<PERSON> nach unten und klicken Sie", "delete": "Löschen", "retrying": "<PERSON><PERSON><PERSON> Versuch…", "retryConnection": "Verbindung erneut versuchen", "RegisterPageTitle": "Registrieren – biela.dev", "RegisterPageDescription": "<PERSON>rstellen Sie Ihr Konto auf biela.dev und schalten Sie alle Funktionen frei.", "SignUpHeading": "Registrieren", "AlreadyLoggedInRedirectHome": "Sie sind bereits eingeloggt! Weiterleitung zur Startseite...", "PasswordsMismatch": "Passwörter stimmen nicht überein.", "FirstNameRequired": "Vorname ist erforderlich", "LastNameRequired": "Nachname ist erforderlich", "UsernameRequired": "Benutzername ist erforderlich", "EmailRequired": "E-Mail-Adresse ist erforderlich", "EmailInvalid": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein", "TooManyRequests": "<PERSON>u viele Anfragen, bitte versuche es später erneut", "SomethingWentWrongMessage": "Etwas ist schiefgelaufen, bitte versuche es später erneut", "PasswordRequired": "Passwort ist erforderlich", "ConfirmPasswordRequired": "Bitte bestätigen Sie Ihr Passwort", "AcceptTermsRequired": "Sie müssen die Nutzungsbedingungen und die Datenschutzrichtlinie akzeptieren", "CaptchaRequired": "Bitte vervollständigen Sie das CAPTCHA", "RegistrationFailed": "Registrierung fehlgeschlagen", "EmailConfirmationSent": "Bestätigungs-E-Mail wurde gesendet! Bitte bestätigen Sie Ihre E-Mail und melden Sie sich dann an.", "RegistrationServerError": "Registrierung fehlgeschlagen (Server hat false zurückgegeben).", "SomethingWentWrong": "Etwas ist schiefgelaufen", "CheckEmailHeading": "Überprüfen Sie Ihre E-Mails zur Bestätigung Ihrer Registrierung", "CheckEmailDescription": "Wir haben Ihnen eine E-Mail mit einem Bestätigungslink gesendet.", "GoToHomepage": "Zur Startsei<PERSON>", "ReferralCodeOptional": "Empfehlungscode (optional)", "EnterReferralCode": "<PERSON><PERSON>en Si<PERSON> einen Empfehlungscode ein, wenn <PERSON><PERSON> von einem bestehenden Nutzer eingeladen wurden.", "PasswordPlaceholder": "Passwort", "ConfirmPasswordPlaceholder": "Passwort bestätigen", "CreateAccount": "<PERSON><PERSON> er<PERSON>", "AlreadyHaveAccountPrompt": "Sie haben bereits ein Konto?", "Login": "Anmelden", "AcceptTermsPrefix": "Ich stimme den", "TermsOfService": "Nutzungsbedingungen", "AndSeparator": "und", "PrivacyPolicy": "Datenschutzrichtlinien", "LoginPageTitle": "Anmelden – biela.dev", "LoginPageDescription": "Greifen Sie auf Ihr Konto zu oder melden Si<PERSON> sich bei biela.dev an, um alle Funktionen zu nutzen.", "LogInHeading": "Anmelden", "EmailOrUsernamePlaceholder": "E-Mail / Benutzername", "ForgotPassword?": "Passwort vergessen?", "LoginToProfile": "Anmelden zum Profil", "UserNotConfirmed": "Benutzerkonto nicht bestätigt", "ConfirmEmailNotice": "Sie müssen Ihre E-Mail-Adresse bestätigen, um Ihr Konto zu aktivieren.", "ResendConfirmationEmail": "Bestätigungs-E-Mail erneut senden", "ResendConfirmationSuccess": "Bestätigungs-E-Mail wurde erneut gesendet! Bitte überprüfen Sie Ihren Posteingang.", "ResendConfirmationError": "Fehler beim Senden der Bestätigungs-E-Mail.", "LoginSuccess": "Anmeldung erfolgreich! Weiterleitung...", "LoginFailed": "Anmeldung fehlgeschlagen", "LoginWithGoogle": "Mit Google anmelden", "LoginWithGitHub": "Mit GitHub anmelden", "SignUpWithGoogle": "Mit Google registrieren", "SignUpWithGitHub": "Mit GitHub registrieren", "Or": "ODER", "NoAccountPrompt": "Noch kein Konto?", "SignMeUp": "Jetzt registrieren", "ForgotPasswordPageTitle": "Passwort vergessen – biela.dev", "ForgotPasswordPageDescription": "Setzen Sie das Passwort Ihres biela.dev-Kontos zurück und erhalten Sie wieder Zugriff.", "BackToLogin": "Zurück zur Anmeldung", "ForgotPasswordHeading": "Passwort vergessen", "ForgotPasswordDescription": "Geben Sie Ihre E-Mail-Adresse ein und wir senden Ihnen einen Verifizierungslink zur Zurücksetzung Ihres Passworts.", "VerificationLinkSent": "Verifizierungslink gesendet! Bitte überprüfen Sie Ihre E-Mails.", "EnterYourEmailPlaceholder": "E-Mail-Ad<PERSON><PERSON>", "Sending": "Wird gesendet...", "SendVerificationCode": "Verifizierungscode senden", "SendVerificationLink": "Bestätigungslink senden", "InvalidConfirmationLink": "Ungültiger Bestätigungslink", "Back": "Zurück", "ResetPassword": "Passwort zurücksetzen", "ResetPasswordDescription": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein neues Passwort für Ihr Konto", "NewPasswordPlaceholder": "Neues Passwort", "ConfirmNewPasswordPlaceholder": "Neues Passwort bestätigen", "ResetPasswordButton": "Passwort zurücksetzen", "PasswordRequirements": "Das Passwort muss mindestens 8 Zeichen lang sein und einen Großbuchstaben, einen Kleinbuchstaben, eine <PERSON> und ein Sonderzeichen enthalten.", "PasswordUpdatedSuccess": "Passwort erfolgreich aktualisiert!", "affiliateDashboard": "Affiliate-Dashboard", "userDashboard": "Benutzer-Dashboard", "returnToAffiliateDashboard": "<PERSON><PERSON><PERSON> zum Affiliate-Dashboard", "returnToUserDashboard": "<PERSON><PERSON><PERSON> zum Benutzer-Dashboard", "myProfile": "<PERSON><PERSON>", "viewAndEditYourProfile": "<PERSON>il an<PERSON>hen und bearbeiten", "billing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "manageYourBillingInformation": "Ihre Abrechnungsinformationen verwalten", "logout": "Abmelden", "logoutDescription": "<PERSON> abmelden", "SupabaseNotAvailable": "Supabase ist derzeit nicht verfügbar. Bitte versuchen Sie es später erneut.", "projectActions": {"invalidSlug": "Ungültiger Projekt-Slug.", "transferLimit": "Sie haben diese Version des Projekts bereits an diesen Benutzer übertragen. Nehmen Sie Änderungen vor, um erneut zu übertragen.", "downloadSuccess": "Projekt erfolgreich heruntergeladen!", "downloadError": "Projekt konnte nicht heruntergeladen werden.", "exportSuccess": "Chat exportiert! Überprüfe deinen Download-Ordner.", "exportError": "Chat konnte nicht exportiert werden.", "duplicateSuccess": "Chat erfolgreich dupliziert!", "duplicateError": "Chat konnte nicht dupliziert werden."}, "enter_new_phone_number": "Neue Telefonnummer eingeben", "enter_new_phone_number_below": "Bitte gib unten deine neue Telefonnummer ein:", "new_phone_placeholder": "Neue Telefonnummer", "enter_otp_code": "OTP-Code eingeben", "confirm_phone_message": "Um dein Konto zu nutzen, musst du deine Telefonnummer bestätigen. Gib den an ({{phone}}) gesendeten Code ein.", "wrong_phone": "Falsche Telefonnummer?", "resend_sms": "SMS erneut senden", "tokensAvailable": "verfügbare Token", "sectionTitle": "Domainverwaltung", "addDomainButton": "Domainname Hinzufügen", "connectCustomDomainTitle": "Benutzerdefinierten Domainnamen Verbinden", "disclaimer": "Haftungsausschluss:", "disclaimerText": "Um erfolgreich zu verifizieren, müssen alle oben genannten DNS-Regeln korrekt eingestellt werden", "domainInputDescription": "<PERSON><PERSON><PERSON> Si<PERSON> den Domainnamen ein, den Si<PERSON> mit diesem Projekt verbinden möchten.", "domainLabel": "Domainname", "domainPlaceholder": "example.com", "cancelButton": "Abbrechen", "continueButton": "Domainname Hinzufügen", "deployingText": "Bereitstellung...", "addingText": "Hinzufügen...", "verifyButtonText": "Verifizieren", "configureDnsTitle": "DNS-Einträge Konfigurieren", "configureDnsDescription": "Fügen Sie die folgenden DNS-Einträge zu Ihrer Domain hinzu, um den Besitz zu verifizieren und sie mit diesem Projekt zu verbinden.", "tableHeaderType": "<PERSON><PERSON>", "tableHeaderName": "Name", "tableHeaderValue": "Wert", "note": "<PERSON><PERSON><PERSON><PERSON>:", "noteText": "DNS-Änderungen können bis zu 48 Stunden dauern, um sich zu verbreiten. In der Regel treten sie jedoch innerhalb weniger Minuten bis Stunden in Kraft.", "backButton": "Zurück", "showDnsButton": "DNS-Einstellungen Anzeigen", "hideDnsButton": "DNS-Einstellungen Verbergen", "removeButton": "Entfernen", "dnsSettingsTitle": "DNS-Einstellungen der Domain", "removeDomainConfirmTitle": "Domainname Entfernen", "removeConfirmationText": "Möchten Sie den Domainnamen wirklich entfernen? ", "importantCleanupTitle": "Wichtige DNS-Bereinigung", "cleanupDescription": "<PERSON><PERSON>, nach dem Entfernen dieser Domain auch die während der Einrichtung erstellten DNS-Einträge zu löschen, um eine saubere DNS-Konfiguration aufrechtzuerhalten und potenzielle Konflikte zu vermeiden.", "confirmRemoveButton": "Domainname Entfernen", "customConfigTitle": "Benutzerdefinierte Domainkonfiguration", "customConfigDescription": "Verbinden Sie Ihre eigenen Domainnamen mit diesem Projekt. Ihr Projekt bleibt über die Standarddomain von Biela <PERSON>, aber benutzerdefinierte Domains bieten ein professionelles Marken-Erlebnis.", "defaultLabel": "Standard", "statusActive": "Aktiv", "statusPending": "<PERSON><PERSON><PERSON><PERSON>", "lastVerifiedText": "Zuletzt gerade eben verifiziert", "errorInvalidDomain": "Bitte geben Si<PERSON> einen gültigen Domainnamen ein (z.B. example.com)", "errorDuplicateDomain": "Dieser Domainname ist bereits mit Ihrem Projekt verbunden", "errorAddFail": "Domainname konnte nicht hinzugefügt werden.", "successAdd": "Domainname erfolgreich hinzugefügt! Ihre Domain wurde diesem Projekt hinzugefügt.", "benefitsTitle": "Domainvorteile", "benefitSecurityTitle": "Erhöhte Sicherheit", "benefitSecurityDesc": "Alle benutzerdefinierten Domains sind automatisch mit SSL-Zertifikaten gesichert.", "benefitPerformanceTitle": "Hohe Leistung", "benefitPerformanceDesc": "Ein globales CDN sorgt da<PERSON>ür, dass Ihr Projekt weltweit schnell lädt.", "benefitBrandingTitle": "Professionelles Branding", "benefitBrandingDesc": "Verwenden Sie Ihre eigene Domain für ein konsistentes Markenerlebnis.", "benefitAnalyticsTitle": "Analyse-Integration", "benefitAnalyticsDesc": "Benutzerdefinierte Domains arbeiten nahtlos mit Analyseplattformen zusammen.", "meta": {"index": {"title": "biela.dev | AI-gestützter Web- & App-Builder – <PERSON><PERSON><PERSON><PERSON> mit Prompts", "description": "Transformieren Sie Ihre Ideen in lebende Websites oder Apps mit biela.dev. Verwenden Sie AI-gestützte Prompts, um benutzerdefinierte digitale Produkte problemlos zu erstellen."}, "login": {"title": "Anmelden bei Ihrem biela.dev-Konto", "description": "Greifen Sie auf Ihr biela.dev-Dashboard zu, um Ihre AI-erstellten Projekte zu verwalten und zu erstellen."}, "register": {"title": "Bei biela.dev registrieren – Erstellen Sie mit AI beginnen", "description": "<PERSON>rstellen Sie Ihr biela.dev-Konto, um mit AI-gestützten Prompts Websites und Apps zu erstellen."}, "dashboard": {"title": "Ihr Projektdashboard – biela.dev", "description": "Verwalten Sie Ihre AI-erstellten Websites und Apps, bearbeiten Sie Live-Projekte und verfolgen Sie Ihr Build-Historie – alles in einem Ort."}, "profile": {"title": "<PERSON><PERSON> biela.dev", "description": "Zeige und aktualisiere deine Kontoinformationen auf biela.dev, verwalte deine Einstellungen und personalisiere dein KI-Erlebnis."}, "billing": {"title": "Abrechnung – Verwalten Sie Ihren Plan sicher auf biela.dev", "description": "Greifen Sie auf Ihre Abrechnungseinstellungen zu, um Ihr Abonnement zu verwalten, Zahlungsmethoden zu aktualisieren und Ihre biela.dev-Pläne im Griff zu behalten."}}, "transferProject": "<PERSON><PERSON> teilen", "transferSecurityNoteDescription": "Der Empfänger erhält vollständigen Zugriff auf eine Kopie dieses Projekts und alle damit verbundenen Ressourcen.", "transferProjectDescription": "<PERSON>eben Sie den Benutzernamen oder die E-Mail-Adresse der Person ein, an die Si<PERSON> eine Kopie dieses Projekts übertragen möchten.", "transferProjectLabel": "Benutzername oder E-Mail", "transferProjectPlaceholder": "<NAME_EMAIL>", "transferButton": "Übertragen", "transferSecurityNote": "Sicherheitshinweis:", "dontHavePermisionToTransfer": "Sie haben keine Berechtigung, dieses Projekt zu übertragen", "transferProjectUserNotFound": "<PERSON><PERSON><PERSON> {{ user }} wurde nicht gefunden!", "transferErrorOwnAccount": "Sie können ein Projekt nicht auf Ihr eigenes Konto übertragen.", "transferError": "Fehler beim Übertragen des Projekts", "transferSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> an {{ user }} übertragen", "enterValidEmailUsername": "<PERSON>te geben Si<PERSON> einen Benutzernamen oder eine E-Mail-Adresse ein", "enterMinValidEmailUsername": "Bitte geben Si<PERSON> einen gültigen Benutzernamen (mindestens 3 Zeichen) oder eine E-Mail-Adresse ein", "youWillStillHaveAccess": "Du hast weiterhin Zugriff auf das ursprüngliche Projekt", "newChangesWillNotAffect": "Neue Änderungen wirken sich nicht auf das Projekt des anderen Nutzers aus", "ProjectInformationNotLoaded": "Projektinformationen konnten nicht geladen werden.", "projectInfoTitle": "Projektinfo", "generalInformationProject": "Allgemeine Informationen über dein Projekt.", "rename": "Umbenennen", "lastlySavedAt": "Zuletzt gespeichert am:", "noSavedAppVersion": "<PERSON>s gibt keine gespeicherte App-Version.", "Owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TechStack": "Technologie-Stack", "FeatureCount": "Anzahl der Funktionen", "UniqueComponentCount": "Anzahl eindeutiger Komponenten", "inviteCollaborator": {"title": "MITARBEITER EINLADEN", "message": "Du bist dabei, {{email}} zur Zusammenarbeit an diesem Projekt einzuladen.", "action": "Einladung senden"}, "removeCollaborator": {"title": "MITARBEITER ENTFERNEN", "message": "Du bist dabei, {{email}} aus diesem Projekt zu entfernen.", "action": "Entfernen"}, "database": {"title": "Datenbankverbindungen", "fields": {"created": "<PERSON><PERSON><PERSON><PERSON>", "size": "Größe", "region": "Region", "version": "Version"}, "button": {"connected": "Verbunden", "connect": "Verbinden", "notConnected": "Nicht verbunden"}}, "status": {"Online": "Online", "Degraded": "Beeinträchtigt", "Restoring": "Wird wiederhergestellt", "Restored": "Wiederhergestellt", "Creating": "<PERSON>ird erste<PERSON>t", "Provisioning": "Wird bereitgestellt", "Coming Up": "Startet", "Deleting": "<PERSON><PERSON><PERSON>", "Deleted": "Gelöscht", "Pausing": "<PERSON>ird paus<PERSON>t", "Paused": "<PERSON><PERSON><PERSON><PERSON>", "Inactive": "Inaktiv", "Suspended": "Ausgesetzt", "Resuming": "Wird fortgesetzt", "Updating": "<PERSON><PERSON><PERSON> a<PERSON><PERSON>", "Migrating": "<PERSON>ir<PERSON> mig<PERSON>t", "Maintenance": "Wartung", "Restarting": "Wird neu gestartet", "Backup in progress": "<PERSON><PERSON> l<PERSON>t", "Restore in progress": "Wiederherstellung läuft", "Failed": "Fehlgeschlagen", "Unknown": "Unbekannt", "Loading...": "Wird geladen...", "Not Found": "Nicht gefunden", "Error": "<PERSON><PERSON>"}, "titleChat": "{{typeCapitalized}}-Chat", "titleMessage": "{{typeCapitalized}}-<PERSON><PERSON><PERSON><PERSON>", "dialogDescriptionText": "<PERSON><PERSON> sind dabei, {{description}} zu {{type}}.", "confirmText": "<PERSON>d <PERSON> sic<PERSON>, dass Si<PERSON> diesen Chat {{type}} möchten?", "deleteButton": "Löschen", "deleteInfinitive": "löschen", "deleteNoun": "Löschung", "duplicateButton": "Duplizieren", "duplicateInfinitive": "dupli<PERSON><PERSON>", "duplicateNoun": "Duplizierung", "downloadButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "downloadInfinitive": "herunt<PERSON><PERSON>n", "downloadNoun": "Download", "exportButton": "Exportieren", "exportInfinitive": "exportieren", "exportNoun": "Export", "forkButton": "Abzweigen", "forkInfinitive": "abzweigen", "forkNoun": "Abzweigung", "rollbackButton": "Z<PERSON>ücksetzen", "rollbackInfinitive": "zurücksetzen", "rollbackNoun": "Rollback", "saveButton": "Speichern", "saveInfinitive": "speichern", "saveNoun": "Speicherung", "restoreButton": "Wiederherstellen", "restoreInfinitive": "wied<PERSON><PERSON><PERSON><PERSON>", "restoreNoun": "Wiederherstellung", "loadingOverlayChat": "<PERSON><PERSON> warten, während wir den Chat {{type}}.", "loadingOverlayMessages": "<PERSON>te warten, während wir die Nachrichten {{type}}.", "exportProjectChat": "Projekt-Chat exportieren", "downloadProjectFiles": "Projektdateien herunterladen", "pleaseSelectCommit": "Bitte wählen Sie einen Commit zum Wiederherstellen:", "refresh": "Aktualisieren", "savingApp": "Speichern...", "confirmationDelete": "Löschen", "confirmationDuplicate": "Duplizieren", "confirmationDownload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmationExport": "Exportieren", "confirmationFork": "Abzweigen", "confirmationRollback": "Z<PERSON>ücksetzen", "confirmationSave": "Speichern", "confirmationRestore": "Wiederherstellen", "dialogTitleChat": "{{typeCapitalized}} diesen <PERSON><PERSON>?", "dialogTitleData": "{{typeCapitalized}} Ihre Daten?", "dialogLoadingChat": "{{typeCapitalized}} des Chats...", "dialogLoadingData": "{{typeCapitalized}} der Nachrichten...", "dialogDescription": "<PERSON>e sind dabei zu {{type}}: {{description}}", "dialogConfirm": "<PERSON>d <PERSON> sic<PERSON>, dass Si<PERSON> {{type}} möchten?", "buttonCancel": "Abbrechen", "previousAppVersion": "eine vorherige App-Version", "CreatingDatabase": "Datenbank wird erstellt...", "DatabaseDashboard": "Datenbank-Dashboard", "ConnectDatabase": "Datenbank verbinden", "DatabaseConnectionOptions": "Optionen zur Datenbankverbindung", "LoadingYourSupabaseProjects": "Lade deine Supabase-Projekte...", "NoSupabaseProjectsFound": "<PERSON>ine Supabase-Projekte gefunden. Verbinde dich zuerst mit Supabase.", "DatabaseOptions": "Datenbank-optionen", "DisconnectDatabase": "Datenbank trennen", "OpenSupabaseDashboard": "Supabase-Dashboard öffnen", "LinkExistingProject": "Bestehendes Projekt verknüpfen", "IncludeDataWhenDuplicating": "Daten beim Duplizieren einbeziehen", "DuplicateProject": "Projekt duplizieren", "CreateNewSupabaseDB": "Neue Supabase-DB erstellen", "DisconnectSupabaseDB": "Supabase-DB trennen", "UseExistingSupabaseDB": "Bestehende Supabase-DB verwenden", "SharedFrom": "<PERSON><PERSON><PERSON> von", "TokensExhausted": "Du hast keine Tokens mehr", "TokensExhaustedDescription": "Um mit deinem Projekt fortzufahren, wähle eine Option:", "upgradePlan": "Meinen Plan upgraden", "buyMoreTokens": "Mehr Tokens kaufen", "seeScreenshot": "Screenshot anzeigen"}