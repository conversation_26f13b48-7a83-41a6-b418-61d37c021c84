{"Claude4Description": "Most powerful AI for premium websites", "Claude4FeatureFirst": "High-quality code", "Claude4FeatureSecond": "Advanced functionality", "Claude4FeatureThird": "Superior design", "Claude4Performance": "Excellent", "Claude4Cost": "Premium", "Claude4ContextWindow": "Standard context window (200K tokens)", "GeminiProDescription": "Versatile AI with wide context", "GeminiProFeatureFirst": "Reliable code output", "GeminiProFeatureSecond": "Handles complex inputs", "GeminiProFeatureThird": "Good design quality", "GeminiProPerformance": "Very good", "GeminiProCost": "Mid-premium", "GeminiProContextWindow": "Large context window (1M+ tokens)", "GeminiFlashDescription": "Faster, cost-effective option", "GeminiFlashFeatureFirst": "Quick generation", "GeminiFlashFeatureSecond": "Basic functionality", "GeminiFlashFeatureThird": "Simple designs", "GeminiFlashPerformance": "Good", "GeminiFlashCost": "Budget-friendly", "GeminiFlashContextWindow": "Large context window (1M+ tokens)"}