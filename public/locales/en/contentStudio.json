{"title": "Content Studio", "searchPlaceholder": "Search images...", "loading": "Loading...", "cannotDeleteNonEmptyFolder": "Cannot delete non-empty folder. Please move or delete its contents first.", "confirmDeleteTitle": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete the selected images? This action cannot be undone.", "confirmDeleteSingleImageTitle": "Confirm Delete", "confirmDeleteSingleImageMessage": "Are you sure you want to delete this image? This action cannot be undone.", "confirmDeleteFolderTitle": "Delete Folder", "confirmDeleteFolderMessage": "Are you sure you want to delete the folder \"{{folderName}}\"? This action cannot be undone.", "delete": "Delete", "deleteFolder": "Delete Folder", "useInProject": "Use in Project", "uploadImages": "Upload Images", "moveToFolder": "Move to Folder", "addTags": "Add Tags", "clearSelection": "Clear Selection", "sort.newest": "Newest", "sort.oldest": "Oldest", "sort.name-asc": "Name (A-Z)", "sort.name-desc": "Name (Z-A)", "sort.size-asc": "Size (Smallest)", "sort.size-desc": "<PERSON><PERSON> (Largest)", "view.grid": "Grid View", "view.list": "List View", "filters": "Filters", "applyFilters": "Apply Filters", "noImages": "No images found.", "noFolders": "No folders available.", "createFolder": "Create Folder", "renameFolder": "<PERSON><PERSON>", "confirm": "Confirm", "cancel": "Cancel", "close": "Close", "folderSearchPlaceholder": "Search folders...", "foldersLabel": "Folders", "searchResults": "Search Results", "noFoldersFound": "No folders found", "allFiles": "All Files", "myFolders": "My Folders", "createSubfolder": "Create Subfolder", "folderNames": {"all": "All Files", "recent": "Recent", "favorites": "Favorites"}, "sort": {"label": "Sort", "newest": "Newest First", "oldest": "Oldest First", "nameAsc": "Name (A-Z)", "nameDesc": "Name (Z-A)", "sizeAsc": "Size (Smallest)", "sizeDesc": "<PERSON><PERSON> (Largest)"}, "noImagesFound": "No images found", "noImagesHelp": "Upload some images or select a different folder", "viewDetails": "View Details", "view": "View", "tableHeader": {"name": "Name", "size": "Size", "type": "Type", "date": "Date"}, "imageDetails": {"title": "Image Details", "videoTitle": "Video Details"}, "videoUrl": "Video URL", "edit": "Edit", "save": "Save", "tags": "Tags", "add": "Add", "addTagPlaceholder": "Add a tag...", "uploaded": "Uploaded:", "fileInfo": "File Information", "type": "Type", "size": "Size", "dimensions": "Dimensions", "location": "Location", "imageUrl": "Image URL", "urlCopied": "URL copied!", "noTags": "No tags", "move": "Move", "deleteImageTitle": "Delete Image", "deleteImageMessage": "Are you sure you want to delete this image? This action cannot be undone.", "deleteImageConfirm": "Delete Image", "moveDialog": {"title": "Move Items", "selectDestination": "Select destination folder:", "rootOption": "Root (All Files)", "noFolders": "No other folders available", "moveButton": "Move Items"}, "editConfirm": {"title": "Save Changes", "message": "Are you sure you want to save the changes to this item? This will update the metadata in your library.", "confirm": "Save Changes"}, "clearFilters": "Clear Filters", "dateRange": "Date Range", "dateFrom": "From", "dateTo": "To", "searchTagsPlaceholder": "Search tags...", "noTagsMatchSearch": "No tags match your search", "noTagsAvailable": "No tags available", "selected": "selected", "uploadFiles": "Upload Files", "uploading": "Uploading...", "dropFilesHere": "Drop files here", "dragDropFilesHere": "Drag & Drop files here", "releaseToUpload": "Release to upload your files", "dragDropOrBrowse": "Drag and drop image files here, or click the button below to browse", "browseFiles": "Browse Files", "fileTitle": "File Title", "tagsForFile": "Tags for", "pressEnterToAddTag": "Press Enter to add each tag", "folderLocation": "Folder Location", "rootAllFiles": "Root (All Files)", "noFilesSelected": "No files selected", "selectFilesHint": "Select files by dragging them to the upload area or using the browse button", "supportedFormats": "Supported formats: JPG, PNG, GIF, WebP", "maxFileSize": "Maximum file size: 5MB", "filesToUpload": "Files to upload", "addMore": "Add More", "selectFilesToUpload": "Select files to upload", "upload": "Upload", "errorMaxFiles": "You can only select up to {{count}} files.", "errorFileSizeExceeded": "\"{{fileName}}\" exceeds the maximum file size accepted. Skipping...", "errorNoValidFiles": "No valid files to upload.", "errorMissingFileTitle": "All files must have a name.", "errorFileTypeNotSupported": "{{fileName}}'s file type is not supported. Supported formats: JPG, PNG, GIF, WebP.", "uploadError": "There was an error while uploading.", "placeholderAddTag": "Add a tag and press Enter", "enterTagsForSingle": "Enter Tags for 1 item", "enterTagsForMultiple": "Enter Tags for multiple items", "addAnotherTagPlaceholder": "Add another tag...", "commonlyUsedTags": "Commonly used tags", "addTagsButton": "Add Tags", "suggestedTags": {"app": "App", "code": "Code", "coding": "Coding", "coffee": "Coffee", "css": "CSS", "design": "Design", "development": "Development", "html": "HTML", "javascript": "JavaScript", "laptop": "Laptop", "mobile": "Mobile", "programming": "Programming", "react": "React", "screen": "Screen", "web": "Web", "webdev": "Web Dev", "workspace": "Workspace"}, "createNewFolder": "Create New Folder", "folderName": "Folder Name", "placeholderFolderName": "Enter folder name", "parentFolder": "<PERSON><PERSON>er", "errorFolderNameRequired": "Folder name is required", "errorFolderAlreadyExists": "A folder with this name already exists", "newFolderName": "New Folder Name", "placeholderNewFolderName": "Enter new folder name", "errorFileMustHaveName": "The file must have a name.", "chooseAnImage": "Choose an Image", "uploadSomeImages": " Upload some images or select a different folder"}