{"userInformation": "User Information", "firstName": "First Name", "lastName": "Last Name", "username": "User Name", "phone": "Phone", "preferredLanguage": "Preferred Language", "billingInformation": "Billing Information", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2", "city": "City", "zipCode": "Zip Code", "referralCode": "Referral Code", "referralLink": "Referral Link", "referral": "Referral", "location": "Location", "site": "Site", "email": "Email", "contactInfo": "Contact Info", "websiteUrl": "Website URL", "accountDeletion": "Account Deletion", "deleteAccount": "Delete Account", "deleteAccountWarning": "In case of deletion you'll remove all your projects and personal data. Also you'll lose control of projects in your organizations.", "connectAccount": "Connect your {{platform}} account", "connectNow": "Connect Now", "connected": "Connected", "personalWebsite": "Personal Website", "facebook": "Facebook", "twitterX": "Twitter/X", "linkedin": "LinkedIn", "youtube": "YouTube", "instagram": "Instagram", "tiktok": "TikTok", "Country": "Country", "ChangePassword": "Change Password", "Updating": "Updating...", "Save": "Save", "Edit": "Edit", "CurrentPassword": "Current Password", "NewPassword": "New Password", "RepeatNewPassword": "Repeat New Password", "AllPasswordFieldsAreRequired": "All password fields are required", "NewPasswordsDoNotMatch": "New passwords do not match", "PasswordMustBeAtLeastCharactersLong": "Password must be at least {{minLength}} characters long", "UserNotFound": "User not found", "CurrentPasswordIsIncorrect": "Current password is incorrect", "PasswordUpdated": "Your password has been updated successfully!", "ErrorWhileUpdatingPassword": "Error while updating password", "Success": "Success", "Error": "Error", "updateAccount": "Your account has been updated successfully!", "failedUpdateAccount": "Something went wrong. Please try again.", "Close": "Close", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "usernameRequired": "Username is required", "phoneRequired": "Phone number is required", "address1Required": "Address is required", "cityRequired": "City is required", "zipCodeRequired": "Zip code is required", "countryRequired": "Country is required", "emailRequired": "Email is required", "EmailInvalid": "Please enter a valid email address", "URLInvalid": "Please enter a valid URL", "copyReferralCode": "Copy Referral Code", "copyReferralLink": "Copy Referral Link", "purchaseDetails": "Purchase Details", "dateLabel": "Date", "planLabel": "Plan", "amountLabel": "Amount", "tokensLabel": "Tokens", "whatsNext": "What's Next", "yourTokensAvailable": "Your tokens are now available in your account", "purchaseDetailsStored": "Purchase details are stored in your account", "viewPurchaseHistory": "You can view your purchase history in the billing section", "thankYou": "Thank You!", "purchaseSuccessful": "Your purchase was successful", "startCoding": "Start Coding", "tokenTopUpComingSoon": "Token Top-Up Coming Soon", "finalisingPricing": "We're finalising our token pricing system to provide you with the best rates and volume discounts.", "getReady": "Get ready for flexible token packages that will power your development needs.", "gotIt": "Got It", "checkBackSoon": "Check back soon for token purchases", "buyExtraTokens": "Buy Extra Tokens", "paymentMethod": "Payment Method", "howManyTokens": "How many tokens would you like?", "placeholderEnterTokens": "Enter amount of tokens...", "units": "Units", "availableDiscounts": "Available Discounts", "priceLabel": "Price:", "discountAppliedLabel": "Discount Applied:", "at": "At", "perMillion": "per million tokens", "purchaseTokens": "Purchase Tokens", "minimumPurchaseError": "Minimum purchase is {minTokens} tokens ($1)", "minimumTokenError": "Minimum token purchase is {{minTokens}}", "minimumBulkPurchaseError": "Minimum token purchase is {{amount}}", "maximumTokenError": "Maximum token purchase is {maxTokens}", "maximumBulkPurchaseError": "Maximum token purchase is {{amount}}", "modalTitle": "Choose a Plan or Reload Tokens as Needed", "modalSubtitle": "Pick from our three flexible plans or purchase extra tokens anytime to keep coding without limits.", "highlightExtraTokens": "extra tokens", "toggleMonthly": "Monthly", "toggleYearly": "Yearly (save 10%)", "buttonNeedMoreTokens": "Need more tokens?", "recentInvoicesTitle": "Recent Invoices", "recentInvoicesDescription": "Track your billing history and download invoices", "tableHeaderDate": "Date", "tableHeaderDescription": "Description", "tableHeaderAmount": "Amount", "tableHeaderStatus": "Status", "tableHeaderActions": "Actions", "noInvoicesFound": "No invoices found", "actionPreviewInvoice": "Preview Invoice", "actionDownloadInvoice": "Download Invoice", "nextPage": "Next Page", "previousPage": "Previous Page", "paginationStatus": "Showing {{from}} to {{to}} of {{total}} results", "planDescription": "Perfect for individuals exploring development, learning new skills, or working on personal projects.", "planNameBasicPlus": "Basic Plus", "planNameStarterPlus": "Starter Plus", "planNamePremiumPlus": "Premium Plus", "planPriceBasicPlus": "$100/year", "planPriceStarterPlus": "$200/year", "planPricePremiumPlus": "$300/year", "planTokensBasicPlus": "5M tokens per month", "planTokensStarterPlus": "25M tokens per month", "planTokensPremiumPlus": "80M tokens per month", "priceSeparator": "/", "currentPlanLabel": "Current Plan", "upgradePlanLabel": "Upgrade Plan", "million": "Million", "millions": "Millions"}