{"title": "Estudio de Contenido", "searchPlaceholder": "Buscar imágenes...", "loading": "Cargando...", "cannotDeleteNonEmptyFolder": "No se puede eliminar una carpeta que no está vacía. Por favor, mueva o elimine su contenido primero.", "confirmDeleteTitle": "Confirmar eliminación", "confirmDeleteMessage": "¿Está seguro de que desea eliminar las imágenes seleccionadas? Esta acción no se puede deshacer.", "confirmDeleteSingleImageTitle": "Confirmar eliminación", "confirmDeleteSingleImageMessage": "¿Está seguro de que desea eliminar esta imagen? Esta acción no se puede deshacer.", "confirmDeleteFolderTitle": "Eliminar carpeta", "confirmDeleteFolderMessage": "¿Está seguro de que desea eliminar la carpeta \"{{folderName}}\"? Esta acción no se puede deshacer.", "delete": "Eliminar", "deleteFolder": "Eliminar carpeta", "useInProject": "Usar en el proyecto", "uploadImages": "Subir imágenes", "moveToFolder": "Mover a carpeta", "addTags": "Agregar etiquetas", "clearSelection": "<PERSON><PERSON><PERSON>", "sort.newest": "Más recientes", "sort.oldest": "Más antiguas", "sort.name-asc": "Nombre (A-Z)", "sort.name-desc": "Nombre (Z-A)", "sort.size-asc": "<PERSON><PERSON><PERSON> (más pequeño)", "sort.size-desc": "<PERSON><PERSON><PERSON> (más grande)", "view.grid": "Vista de cuadrícula", "view.list": "Vista de lista", "filters": "<PERSON><PERSON><PERSON>", "applyFilters": "Aplicar filtros", "noImages": "No se encontraron imágenes.", "noFolders": "No hay carpetas disponibles.", "createFolder": "<PERSON><PERSON><PERSON> carpeta", "renameFolder": "Renombrar carpeta", "confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "folderSearchPlaceholder": "Buscar carpetas...", "foldersLabel": "Carpetas", "searchResults": "Resultados de búsqueda", "noFoldersFound": "No se encontraron carpetas", "allFiles": "Todos los archivos", "myFolders": "<PERSON><PERSON> <PERSON>as", "createSubfolder": "Crear subcarpeta", "folderNames": {"all": "Todos los archivos", "recent": "Recientes", "favorites": "<PERSON><PERSON><PERSON><PERSON>"}, "sort": {"label": "Ordenar", "newest": "Más recientes primero", "oldest": "Más antiguas primero", "nameAsc": "Nombre (A-Z)", "nameDesc": "Nombre (Z-A)", "sizeAsc": "<PERSON><PERSON><PERSON> (más pequeño)", "sizeDesc": "<PERSON><PERSON><PERSON> (más grande)"}, "noImagesFound": "No se encontraron imágenes", "noImagesHelp": "Suba algunas imágenes o seleccione una carpeta diferente", "viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "view": "<PERSON>er", "tableHeader": {"name": "Nombre", "size": "<PERSON><PERSON><PERSON>", "type": "Tipo", "date": "<PERSON><PERSON>"}, "imageDetails": {"title": "Detalles de la imagen", "videoTitle": "Detalles del Video"}, "videoUrl": "URL del Video", "edit": "<PERSON><PERSON>", "save": "Guardar", "tags": "Etiquetas", "add": "Agregar", "addTagPlaceholder": "Agregar etiqueta...", "uploaded": "Subido:", "fileInfo": "Información del archivo", "type": "Tipo", "size": "<PERSON><PERSON><PERSON>", "dimensions": "Dimensiones", "location": "Ubicación", "imageUrl": "URL de la imagen", "urlCopied": "¡URL copiada!", "noTags": "Sin etiquetas", "move": "Mover", "deleteImageTitle": "Eliminar imagen", "deleteImageMessage": "¿Está seguro de que desea eliminar esta imagen? Esta acción no se puede deshacer.", "deleteImageConfirm": "Eliminar imagen", "moveDialog": {"title": "Mover elementos", "selectDestination": "<PERSON><PERSON><PERSON><PERSON><PERSON> de des<PERSON>:", "rootOption": "Raíz (Todos los archivos)", "noFolders": "No hay otras carpetas disponibles", "moveButton": "Mover elementos"}, "editConfirm": {"title": "Guardar cambios", "message": "¿Está seguro de que desea guardar los cambios en este elemento? Esto actualizará los metadatos en su biblioteca.", "confirm": "Guardar cambios"}, "clearFilters": "<PERSON><PERSON><PERSON> filtros", "dateRange": "<PERSON><PERSON>", "dateFrom": "<PERSON><PERSON>", "dateTo": "<PERSON><PERSON>", "searchTagsPlaceholder": "Buscar etiquetas...", "noTagsMatchSearch": "No hay etiquetas que coincidan con su búsqueda", "noTagsAvailable": "No hay etiquetas disponibles", "selected": "sele<PERSON><PERSON><PERSON>", "uploadFiles": "Subir archivos", "uploading": "Subiendo...", "dropFilesHere": "Suelte los archivos aquí", "dragDropFilesHere": "Arrastre y suelte los archivos aquí", "releaseToUpload": "Suelte para cargar sus archivos", "dragDropOrBrowse": "Arrastre y suelte archivos de imagen aquí, o haga clic en el botón de abajo para buscarlos", "browseFiles": "Buscar archivos", "fileTitle": "Título del archivo", "tagsForFile": "Etiquetas para", "pressEnterToAddTag": "Presione Enter para agregar cada etiqueta", "folderLocation": "Ubicación de la carpeta", "rootAllFiles": "Raíz (Todos los archivos)", "noFilesSelected": "No se han seleccionado archivos", "selectFilesHint": "Seleccione archivos arrastrándolos al área de carga o usando el botón de búsqueda", "supportedFormats": "Formatos soportados: JPG, PNG, GIF, WebP", "maxFileSize": "Tamaño máximo de archivo: 5MB", "filesToUpload": "Archivos a cargar", "addMore": "Agregar más", "selectFilesToUpload": "Seleccionar archivos para cargar", "upload": "<PERSON><PERSON>", "errorMaxFiles": "Solo puede seleccionar hasta {{count}} archivos.", "errorFileSizeExceeded": "\"{{fileName}}\" excede el tamaño máximo de archivo permitido. Saltando...", "errorNoValidFiles": "No hay archivos válidos para cargar.", "errorMissingFileTitle": "Todos los archivos deben tener un nombre.", "errorFileTypeNotSupported": "El tipo de archivo de {{fileName}} no es compatible. Formatos compatibles: JPG, PNG, GIF, WebP.", "uploadError": "Hubo un error al subir el archivo.", "placeholderAddTag": "Agregue una etiqueta y presione Enter", "enterTagsForSingle": "Introducir etiquetas para 1 elemento", "enterTagsForMultiple": "Introducir etiquetas para varios elementos", "addAnotherTagPlaceholder": "Agregar otra etiqueta...", "commonlyUsedTags": "Etiquetas más comunes", "addTagsButton": "Agregar etiquetas", "suggestedTags": {"app": "Aplicación", "code": "Código", "coding": "Programación", "coffee": "Café", "css": "CSS", "design": "Diseño", "development": "Desarrollo", "html": "HTML", "javascript": "JavaScript", "laptop": "<PERSON><PERSON><PERSON>", "mobile": "Móvil", "programming": "Programación", "react": "React", "screen": "<PERSON><PERSON><PERSON>", "web": "Web", "webdev": "Desarrollo Web", "workspace": "Espacio de trabajo"}, "createNewFolder": "<PERSON><PERSON><PERSON> nueva carpeta", "folderName": "Nombre de la carpeta", "placeholderFolderName": "Introduzca el nombre de la carpeta", "parentFolder": "Carpeta principal", "errorFolderNameRequired": "Se requiere un nombre de carpeta", "errorFolderAlreadyExists": "Ya existe una carpeta con este nombre", "newFolderName": "Nuevo nombre de carpeta", "placeholderNewFolderName": "Introduzca el nuevo nombre de la carpeta", "errorFileMustHaveName": "El archivo debe tener un nombre.", "chooseAnImage": "Elige una imagen", "uploadSomeImages": "Sube algunas imágenes o selecciona una carpeta diferente"}