{"userInformation": "Información del Usuario", "firstName": "Nombre", "lastName": "Apellido", "username": "Nombre de Usuario", "phone": "Teléfono", "preferredLanguage": "Idioma Preferido", "billingInformation": "Información de Facturación", "addressLine1": "Dirección Línea 1", "addressLine2": "Dirección Línea 2", "city": "Ciudad", "zipCode": "Código Postal", "referralCode": "Código de Referencia", "referralLink": "Enlace de Referencia", "referral": "Referencia", "location": "Ubicación", "site": "Sitio", "email": "Correo Electrónico", "contactInfo": "Información de contacto", "websiteUrl": "URL del Sitio Web", "accountDeletion": "Eliminación de Cuenta", "deleteAccount": "Eliminar Cuenta", "deleteAccountWarning": "En caso de eliminación, se eliminarán todos tus proyectos y datos personales. También perderás el control de los proyectos en tus organizaciones.", "connectAccount": "Conecta tu cuenta de {{platform}}", "connectNow": "Conectar Ahora", "connected": "Conectado", "personalWebsite": "Sitio web personal", "facebook": "Facebook", "twitterX": "Twitter/X", "linkedin": "LinkedIn", "youtube": "YouTube", "instagram": "Instagram", "tiktok": "TikTok", "Country": "<PERSON><PERSON>", "ChangePassword": "Cambiar contraseña", "Updating": "Actualizando...", "Save": "Guardar", "Edit": "<PERSON><PERSON>", "CurrentPassword": "Contraseña actual", "NewPassword": "Nueva contraseña", "RepeatNewPassword": "Repetir nueva contraseña", "AllPasswordFieldsAreRequired": "Todos los campos de contraseña son obligatorios", "NewPasswordsDoNotMatch": "Las nuevas contraseñas no coinciden", "PasswordMustBeAtLeastCharactersLong": "La contraseña debe tener al menos {{minLength}} caracteres", "UserNotFound": "Usuario no encontrado", "CurrentPasswordIsIncorrect": "La contraseña actual es incorrecta", "PasswordUpdated": "¡Tu contraseña ha sido actualizada con éxito!", "ErrorWhileUpdatingPassword": "Error al actualizar la contraseña", "Success": "Éxito", "Error": "Error", "updateAccount": "¡Tu cuenta ha sido actualizada con éxito!", "failedUpdateAccount": "Algo salió mal. <PERSON><PERSON> favor, inténtalo de nuevo.", "Close": "<PERSON><PERSON><PERSON>", "firstNameRequired": "El nombre es obligatorio", "lastNameRequired": "El apellido es obligatorio", "usernameRequired": "El nombre de usuario es obligatorio", "phoneRequired": "El número de teléfono es obligatorio", "address1Required": "La dirección es obligatoria", "cityRequired": "La ciudad es obligatoria", "zipCodeRequired": "El código postal es obligatorio", "countryRequired": "El país es obligatorio", "emailRequired": "El correo electrónico es obligatorio", "EmailInvalid": "Por favor, ingrese una dirección de correo electrónico válida", "URLInvalid": "Por favor, ingrese una URL válida", "copyReferralCode": "Copiar c<PERSON>digo de <PERSON>", "copyReferralLink": "<PERSON><PERSON><PERSON> en<PERSON> de <PERSON>", "purchaseDetails": "Detalles de la compra", "dateLabel": "<PERSON><PERSON>", "planLabel": "Plan", "amountLabel": "Monto", "tokensLabel": "Tokens", "whatsNext": "¿Qué sigue?", "yourTokensAvailable": "Tus tokens ya están disponibles en tu cuenta", "purchaseDetailsStored": "Los detalles de la compra se han guardado en tu cuenta", "viewPurchaseHistory": "Puedes ver tu historial de compras en la sección de facturación", "thankYou": "¡<PERSON><PERSON><PERSON>!", "purchaseSuccessful": "Tu compra fue exitosa", "startCoding": "Comienza a programar", "tokenTopUpComingSoon": "Recarga de tokens próximamente", "finalisingPricing": "Estamos finalizando nuestro sistema de precios para ofrecerte las mejores tarifas y descuentos por volumen.", "getReady": "Prepárate para paquetes de tokens flexibles que impulsarán tus necesidades de desarrollo.", "gotIt": "Entendido", "checkBackSoon": "Vuelve pronto para comprar tokens", "buyExtraTokens": "Comprar tokens extra", "paymentMethod": "Método de pago", "howManyTokens": "¿Cuántos tokens deseas?", "placeholderEnterTokens": "Introduce la cantidad de tokens...", "units": "Unidades", "availableDiscounts": "Descuentos disponibles", "priceLabel": "Precio:", "discountAppliedLabel": "Descuento aplicado:", "at": "A", "perMillion": "por millón de tokens", "purchaseTokens": "Comprar tokens", "minimumPurchaseError": "La compra mínima es de {minTokens} tokens ($1)", "minimumTokenError": "La compra mínima de tokens es {{minTokens}}", "minimumBulkPurchaseError": "La compra mínima de tokens es {{amount}}", "maximumTokenError": "La compra máxima de tokens es de {maxTokens}", "maximumBulkPurchaseError": "La compra máxima de tokens es {{amount}}", "modalTitle": "Elige un plan o recarga tokens según sea necesario", "modalSubtitle": "Elige entre nuestros tres planes flexibles o compra tokens extra en cualquier momento para seguir codificando sin límites.", "highlightExtraTokens": "tokens extra", "toggleMonthly": "<PERSON><PERSON><PERSON>", "toggleYearly": "<PERSON><PERSON> (ahorra 10%)", "buttonNeedMoreTokens": "¿Necesitas más tokens?", "recentInvoicesTitle": "Facturas recientes", "recentInvoicesDescription": "Consulta tu historial de facturación y descarga facturas", "tableHeaderDate": "<PERSON><PERSON>", "tableHeaderDescription": "Descripción", "tableHeaderAmount": "Cantidad", "tableHeaderStatus": "Estado", "tableHeaderActions": "Acciones", "noInvoicesFound": "No se encontraron facturas", "actionPreviewInvoice": "Vista previa de la factura", "actionDownloadInvoice": "<PERSON><PERSON><PERSON> factura", "planDescription": "Perfecto para individuos que exploran el desarrollo, aprenden nuevas habilidades o trabajan en proyectos personales.", "planNameBasicPlus": "Basic Plus", "planNameStarterPlus": "Starter Plus", "planNamePremiumPlus": "Premium Plus", "planPriceBasicPlus": "$100/año", "planPriceStarterPlus": "$200/año", "planPricePremiumPlus": "$300/año", "planTokensBasicPlus": "5M tokens por mes", "planTokensStarterPlus": "25M tokens por mes", "planTokensPremiumPlus": "80M tokens por mes", "priceSeparator": "/", "currentPlanLabel": "Plan actual", "upgradePlanLabel": "Actualizar plan", "million": "Millón", "millions": "<PERSON><PERSON>", "nextPage": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "previousPage": "Página anterior", "paginationStatus": "Mostrando de {{from}} a {{to}} de {{total}} resultados"}