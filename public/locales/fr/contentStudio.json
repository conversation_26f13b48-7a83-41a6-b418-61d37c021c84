{"title": "Studio de contenu", "searchPlaceholder": "Rechercher des images...", "loading": "Chargement...", "cannotDeleteNonEmptyFolder": "Impossible de supprimer un dossier non vide. Veuillez d'abord d<PERSON>placer ou supprimer son contenu.", "confirmDeleteTitle": "Confirmer la <PERSON>", "confirmDeleteMessage": "Êtes-vous sûr de vouloir supprimer les images sélectionnées ? Cette action est irréversible.", "confirmDeleteSingleImageTitle": "Confirmer la <PERSON>", "confirmDeleteSingleImageMessage": "Êtes-vous sûr de vouloir supprimer cette image ? Cette action est irréversible.", "confirmDeleteFolderTitle": "Supp<PERSON>er le dossier", "confirmDeleteFolderMessage": "Êtes-vous sûr de vouloir supprimer le dossier \"{{folderName}}\" ? Cette action est irréversible.", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteFolder": "Supp<PERSON>er le dossier", "useInProject": "Utiliser dans le projet", "uploadImages": "Téléverser des images", "moveToFolder": "<PERSON><PERSON>placer vers un dossier", "addTags": "Ajouter des tags", "clearSelection": "Effacer la sélection", "sort.newest": "Les plus récents", "sort.oldest": "Les plus anciens", "sort.name-asc": "Nom (A-Z)", "sort.name-desc": "Nom (Z-A)", "sort.size-asc": "<PERSON><PERSON> (plus petite)", "sort.size-desc": "<PERSON><PERSON> (plus grande)", "view.grid": "Vue en grille", "view.list": "Vue en liste", "filters": "Filtres", "applyFilters": "Appliquer les filtres", "noImages": "Aucune image trouvée.", "noFolders": "Aucun dossier disponible.", "createFolder": "<PERSON><PERSON><PERSON> un dossier", "renameFolder": "Renommer le dossier", "confirm": "Confirmer", "cancel": "Annuler", "close": "<PERSON><PERSON><PERSON>", "folderSearchPlaceholder": "Rechercher des dossiers...", "foldersLabel": "Dossiers", "searchResults": "Résultats de recherche", "noFoldersFound": "<PERSON>cun dossier trouvé", "allFiles": "To<PERSON> les fichiers", "myFolders": "Mes dossiers", "createSubfolder": "<PERSON><PERSON><PERSON> un sous-dossier", "folderNames": {"all": "To<PERSON> les fichiers", "recent": "<PERSON><PERSON><PERSON><PERSON>", "favorites": "<PERSON><PERSON><PERSON>"}, "sort": {"label": "<PERSON><PERSON>", "newest": "Les plus récents d'abord", "oldest": "Les plus anciens d'abord", "nameAsc": "Nom (A-Z)", "nameDesc": "Nom (Z-A)", "sizeAsc": "<PERSON><PERSON> (plus petite)", "sizeDesc": "<PERSON><PERSON> (plus grande)"}, "noImagesFound": "Aucune image trouvée", "noImagesHelp": "Téléversez des images ou sélectionnez un autre dossier", "viewDetails": "Voir les détails", "view": "Voir", "tableHeader": {"name": "Nom", "size": "<PERSON><PERSON>", "type": "Type", "date": "Date"}, "imageDetails": {"title": "Détails de l'image", "videoTitle": "Détails de la Vidéo"}, "videoUrl": "URL de la Vidéo", "edit": "Modifier", "save": "Enregistrer", "tags": "Tags", "add": "Ajouter", "addTagPlaceholder": "Ajouter un tag...", "uploaded": "Téléversé :", "fileInfo": "Informations sur le fichier", "type": "Type", "size": "<PERSON><PERSON>", "dimensions": "Dimensions", "location": "Emplacement", "imageUrl": "URL de l'image", "urlCopied": "URL copiée !", "noTags": "Aucun tag", "move": "<PERSON><PERSON><PERSON><PERSON>", "deleteImageTitle": "Supprimer l'image", "deleteImageMessage": "Êtes-vous sûr de vouloir supprimer cette image ? Cette action est irréversible.", "deleteImageConfirm": "Supprimer l'image", "moveDialog": {"title": "Déplacer les éléments", "selectDestination": "Sélectionner le dossier de destination :", "rootOption": "<PERSON><PERSON> (Tous les fichiers)", "noFolders": "Aucun autre dossier disponible", "moveButton": "Déplacer les éléments"}, "editConfirm": {"title": "Enregistrer les modifications", "message": "Êtes-vous sûr de vouloir enregistrer les modifications de cet élément ? Ce<PERSON> mettra à jour les métadonnées dans votre bibliothèque.", "confirm": "Enregistrer les modifications"}, "clearFilters": "Effacer les filtres", "dateRange": "Plage de dates", "dateFrom": "De", "dateTo": "À", "searchTagsPlaceholder": "Rechercher des tags...", "noTagsMatchSearch": "Aucun tag ne correspond à votre recherche", "noTagsAvailable": "Aucun tag disponible", "selected": "sélectionné", "uploadFiles": "Téléverser des fichiers", "uploading": "Téléversement...", "dropFilesHere": "Déposez les fichiers ici", "dragDropFilesHere": "Glissez-d<PERSON><PERSON>z les fichiers ici", "releaseToUpload": "Relâchez pour téléverser vos fichiers", "dragDropOrBrowse": "G<PERSON><PERSON>z<PERSON>d<PERSON><PERSON><PERSON> des fichiers image ici, ou cliquez sur le bouton ci-dessous pour parcourir", "browseFiles": "Parcourir les fichiers", "fileTitle": "<PERSON><PERSON><PERSON> <PERSON>", "tagsForFile": "Tags pour", "pressEnterToAddTag": "Appuyez sur Entrée pour ajouter chaque tag", "folderLocation": "Emplacement du dossier", "rootAllFiles": "<PERSON><PERSON> (Tous les fichiers)", "noFilesSelected": "<PERSON><PERSON><PERSON> fichier s<PERSON>", "selectFilesHint": "Sélectionnez des fichiers en les faisant glisser dans la zone de téléversement ou en utilisant le bouton parcourir", "supportedFormats": "Formats pris en charge : JPG, PNG, GIF, WebP", "maxFileSize": "Taille maximale du fichier : 5 Mo", "filesToUpload": "Fichiers à téléverser", "addMore": "Ajouter plus", "selectFilesToUpload": "Sélectionner les fichiers à téléverser", "upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errorMaxFiles": "Vous pouvez sélectionner jusqu'à {{count}} fi<PERSON>ers seulement.", "errorFileSizeExceeded": "\"{{file<PERSON>ame}}\" dépasse la taille maximale autorisée. Ignoré...", "errorNoValidFiles": "Aucun fichier valide à téléverser.", "errorMissingFileTitle": "Tous les fichiers doivent avoir un nom.", "errorFileTypeNotSupported": "Le type de fichier de {{fileName}} n'est pas pris en charge. Formats pris en charge : JPG, PNG, GIF, WebP.", "uploadError": "Une erreur s'est produite lors du téléchargement.", "placeholderAddTag": "Ajoutez un tag et appuyez sur Entrée", "enterTagsForSingle": "Saisir les tags pour 1 élément", "enterTagsForMultiple": "Saisir les tags pour plusieurs éléments", "addAnotherTagPlaceholder": "Ajouter un autre tag...", "commonlyUsedTags": "Tags couramment utilisés", "addTagsButton": "Ajouter des tags", "suggestedTags": {"app": "Application", "code": "Code", "coding": "Programmation", "coffee": "Café", "css": "CSS", "design": "Design", "development": "Développement", "html": "HTML", "javascript": "JavaScript", "laptop": "Ordinateur portable", "mobile": "Mobile", "programming": "Programmation", "react": "React", "screen": "É<PERSON>ran", "web": "Web", "webdev": "Développement Web", "workspace": "Espace de travail"}, "createNewFolder": "<PERSON><PERSON>er un nouveau dossier", "folderName": "Nom du dossier", "placeholderFolderName": "<PERSON><PERSON> le nom du dossier", "parentFolder": "Dossier parent", "errorFolderNameRequired": "Le nom du dossier est requis", "errorFolderAlreadyExists": "Un dossier avec ce nom existe déjà", "newFolderName": "Nouveau nom de dossier", "placeholderNewFolderName": "Saisir le nouveau nom du dossier", "errorFileMustHaveName": "Le fichier doit avoir un nom.", "chooseAnImage": "Choisissez une image", "uploadSomeImages": "Téléversez des images ou sélectionnez un autre dossier"}