{"userInformation": "Informations de l'utilisateur", "firstName": "Prénom", "lastName": "Nom de famille", "username": "Nom d'utilisateur", "phone": "Téléphone", "preferredLanguage": "Langue préférée", "billingInformation": "Informations de facturation", "addressLine1": "Adresse Ligne 1", "addressLine2": "Adresse Ligne 2", "city": "Ville", "zipCode": "Code Postal", "referralCode": "Code de parrainage", "referralLink": "<PERSON><PERSON> de <PERSON>", "referral": "Parrainage", "location": "Emplacement", "site": "Site", "email": "E-mail", "contactInfo": "Informations de contact", "websiteUrl": "URL du site web", "accountDeletion": "Suppression du compte", "deleteAccount": "Supprimer le compte", "deleteAccountWarning": "En cas de suppression, vous supprimerez tous vos projets et données personnelles. Vous perdrez également le contrôle des projets dans vos organisations.", "connectAccount": "Connectez votre compte {{platform}}", "connectNow": "Connecter maintenant", "connected": "Connecté", "personalWebsite": "Site personnel", "facebook": "Facebook", "twitterX": "Twitter/X", "linkedin": "LinkedIn", "youtube": "YouTube", "instagram": "Instagram", "tiktok": "TikTok", "Country": "Pays", "ChangePassword": "Changer le mot de passe", "Updating": "Mise à jour...", "Save": "Enregistrer", "Edit": "Modifier", "CurrentPassword": "Mot de passe actuel", "NewPassword": "Nouveau mot de passe", "RepeatNewPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON> le nouveau mot de passe", "AllPasswordFieldsAreRequired": "Tous les champs de mot de passe sont obligatoires", "NewPasswordsDoNotMatch": "Les nouveaux mots de passe ne correspondent pas", "PasswordMustBeAtLeastCharactersLong": "Le mot de passe doit contenir au moins {{minLength}} caractères", "UserNotFound": "Utilisateur non trouvé", "CurrentPasswordIsIncorrect": "Le mot de passe actuel est incorrect", "PasswordUpdated": "Votre mot de passe a été mis à jour avec succès!", "ErrorWhileUpdatingPassword": "Erreur lors de la mise à jour du mot de passe", "Success": "Su<PERSON>ès", "Error": "<PERSON><PERSON><PERSON>", "updateAccount": "Votre compte a été mis à jour avec succès !", "failedUpdateAccount": "Une erreur s'est produite. Veuillez réessayer.", "Close": "<PERSON><PERSON><PERSON>", "firstNameRequired": "Le prénom est requis", "lastNameRequired": "Le nom est requis", "usernameRequired": "Le nom d'utilisateur est requis", "phoneRequired": "Le numéro de téléphone est requis", "address1Required": "L'adresse est requise", "cityRequired": "La ville est requise", "zipCodeRequired": "Le code postal est requis", "countryRequired": "Le pays est requis", "emailRequired": "L'e-mail est requis", "EmailInvalid": "Veuillez saisir une adresse e-mail valide", "URLInvalid": "Veuillez saisir une URL valide", "copyReferralCode": "Copier le code de parrainage", "copyReferralLink": "Copier le lien de parrainage", "purchaseDetails": "<PERSON>é<PERSON> de l'achat", "dateLabel": "Date", "planLabel": "Forfait", "amountLabel": "<PERSON><PERSON>", "tokensLabel": "Jet<PERSON>", "whatsNext": "Et ensuite ?", "yourTokensAvailable": "Vos jetons sont maintenant disponibles dans votre compte", "purchaseDetailsStored": "Les détails de l'achat sont enregistrés dans votre compte", "viewPurchaseHistory": "<PERSON><PERSON> pou<PERSON> consulter votre historique d'achats dans la section de facturation", "thankYou": "<PERSON><PERSON><PERSON> !", "purchaseSuccessful": "Votre achat a été effectué avec succès", "startCoding": "Commencez à coder", "tokenTopUpComingSoon": "Recharge de jetons bientôt disponible", "finalisingPricing": "Nous finalisons notre système de tarification pour vous offrir les meilleurs tarifs et remises sur volume.", "getReady": "Préparez-vous à des packs de jetons flexibles pour répondre à vos besoins de développement.", "gotIt": "<PERSON><PERSON><PERSON>", "checkBackSoon": "Revenez bientôt pour acheter des jetons", "buyExtraTokens": "Acheter des jetons supplémentaires", "paymentMethod": "Méthode de paiement", "howManyTokens": "Combien de jetons souhaitez-vous ?", "placeholderEnterTokens": "Entrez le nombre de jetons...", "units": "Unités", "availableDiscounts": "Remises disponibles", "priceLabel": "Prix :", "discountAppliedLabel": "Remise appliquée :", "at": "À", "perMillion": "par million de jetons", "purchaseTokens": "Acheter des jetons", "minimumPurchaseError": "Achat minimum de {minTokens} jetons (1 $)", "minimumTokenError": "L'achat minimum de jetons est de {{minTokens}}", "maximumTokenError": "Achat maximum de {maxTokens} jetons", "modalTitle": "Choisissez un plan ou rechargez des jetons selon vos besoins", "modalSubtitle": "Choisissez parmi nos trois plans flexibles ou achetez des jetons supplémentaires à tout moment pour coder sans limites.", "highlightExtraTokens": "jetons supplémentaires", "toggleMonthly": "<PERSON><PERSON><PERSON>", "toggleYearly": "<PERSON><PERSON> (économisez 10 %)", "buttonNeedMoreTokens": "Besoin de plus de jetons ?", "recentInvoicesTitle": "Factures récentes", "recentInvoicesDescription": "Su<PERSON>z votre historique de facturation et téléchargez les factures", "tableHeaderDate": "Date", "tableHeaderDescription": "Description", "tableHeaderAmount": "<PERSON><PERSON>", "tableHeaderStatus": "Statut", "tableHeaderActions": "Actions", "noInvoicesFound": "Aucune facture trouvée", "actionPreviewInvoice": "Aperçu de la facture", "actionDownloadInvoice": "Télécharger la facture", "planDescription": "Parfait pour les personnes explorant le développement, apprenant de nouvelles compétences ou travaillant sur des projets personnels.", "planNameBasicPlus": "Basic Plus", "planNameStarterPlus": "Starter Plus", "planNamePremiumPlus": "Premium Plus", "planPriceBasicPlus": "$100/an", "planPriceStarterPlus": "$200/an", "planPricePremiumPlus": "$300/an", "planTokensBasicPlus": "5M jetons par mois", "planTokensStarterPlus": "25M jetons par mois", "planTokensPremiumPlus": "80M jetons par mois", "priceSeparator": "/", "currentPlanLabel": "Plan actuel", "upgradePlanLabel": "Mettre à niveau le plan", "minimumBulkPurchaseError": "L'achat minimum de jetons est de {{amount}}", "maximumBulkPurchaseError": "L'achat maximum de jetons est de {{amount}}", "million": "Million", "millions": "Millions", "nextPage": "<PERSON> suivante", "previousPage": "<PERSON> p<PERSON>", "paginationStatus": "Affichage de {{from}} à {{to}} sur un total de {{total}} résultats"}