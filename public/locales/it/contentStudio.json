{"title": "Studio dei Contenuti", "searchPlaceholder": "Cerca immagini...", "loading": "Caricamento...", "cannotDeleteNonEmptyFolder": "Impossibile eliminare una cartella non vuota. Sposta o elimina prima il suo contenuto.", "confirmDeleteTitle": "Conferma eliminazione", "confirmDeleteMessage": "Sei sicuro di voler eliminare le immagini selezionate? Questa azione non può essere annullata.", "confirmDeleteSingleImageTitle": "Conferma eliminazione", "confirmDeleteSingleImageMessage": "Sei sicuro di voler eliminare questa immagine? Questa azione non può essere annullata.", "confirmDeleteFolderTitle": "<PERSON><PERSON> cartella", "confirmDeleteFolderMessage": "Sei sicuro di voler eliminare la cartella \"{{folderName}}\"? Questa azione non può essere annullata.", "delete": "Elimina", "deleteFolder": "<PERSON><PERSON> cartella", "useInProject": "<PERSON>a nel progetto", "uploadImages": "Carica immagini", "moveToFolder": "Sposta nella cartella", "addTags": "Aggiungi tag", "clearSelection": "Deseleziona tutto", "sort.newest": "<PERSON><PERSON>", "sort.oldest": "<PERSON><PERSON> vecchi", "sort.name-asc": "Nome (A-Z)", "sort.name-desc": "Nome (Z-A)", "sort.size-asc": "Dimensione (più piccola)", "sort.size-desc": "Dimensione (più grande)", "view.grid": "Vista a griglia", "view.list": "Vista a elenco", "filters": "<PERSON><PERSON><PERSON>", "applyFilters": "Applica filtri", "noImages": "Nessuna immagine trovata.", "noFolders": "Nessuna cartella disponibile.", "createFolder": "<PERSON><PERSON> cartella", "renameFolder": "R<PERSON><PERSON> cartella", "confirm": "Conferma", "cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON>", "folderSearchPlaceholder": "Cerca cartelle...", "foldersLabel": "<PERSON><PERSON><PERSON>", "searchResults": "Risultati di ricerca", "noFoldersFound": "Nessuna cartella trovata", "allFiles": "Tutti i file", "myFolders": "Le mie cartelle", "createSubfolder": "<PERSON><PERSON>", "folderNames": {"all": "Tutti i file", "recent": "<PERSON><PERSON>", "favorites": "Preferiti"}, "sort": {"label": "Ordina", "newest": "Più recenti prima", "oldest": "<PERSON><PERSON> vecchi prima", "nameAsc": "Nome (A-Z)", "nameDesc": "Nome (Z-A)", "sizeAsc": "Dimensione (più piccola)", "sizeDesc": "Dimensione (più grande)"}, "noImagesFound": "<PERSON>essuna immagine trovata", "noImagesHelp": "Carica alcune immagini o seleziona un'altra cartella", "viewDetails": "Visualizza <PERSON>", "view": "Visualizza", "tableHeader": {"name": "Nome", "size": "Dimensione", "type": "Tipo", "date": "Data"}, "imageDetails": {"title": "Dettag<PERSON> immagine", "videoTitle": "Dettagli del Video"}, "videoUrl": "URL del Video", "edit": "Modifica", "save": "<PERSON><PERSON>", "tags": "Tag", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addTagPlaceholder": "Aggiungi un tag...", "uploaded": "Caricato:", "fileInfo": "Informazioni file", "type": "Tipo", "size": "Dimensione", "dimensions": "Dimensioni", "location": "Posizione", "imageUrl": "URL immagine", "urlCopied": "URL copiato!", "noTags": "<PERSON><PERSON><PERSON> tag", "move": "Sposta", "deleteImageTitle": "Elimina immagine", "deleteImageMessage": "Sei sicuro di voler eliminare questa immagine? Questa azione non può essere annullata.", "deleteImageConfirm": "Elimina immagine", "moveDialog": {"title": "Sposta elementi", "selectDestination": "Seleziona la cartella di destinazione:", "rootOption": "<PERSON>dice (Tutti i file)", "noFolders": "Nessun'altra cartella disponibile", "moveButton": "Sposta elementi"}, "editConfirm": {"title": "Salva modifiche", "message": "Sei sicuro di voler salvare le modifiche a questo elemento? Ciò aggiornerà i metadati nella tua libreria.", "confirm": "Salva modifiche"}, "clearFilters": "<PERSON><PERSON><PERSON><PERSON> filtri", "dateRange": "Intervallo di date", "dateFrom": "Da", "dateTo": "A", "searchTagsPlaceholder": "Cerca tag...", "noTagsMatchSearch": "N<PERSON>un tag corrisponde alla ricerca", "noTagsAvailable": "Nessun tag disponibile", "selected": "selezionato", "uploadFiles": "Carica file", "uploading": "Caricamento in corso...", "dropFilesHere": "Rilascia i file qui", "dragDropFilesHere": "Trascina e rilascia i file qui", "releaseToUpload": "Rilascia per caricare i file", "dragDropOrBrowse": "Trascina qui i file immagine o clicca sul pulsante in basso per cercarli", "browseFiles": "Sfoglia file", "fileTitle": "<PERSON><PERSON>", "tagsForFile": "Tag per", "pressEnterToAddTag": "Premi Invio per aggiungere ogni tag", "folderLocation": "Posizione della cartella", "rootAllFiles": "<PERSON>dice (Tutti i file)", "noFilesSelected": "Nessun file selezionato", "selectFilesHint": "Seleziona i file trascinandoli nell'area di caricamento o utilizzando il pulsante di ricerca", "supportedFormats": "Formati supportati: JPG, PNG, GIF, WebP", "maxFileSize": "Dimensione massima file: 5 MB", "filesToUpload": "File da caricare", "addMore": "Aggiungi altro", "selectFilesToUpload": "Seleziona i file da caricare", "upload": "Carica", "errorMaxFiles": "Puoi selezionare fino a {{count}} file.", "errorFileSizeExceeded": "\"{{fileName}}\" supera la dimensione massima consentita. Saltato...", "errorNoValidFiles": "Nessun file valido da caricare.", "errorMissingFileTitle": "Tutti i file devono avere un nome.", "errorFileTypeNotSupported": "Il tipo di file di {{fileName}} non è supportato. Formati supportati: JPG, PNG, GIF, WebP.", "uploadError": "Si è verificato un errore durante il caricamento.", "placeholderAddTag": "Aggiungi un tag e premi Invio", "enterTagsForSingle": "Inserisci tag per 1 elemento", "enterTagsForMultiple": "Inserisci tag per più elementi", "addAnotherTagPlaceholder": "Aggiungi un altro tag...", "commonlyUsedTags": "Tag più usati", "addTagsButton": "Aggiungi tag", "suggestedTags": {"app": "App", "code": "Codice", "coding": "Programmazione", "coffee": "Caffè", "css": "CSS", "design": "Design", "development": "<PERSON><PERSON><PERSON><PERSON>", "html": "HTML", "javascript": "JavaScript", "laptop": "Laptop", "mobile": "Mobile", "programming": "Programmazione", "react": "React", "screen": "<PERSON><PERSON><PERSON>", "web": "Web", "webdev": "Sviluppo Web", "workspace": "Spazio di lavoro"}, "createNewFolder": "Crea nuova cartella", "folderName": "Nome cartella", "placeholderFolderName": "Inserisci il nome della cartella", "parentFolder": "Cartella principale", "errorFolderNameRequired": "Il nome della cartella è obbligatorio", "errorFolderAlreadyExists": "Esiste già una cartella con questo nome", "newFolderName": "Nuovo nome cartella", "placeholderNewFolderName": "Inserisci il nuovo nome della cartella", "errorFileMustHaveName": "Il file deve avere un nome.", "chooseAnImage": "<PERSON><PERSON><PERSON> un'immagine", "uploadSomeImages": "Carica alcune immagini o seleziona un'altra cartella"}