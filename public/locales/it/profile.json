{"userInformation": "Informazioni utente", "firstName": "Nome", "lastName": "Cognome", "username": "Nome Utente", "phone": "Telefono", "preferredLanguage": "Lingua preferita", "billingInformation": "Informazioni di fatturazione", "addressLine1": "Indirizzo Linea 1", "addressLine2": "Indirizzo Linea 2", "city": "Città", "zipCode": "CAP", "referralCode": "Codice di riferimento", "referralLink": "Link di riferimento", "referral": "Riferimento", "location": "Posizione", "site": "<PERSON><PERSON>", "email": "E-mail", "contactInfo": "Informazioni di contatto", "websiteUrl": "URL del sito web", "accountDeletion": "Eliminazione dell'account", "deleteAccount": "Elimina account", "deleteAccountWarning": "In caso di eliminazione, rimuoverai tutti i tuoi progetti e i tuoi dati personali. Inoltre, perderai il controllo dei progetti nelle tue organizzazioni.", "connectAccount": "Collega il tuo account {{platform}}", "connectNow": "<PERSON><PERSON><PERSON> ora", "connected": "<PERSON><PERSON><PERSON>", "personalWebsite": "Sito personale", "facebook": "Facebook", "twitterX": "Twitter/X", "linkedin": "LinkedIn", "youtube": "YouTube", "instagram": "Instagram", "tiktok": "TikTok", "Country": "<PERSON><PERSON>", "ChangePassword": "Cambia password", "Updating": "Aggiornamento...", "Save": "<PERSON><PERSON>", "Edit": "Modifica", "CurrentPassword": "Password attuale", "NewPassword": "Nuova password", "RepeatNewPassword": "R<PERSON>eti nuova password", "AllPasswordFieldsAreRequired": "<PERSON>tti i campi della password sono obbligatori", "NewPasswordsDoNotMatch": "Le nuove password non corrispondono", "PasswordMustBeAtLeastCharactersLong": "La password deve essere lunga almeno {{minLength}} caratteri", "UserNotFound": "Utente non trovato", "CurrentPasswordIsIncorrect": "La password attuale è errata", "PasswordUpdated": "La tua password è stata aggiornata con successo!", "ErrorWhileUpdatingPassword": "Errore durante l'aggiornamento della password", "Success": "Successo", "Error": "Errore", "updateAccount": "Il tuo account è stato aggiornato con successo!", "failedUpdateAccount": "Qualcosa è andato storto. Per favore riprova.", "Close": "<PERSON><PERSON>", "firstNameRequired": "Il nome è obbligatorio", "lastNameRequired": "Il cognome è obbligatorio", "usernameRequired": "Il nome utente è obbligatorio", "phoneRequired": "Il numero di telefono è obbligatorio", "address1Required": "L'indirizzo è obbligatorio", "cityRequired": "La città è obbligatoria", "zipCodeRequired": "Il CAP è obbligatorio", "countryRequired": "Il paese è obbligatorio", "emailRequired": "L'email è obbligatoria", "EmailInvalid": "Inserisci un indirizzo email valido", "URLInvalid": "Inserisci un URL valido", "copyReferralCode": "Copia il codice di riferimento", "copyReferralLink": "Copia il link di riferimento", "purchaseDetails": "Dettagli dell'acquisto", "dateLabel": "Data", "planLabel": "Piano", "amountLabel": "Importo", "tokensLabel": "Token", "whatsNext": "E ora?", "yourTokensAvailable": "I tuoi token sono ora disponibili nel tuo account", "purchaseDetailsStored": "I dettagli dell'acquisto sono stati salvati nel tuo account", "viewPurchaseHistory": "Puoi visualizzare la cronologia degli acquisti nella sezione fatturazione", "thankYou": "Grazie!", "purchaseSuccessful": "Acquisto effettuato con successo", "startCoding": "Inizia a programmare", "tokenTopUpComingSoon": "Ricarica dei token in arrivo", "finalisingPricing": "Stiamo finalizzando il nostro sistema di prezzi per offrirti le migliori tariffe e sconti per volume.", "getReady": "Preparati a pacchetti di token flessibili per soddisfare le tue esigenze di sviluppo.", "gotIt": "Capito", "checkBackSoon": "<PERSON><PERSON> presto per acquistare token", "buyExtraTokens": "Acquista token extra", "paymentMethod": "Metodo di pagamento", "howManyTokens": "Quanti token desideri?", "placeholderEnterTokens": "Inserisci il numero di token...", "units": "Unità", "availableDiscounts": "Sconti disponibili", "priceLabel": "Prezzo:", "discountAppliedLabel": "Sconto applicato:", "at": "A", "perMillion": "per milione di token", "purchaseTokens": "Acquista token", "minimumPurchaseError": "Acquisto minimo {minTokens} token ($1)", "minimumTokenError": "Acquisto minimo di {minTokens} token", "maximumTokenError": "Acquisto massimo di {maxTokens} token", "modalTitle": "Scegli un piano o ricarica i token quando necessario", "modalSubtitle": "Scegli tra i nostri tre piani flessibili o acquista token extra in qualsiasi momento per continuare a codificare senza limiti.", "highlightExtraTokens": "token extra", "toggleMonthly": "<PERSON><PERSON><PERSON>", "toggleYearly": "Annuale (risparmia 10%)", "buttonNeedMoreTokens": "Hai bisogno di più token?", "recentInvoicesTitle": "<PERSON>ture recenti", "recentInvoicesDescription": "Controlla la cronologia della fatturazione e scarica le fatture", "tableHeaderDate": "Data", "tableHeaderDescription": "Descrizione", "tableHeaderAmount": "Importo", "tableHeaderStatus": "Stato", "tableHeaderActions": "Azioni", "noInvoicesFound": "Nessuna fattura trovata", "actionPreviewInvoice": "Anteprima fattura", "actionDownloadInvoice": "Scarica fattura", "planDescription": "Perfetto per chi vuole esplorare lo sviluppo, imparare nuove competenze o lavorare a progetti personali.", "planNameBasicPlus": "Basic Plus", "planNameStarterPlus": "Starter Plus", "planNamePremiumPlus": "Premium Plus", "planPriceBasicPlus": "$100/anno", "planPriceStarterPlus": "$200/anno", "planPricePremiumPlus": "$300/anno", "planTokensBasicPlus": "5M token al mese", "planTokensStarterPlus": "25M token al mese", "planTokensPremiumPlus": "80M token al mese", "priceSeparator": "/", "currentPlanLabel": "Piano attuale", "upgradePlanLabel": "Aggiorna piano", "nextPage": "Pagina successiva", "previousPage": "<PERSON><PERSON><PERSON>e", "paginationStatus": "Visualizzazione da {{from}} a {{to}} di {{total}} risultati"}