{"userInformation": "ユーザー情報", "firstName": "名", "lastName": "姓", "username": "ユーザー名", "phone": "電話", "preferredLanguage": "希望の言語", "billingInformation": "請求情報", "addressLine1": "住所 1", "addressLine2": "住所 2", "city": "市区町村", "zipCode": "郵便番号", "referralCode": "紹介コード", "referralLink": "紹介リンク", "referral": "紹介", "location": "場所", "site": "サイト", "email": "メール", "contactInfo": "連絡先情報", "websiteUrl": "ウェブサイト URL", "accountDeletion": "アカウント削除", "deleteAccount": "アカウントを削除", "deleteAccountWarning": "アカウントを削除すると、すべてのプロジェクトと個人データが削除されます。また、組織内のプロジェクトの管理権限も失われます。", "connectAccount": "{{platform}} アカウントを接続", "connectNow": "今すぐ接続", "connected": "接続済み", "personalWebsite": "個人ウェブサイト", "facebook": "Facebook", "twitterX": "Twitter/X", "linkedin": "LinkedIn", "youtube": "YouTube", "instagram": "Instagram", "tiktok": "TikTok", "Country": "国", "ChangePassword": "パスワードを変更", "Updating": "更新中...", "Save": "保存", "Edit": "編集", "CurrentPassword": "現在のパスワード", "NewPassword": "新しいパスワード", "RepeatNewPassword": "新しいパスワードを再入力", "AllPasswordFieldsAreRequired": "すべてのパスワードフィールドが必要です", "NewPasswordsDoNotMatch": "新しいパスワードが一致しません", "PasswordMustBeAtLeastCharactersLong": "パスワードは少なくとも{{minLength}}文字である必要があります", "UserNotFound": "ユーザーが見つかりません", "CurrentPasswordIsIncorrect": "現在のパスワードが正しくありません", "PasswordUpdated": "パスワードが正常に更新されました！", "ErrorWhileUpdatingPassword": "パスワードの更新中にエラーが発生しました", "Success": "成功", "Error": "エラー", "updateAccount": "アカウントが正常に更新されました！", "failedUpdateAccount": "問題が発生しました。もう一度お試しください。", "Close": "閉じる", "firstNameRequired": "名は必須です", "lastNameRequired": "姓は必須です", "usernameRequired": "ユーザー名は必須です", "phoneRequired": "電話番号は必須です", "address1Required": "住所は必須です", "cityRequired": "市区町村は必須です", "zipCodeRequired": "郵便番号は必須です", "countryRequired": "国は必須です", "emailRequired": "メールアドレスは必須です", "EmailInvalid": "有効なメールアドレスを入力してください", "URLInvalid": "有効なURLを入力してください", "copyReferralCode": "紹介コードをコピー", "copyReferralLink": "紹介リンクをコピー", "purchaseDetails": "購入の詳細", "dateLabel": "日付", "planLabel": "プラン", "amountLabel": "金額", "tokensLabel": "トークン", "whatsNext": "次は何をしますか？", "yourTokensAvailable": "トークンがアカウントに追加されました", "purchaseDetailsStored": "購入の詳細がアカウントに保存されました", "viewPurchaseHistory": "請求セクションで購入履歴を確認できます", "thankYou": "ありがとうございます！", "purchaseSuccessful": "購入が成功しました", "startCoding": "コーディングを始めましょう", "tokenTopUpComingSoon": "トークンのチャージはまもなく開始されます", "finalisingPricing": "最適な価格とボリュームディスカウントを提供するため、価格体系を最終調整中です。", "getReady": "開発ニーズに応える柔軟なトークンパッケージをご用意します。", "gotIt": "了解", "checkBackSoon": "トークン購入のため、後ほどご確認ください", "buyExtraTokens": "追加トークンを購入", "paymentMethod": "支払い方法", "howManyTokens": "トークンの数量は？", "placeholderEnterTokens": "トークン数を入力してください...", "units": "単位", "availableDiscounts": "利用可能な割引", "priceLabel": "価格：", "discountAppliedLabel": "適用された割引：", "at": "で", "perMillion": "100万トークンあたり", "purchaseTokens": "トークンを購入する", "minimumPurchaseError": "最小購入は {minTokens} トークン（$1）です", "minimumTokenError": "最小トークン購入数は {minTokens} です", "maximumTokenError": "最大トークン購入数は {maxTokens} です", "modalTitle": "プランを選択するか、必要に応じてトークンを補充", "modalSubtitle": "3つの柔軟なプランから選ぶか、いつでも追加トークンを購入して、開発を止めないでください。", "highlightExtraTokens": "追加トークン", "toggleMonthly": "月額", "toggleYearly": "年額（10％割引）", "buttonNeedMoreTokens": "もっとトークンが必要ですか？", "recentInvoicesTitle": "最近の請求書", "recentInvoicesDescription": "請求履歴を確認し、請求書をダウンロード", "tableHeaderDate": "日付", "tableHeaderDescription": "説明", "tableHeaderAmount": "金額", "tableHeaderStatus": "ステータス", "tableHeaderActions": "操作", "noInvoicesFound": "請求書が見つかりません", "actionPreviewInvoice": "請求書をプレビュー", "actionDownloadInvoice": "請求書をダウンロード", "planDescription": "開発を始める方、スキルを学びたい方、個人プロジェクトに取り組む方に最適です。", "planNameBasicPlus": "Basic Plus", "planNameStarterPlus": "Starter Plus", "planNamePremiumPlus": "Premium Plus", "planPriceBasicPlus": "$100/年", "planPriceStarterPlus": "$200/年", "planPricePremiumPlus": "$300/年", "planTokensBasicPlus": "月間 5M トークン", "planTokensStarterPlus": "月間 25M トークン", "planTokensPremiumPlus": "月間 80M トークン", "priceSeparator": "/", "currentPlanLabel": "現在のプラン", "upgradePlanLabel": "プランをアップグレード", "nextPage": "次のページ", "previousPage": "前のページ", "paginationStatus": "{{total}} 件中 {{from}} 件から {{to}} 件を表示"}