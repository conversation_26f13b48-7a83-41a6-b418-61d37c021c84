{"whatWouldYouLikeToBuild": "당신의 아이디어를 몇 분 만에 실시간 웹사이트나 앱으로 전환하세요", "whatWouldYouLikeToBuildSubtitle": "만약 당신이 <1>상상</1>할 수 있다면, <5>코드화</5>할 수 있습니다.", "fromIdeaToDeployment": "무료로 시작하세요. 무엇이든 코딩하세요. 모든 프롬프트로 실력을 기회로 바꾸세요.", "codePlaceholder": "상상할 수 있다면, BIELA가 코드를 작성할 수 있습니다. 오늘은 무엇을 할까요?", "defaultPlaceholder": "오늘 어떻게 도와드릴까요? 함께 멋진 것을 만들어봅시다", "checkingFeatures": "기능 확인 중", "checklists": "체크리스트", "runUnitTestsSuggestionTitle": "제안", "runUnitTestsSuggestionMessage": "프로젝트의 단위 테스트를 실행하시겠습니까?", "runUnitTestsPrimaryButton": "단위 테스트 실행", "runUnitTestsSecondaryButton": "취소", "createDatabaseTitle": "데이터베이스 생성", "createDatabaseMessage": "프로젝트용 데이터베이스를 생성하시겠습니까?", "createDatabasePrimaryButton": "데이터베이스 생성", "createDatabaseSecondaryButton": "취소", "extendedThinking": "확장된 사고", "extendedThinkingTooltip": "AI가 응답하기 전에 더 깊이 생각하도록 허용", "firstResponseOnly": "첫 번째 응답만", "always": "항상", "AIReasoning": "AI 추론", "thinking": "사고", "attachFile": "파일 첨부", "voiceInput": "음성 입력", "selectLanguage": "음성 입력 언어 선택", "languageSelectionDisabled": "녹음 중에는 언어 선택이 비활성화됨", "notAvailableInFirefox": "이 기능은 Firefox에서는 사용할 수 없지만 Chrome 및 Safari에서는 사용할 수 있습니다", "enhancePrompt": "프롬프트 향상", "cleanUpProject": "프로젝트 정리", "clearChatHistory": "채팅 기록 지우기", "ChatNotFound": "채팅이나 사용자를 찾을 수 없습니다", "ChatClearFailedServer": "채팅 기록 삭제 실패 (서버 오류).", "NoMessagesToClear": "삭제할 메시지가 없습니다", "ChatClearSuccess": "채팅 기록이 삭제되었습니다!", "ChatClearFailedUnexpected": "채팅 기록 삭제 실패 (예상치 못한 오류).", "ClearChatTitle": "채팅 기록 지우기", "ClearChatConfirm": "채팅 기록을 지우시겠습니까?", "ClearChatIrreversible": "이 작업은 되돌릴 수 없습니다.", "ClearChatPro": "토큰을 확보하여 성능 향상.", "Advantage": "장점", "Disadvantage": "단점", "ClearChatCon": "AI가 이전 메시지를 기억하지 못하지만, 코드에는 계속 접근할 수 있습니다.", "showPrompt": "프롬프트 표시", "hidePrompt": "프롬프트 숨기기", "sendButton": "보내기", "abortButton": "취소", "inspirationTitle": "영감이 필요하신가요? 다음 예제를 시도해 보세요:", "cleanUpPrompt": "각 파일이 300줄을 넘지 않도록 프로젝트를 정리하세요. 큰 파일은 완전한 기능을 유지하면서 더 작은 모듈형 컴포넌트로 리팩토링합니다. 더 이상 필요하지 않은 모든 파일, 코드, 컴포넌트 및 중복 데이터를 식별하고 제거하세요. 모든 컴포넌트가 기존 시스템에 지장을 주지 않고 올바르게 연결되어 있고 작동하는지 확인하세요. 변경 사항이 오류를 유발하거나 기존 기능을 손상시키지 않는지 검증하여 코드의 무결성을 유지하세요. 목표는 효율성, 유지 관리성 및 명료성 측면에서 프로젝트를 최적화하는 것입니다.", "checklistPrompt": "내 초기 프롬프트를 살펴보고, 목표를 단계별로 이해한 후, 완료된 항목에는 초록색 체크 표시, 아직 해야 할 항목에는 빨간색 체크 표시가 있는 체크리스트를 만들어 주세요.", "personalPortfolioIdea": "다크 테마의 개인 포트폴리오 웹사이트 만들기", "recipeFinderIdea": "재료에 따라 요리를 제안하는 레시피 찾기 앱 구축", "weatherDashboardIdea": "애니메이션 배경이 있는 날씨 대시보드 디자인", "habitTrackerIdea": "진행 상황이 시각화되는 습관 추적기 개발", "loading": "로딩 중", "checkingYourAuthenticity": "진위를 확인하는 중입니다", "error": "오류", "succes": "성공!", "tryAgain": "다시 시도하세요", "dashboard": "대시보드", "getStartedTitle": "시작하기", "getStartedSub": "Biela.dev의 작동 방식을 확인하세요", "createProject": "새 프로젝트 만들기", "createProjectSub": "처음부터 시작하기", "editProjectName": "프로젝트 이름 편집", "editName": "이름 편집", "uploadProject": "프로젝트 업로드", "uploadProjectSub": "기존 프로젝트 가져오기", "importChat": "채팅 가져오기", "importChatSub": "기존 채팅을 가져오기", "createFolder": "새 폴더 만들기", "createFolderSub": "프로젝트를 정리하기", "cancel": "취소", "changeFolder": "폴더 변경", "save": "저장", "importing": "가져오는 중...", "importFolder": "폴더 가져오기", "giveTitle": "제목 지정", "projects": "프로젝트", "searchProjects": "프로젝트 검색...", "becomeAffiliate": "제휴사가 되기", "exclusiveGrowth": "독점 성장 혜택", "lifetimeEarnings": "평생 수익", "highCommissions": "높은 커미션", "earnCommission": "첫 판매에서 50% 커미션 받기", "joinAffiliateProgram": "제휴 프로그램에 참여하기", "folders": "폴더", "organizeProjects": "프로젝트를 카테고리별로 정리하기", "createNewFolder": "새 폴더 만들기", "enterFolderName": "폴더 이름 입력", "editFolder": "폴더 편집", "deleteFolder": "폴더 삭제", "all": "전체", "webProjects": "웹 프로젝트", "mobilepps": "모바일 앱", "developmentComparison": "개발 비교", "traditionalVsAI": "전통 vs AI", "traditional": "전통적인", "standardApproach": "표준 접근법", "developmentCost": "개발 비용", "developmentTime": "개발 시간", "costSavings": "비용 절감", "reducedCosts": "절감된 비용", "timeSaved": "절약된 시간", "fasterDelivery": "더 빠른 납품", "bielaDevAI": "Biela.dev AI", "nextGenDevelopment": "차세대 개발", "developmentCosts": "개발 비용", "openInGitHub": "GitHub에서 열기", "downloadProject": "프로젝트 다운로드", "duplicateProject": "프로젝트 복제", "openProject": "프로젝트 열기", "deleteProject": "프로젝트 삭제", "confirmDelete": "이 폴더를 삭제하시겠습니까?", "invoicePreview": "청구서 미리보기", "settings": {"title": "설정", "deployment": {"AdvancedSettings": {"advanced-settings": "고급 설정", "configure-advanced-deployment-options": "고급 배포 옵션 구성", "server-configuration": "서버 구성", "memory-limit": "메모리 제한", "region": "지역", "security-settings": "보안 설정", "enable-ddos-protection": "DDoS 보호 활성화", "protect-against-distributed": "분산 서비스 거부 공격으로부터 보호", "ip-whitelisting": "IP 화이트리스트", "restrict-acces-to": "특정 IP 주소로 접근 제한", "deployment-options": "배포 옵션", "auto-deploy": "자동 배포", "automatically-deploy-when": "메인 브랜치에 푸시 시 자동 배포", "preview-deployments": "미리보기 배포 표시", "create-preview-deployments": "풀 리퀘스트용 미리보기 배포 생성"}, "BuildSettings": {"build-and-deployment-settings": "빌드 및 배포 설정", "build-command": "빌드 명령어", "override": "재정의", "output-directory": "출력 디렉토리", "override2": "재정의"}, "DatabaseConfiguration": {"database-configuration": "데이터베이스 구성", "configure-your-db-connections": "데이터베이스 연결 및 설정 구성", "database-type": "데이터베이스 유형", "connection-string": "연결 문자열", "your-db-credentials": "데이터베이스 자격 증명은 암호화되어 안전하게 저장됩니다", "database-settings": "데이터베이스 설정", "pool-size": "풀 크기", "require": "필요", "prefer": "선호", "disable": "비활성화", "add-database": "데이터베이스 추가"}, "DomainSettings": {"domain-settings": "도메인 설정", "configure-your-custom-domain": "사용자 지정 도메인 및 SSL 인증서 구성", "custom-domain": "사용자 지정 도메인", "add": "추가", "ssl-certificate": "SSL 인증서", "auto-renew-ssl-certificates": "SSL 인증서 자동 갱신", "auto-renew-before-expiry": "만료 전에 SSL 인증서 자동 갱신", "force-https": "HTTPS 강제 적용", "redirect-all-http-traffic": "모든 HTTP 트래픽을 HTTPS로 리디렉션", "active-domain": "활성 도메인", "remove": "제거"}, "EnvironmentVariables": {"environment-variables": "환경 변수", "configure-environment-variables": "배포를 위한 환경 변수 구성", "all-enviroments": "모든 환경", "environment-variables2": "환경 변수", "preview": "미리보기", "development": "개발", "create-new": "새로 만들기", "key": "키", "value": "값", "save-variable": "변수 저장", "you-can-also-import": ".env 파일에서 변수도 가져올 수 있습니다:", "import-env-file": ".env 파일 가져오기"}, "ProjectConfiguration": {"project-configuration": "프로젝트 구성", "config-your-project-settings": "프로젝트 설정 및 배포 옵션 구성", "project-url": "프로젝트 URL", "framework": "프레임워크", "repo": "저장소", "branch": "브랜치", "main": "main", "development": "development", "staging": "staging"}}, "identity": {"unverified": {"title": "신원 확인", "description": "Biela.dev를 사용하려면 신원을 확인하세요", "subtitle": "안전한 확인 프로세스", "processServers": "모든 카드 정보는 Biela의 서버가 아닌 Stripe의 서버에 안전하게 저장됩니다", "processCharge": "구독에 대한 명시적인 동의 없이 카드는 청구되지 않습니다", "processBenefits": "Biela.dev는 2025년 5월 15일까지 인증된 계정에 대해 완전히 무료입니다", "verifyStripe": "신용카드 또는 체크카드로 인증", "verifyStripeDescription": "확인을 위해 결제 방법을 연결하세요", "verifyNow": "지금 확인"}, "verified": {"title": "신원 확인됨", "description": "귀하의 신원이 성공적으로 확인되었습니다", "paymentMethod": "결제 방법", "cardEnding": "카드 끝자리", "updatePayment": "결제 방법 업데이트", "untilDate": "2025년 5월 15일까지", "freeAccess": "무료 액세스", "freeAccessDescription": "Biela.dev의 전체 액세스를 비용 없이 즐기세요", "secureStorage": "안전한 저장", "secureStorageDescription": "귀하의 카드 정보는 Biela의 서버가 아닌 Stripe의 서버에 안전하게 저장됩니다. 귀하의 명시적인 동의 없이 구독을 위해 카드가 청구되지 않습니다.", "subscriptionAvailable": "구독은 2025년 5월 15일부터 이용하실 수 있습니다."}, "connectingToStripe": "Stripe에 연결 중..."}, "tabs": {"billing": "청구", "profile": "프로필", "deployment": "배포", "identity": "정체성"}}, "help": {"title": "어떻게 도와드릴까요?", "searchPlaceholder": "문서를 검색하세요...", "categories": {"getting-started": {"title": "시작하기", "description": "Biela.dev 사용의 기본을 배우세요", "articles": ["빠른 시작 가이드", "플랫폼 개요", "첫 번째 프로젝트 만들기", "AI 개발 이해하기"]}, "ai-development": {"title": "AI 개발", "description": "AI 지원 개발을 마스터하세요", "articles": ["효과적인 프롬프트 작성", "코드 생성 베스트 프랙티스", "AI 디버깅 팁", "고급 AI 기능"]}, "project-management": {"title": "프로젝트 관리", "description": "프로젝트를 체계적으로 관리하세요", "articles": ["프로젝트 구조", "팀 협업", "버전 관리", "배포 옵션"]}}, "channels": {"docs": {"name": "문서", "description": "포괄적인 가이드와 API 참조"}, "community": {"name": "커뮤니티", "description": "다른 개발자들과 소통하세요"}, "github": {"name": "GitHub", "description": "문제를 보고하고 기여하세요"}}, "support": {"title": "추가 도움이 필요하신가요?", "description": "저희 지원팀은 연중무휴 24시간, 언제든지 질문이나 문제에 도움을 드릴 준비가 되어 있습니다.", "button": "지원 문의하기"}}, "getStarted": {"title": "Biela.dev의 작동 방식을 알아보세요", "description": "Biela.dev로 몇 분 만에 앱을 만드세요. 저희 AI는 설정부터 배포까지 개발 프로세스 전체를 자동화합니다. 저희 AI가 어떻게 당신의 앱을 손쉽게 구축하는지 확인해 보세요!", "features": {"docs": {"title": "개발자 문서", "description": "쉽게 따라할 수 있는 가이드, 팁 및 베스트 프랙티스로 Biela.dev 사용법을 배우세요. 초보자와 숙련된 개발자 모두에게 완벽합니다!", "cta": "개발자 문서 탐색"}, "support": {"title": "피드백 및 지원", "description": "도움이 필요하시거나 피드백을 주고 싶다면, 지원팀에 연락하여 Biela.dev 개선에 동참하세요.", "cta": "피드백 보내기"}, "platform": {"title": "플랫폼 기능", "description": "Biela.dev가 제공하는 강력한 도구들을 활용하여 손쉽게 웹사이트와 앱을 만드세요. AI가 코딩을 대신합니다!", "cta": "기능 탐색"}}, "video": {"title": "빠른 시작 동영상", "description": "Biela.dev가 어떻게 당신을 위해 구축하는지 보세요 – 손쉽게 앱과 웹을 만듭니다!", "cta": "튜토리얼 보기"}, "guide": {"title": "빠른 시작 가이드", "steps": {"setup": {"title": "즉시 프로젝트 설정하기", "description": "몇 초 안에 개발 환경을 준비하세요"}, "generate": {"title": "AI로 풀스택 코드 생성하기", "description": "AI가 프로덕션 준비 코드를 작성하도록 하세요"}, "features": {"title": "즉시 기능 생성", "description": "간단한 프롬프트로 복잡한 기능을 추가하세요"}, "editor": {"title": "노코드 & 로우코드 에디터", "description": "비주얼 또는 코드 방식으로 앱을 수정하세요"}, "optimize": {"title": "실시간 최적화 및 테스트", "description": "앱이 완벽하게 작동하는지 확인하세요"}, "deploy": {"title": "원클릭 배포", "description": "자동 배포로 즉시 라이브로 전환하세요"}}}, "faq": {"title": "자주 묻는 질문", "questions": {"what": {"question": "Biela.dev란 무엇인가요?", "answer": "Biela.dev는 코딩 지식이 없어도 웹사이트와 앱을 만들 수 있도록 돕는 AI 기반 플랫폼입니다. 코드 작성부터 배포까지 전체 개발 과정을 자동화합니다."}, "experience": {"question": "Biela.dev를 사용하려면 프로그래밍 경험이 필요할까요?", "answer": "아니요! Biela.dev는 초보자와 숙련된 개발자 모두를 위해 설계되었습니다. AI의 도움을 받아 구축하거나 생성된 코드를 필요에 따라 수정할 수 있습니다."}, "projects": {"question": "어떤 종류의 프로젝트를 만들 수 있나요?", "answer": "웹사이트, 웹 애플리케이션, 모바일 앱, SaaS 플랫폼, 전자상거래 사이트, 관리 대시보드 등 다양한 프로젝트를 만들 수 있습니다."}, "edit": {"question": "Biela.dev가 생성한 코드를 수정할 수 있나요?", "answer": "네! Biela.dev는 AI가 생성한 코드를 사용자 정의하거나 노코드/로우코드 에디터를 사용하여 쉽게 수정할 수 있도록 합니다."}, "deployment": {"question": "배포는 어떻게 이루어지나요?", "answer": "원클릭으로 Biela.dev가 프로젝트를 배포하여 온라인에 즉시 공개하고 사용 가능하게 만듭니다. 수동 서버 설정이 필요하지 않습니다!"}, "pricing": {"question": "Biela.dev는 무료인가요?", "answer": "Biela.dev는 기본 기능을 갖춘 무료 플랜을 제공합니다. 보다 고급 도구와 리소스가 필요할 경우 프리미엄 플랜으로 업그레이드할 수 있습니다."}, "integrations": {"question": "타사 도구나 데이터베이스를 통합할 수 있나요?", "answer": "네! Biela.dev는 MongoDB, Firebase, Supabase 등 인기 있는 데이터베이스와 타사 API와의 통합을 지원합니다."}, "help": {"question": "문제가 발생했을 때 어디서 도움을 받을 수 있나요?", "answer": "개발자 문서, 빠른 시작 가이드를 참고하거나 헬프 센터를 통해 지원팀에 문의할 수 있습니다."}}}, "cta": {"title": "시작할 준비가 되셨나요?", "description": "Biela.dev로 첫 번째 프로젝트를 만들고 개발의 미래를 경험해 보세요.", "button": "새 프로젝트 만들기"}}, "confirmDeleteProject": "프로젝트 삭제 확인", "confirmRemoveFromFolder": "폴더에서 제거 확인", "deleteProjectWarning": "{{projectName}}을(를) 영구적으로 삭제하시겠습니까?", "removeFromFolderWarning": "{{projectName}}을(를) {{folderName}}에서 제거하시겠습니까?", "confirm": "확인", "confirmDeleteFolder": "폴더 삭제 확인", "deleteFolderWarning": "{{folderName}}을(를) 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.", "folderDeletedSuccessfully": "폴더가 성공적으로 삭제되었습니다", "downloadChat": "채팅 다운로드", "inactiveTitle": "이 탭은 비활성 상태입니다", "inactiveDescription": "아래 버튼을 클릭하여 이 탭을 활성화하고 앱 사용을 계속하세요.", "inactiveButton": "이 탭 사용하기", "suggestions": {"weatherDashboard": "날씨 대시보드 만들기", "ecommercePlatform": "전자상거래 플랫폼 구축", "socialMediaApp": "소셜 미디어 앱 디자인", "portfolioWebsite": "포트폴리오 웹사이트 생성", "taskManagementApp": "작업 관리 앱 만들기", "fitnessTracker": "피트니스 트래커 구축", "recipeSharingPlatform": "레시피 공유 플랫폼 디자인", "travelBookingSite": "여행 예약 사이트 만들기", "learningPlatform": "학습 플랫폼 구축", "musicStreamingApp": "음악 스트리밍 앱 디자인", "realEstateListing": "부동산 매물 만들기", "jobBoard": "구직 게시판 구축"}, "pleaseWait": "잠시만 기다려주세요...", "projectsInAll": "모든 프로젝트", "projectsInCurrentFolder": "{{folderName}}의 프로젝트", "createYourFirstProject": "첫 번째 프로젝트 만들기", "startCreateNewProjectDescription": "멋진 것을 만들기 시작하세요. 프로젝트를 생성하면 여기에 표시됩니다.", "createProjectBtn": "새 프로젝트", "publishedToHackathon": "해커톤에 게시됨", "publishToHackathon": "해커톤에 게시", "refreshSubmission": "제출 새로고침", "hackathonInformation": "해커톤 정보", "selectFolder": "폴더 선택", "folder": "폴더", "selectAFolder": "폴더를 선택하세요", "thisProject": "이 프로젝트", "projectDeletedSuccessfully": "프로젝트가 성공적으로 삭제되었습니다", "projectRemovedFromFolder": "폴더에서 프로젝트가 제거되었습니다", "unnamedProject": "이름 없는 프로젝트", "permanentDeletion": "영구 삭제", "removeFromFolder": "{{folderName}}에서 제거", "verifiedAccount": "인증된 계정", "hasSubmittedAMinimumOfOneProject": "최소한 하나의 프로젝트를 제출했습니다", "haveAtLeastActiveReferrals": "최소 {{number}}개의 활성 추천이 있습니다", "GoToAffiliateDashBoard": "제휴 대시보드로 이동", "LikeOneProjectThatBelongsToAnotherUser": "다른 사용자의 프로젝트를 좋아합니다", "GoToHackathonPage": "해커톤 페이지로 이동", "VibeCodingHackathonStatus": "Vibe Coding 해커톤 상태", "ShowcaseYourBestWork": "최고의 작품을 선보이세요", "submissions": "제출물", "qualifyConditions": "자격 조건", "completed": "완료됨", "collapseQualifyConditions": "자격 조건 접기", "expandQualifyConditions": "자격 조건 확장", "basicParticipation": "기본 참여", "complete": "완료됨", "incomplete": "미완료", "forTop3Places": "상위 3위 안에 들기 위해", "deleteSubmission": "제출물 삭제", "view": "보기", "submitMoreProjectsToIncreaseYourChangesOfWining": "승리 가능성을 높이기 위해 더 많은 프로젝트를 제출하세요!", "removeFromHackathon": "해커톤에서 제거하시겠습니까?", "removeFromHackathonDescription": "Vibe Coding 해커톤에서 <project>{{project}}</project>를 제거하시겠습니까? 이 작업은 되돌릴 수 없습니다.", "cardVerificationRequired": "카드 인증 필요", "pleaseVerifyCard": "계속하려면 카드를 인증하세요", "unlockFeaturesMessage": "플랫폼의 모든 기능에 대한 전체 액세스를 잠금 해제하려면 빠른 카드 인증이 필요합니다. 이 프로세스는 완전히 안전하며 Biela.dev에서 원활한 경험을 보장합니다. 인증 중에는 카드에 요금이 청구되지 않습니다.", "freeVerificationNotice": "인증 혜택", "accessToAllFeatures": "Biela.dev 기능에 대한 전체 액세스.", "enhancedFunctionality": "플랫폼 성능 향상.", "quickSecureVerification": "안전하고 암호화된 인증 프로세스.", "noCharges": "*추가 요금이나 숨겨진 비용 없음", "verificationOnly": " — 인증 전용.", "verifyNow": "지금 인증", "DropFilesToUpload": "업로드할 파일을 드롭하세요", "projectInfo": {"information": "프로젝트 정보", "type": "프로젝트 유형", "complexity": "복잡성", "components": "구성 요소", "features": "기능", "confidenceScore": "신뢰도 점수", "estimationUncertainty": "예측 불확실성", "keyTechnologies": "핵심 기술"}, "projectMetrics": {"teamComposition": "팀 구성", "hoursBreakdown": "시간 분배", "timeToMarket": "출시까지 시간", "maintenance": "유지보수", "aiPowered": "AI 기반", "developerLevel": "개발자 수준", "nextGenAI": "차세대 AI", "keyBenefits": "핵심 이점", "instantDevelopment": "즉시 개발", "noMaintenanceCosts": "유지보수 비용 없음", "highConfidence": "높은 신뢰도", "productionReadyCode": "프로덕션 준비 코드", "immediate": "즉시", "uncertainty": "불확실성", "minimal": "최소"}, "loadingMessages": {"publish": ["프로젝트 정보 가져오는 중...", "스크린샷 생성 중...", "요약 및 설명 생성 중..."], "refresh": ["현재 제출 데이터 가져오는 중...", "프로젝트 스크린샷 업데이트 중...", "프로젝트 세부 정보 새로 고침 중..."]}, "errorMessages": {"publish": "\"{{projectName}}\"을(를) 해커톤에 게시할 수 없습니다. 요청을 처리하는 중 문제가 발생했습니다.", "refresh": "\"{{projectName}}\" 제출을 새로 고칠 수 없습니다. 서버가 프로젝트 정보를 업데이트할 수 없습니다."}, "actions": {"refresh": {"title": "제출 새로 고침", "successMessage": "프로젝트 제출이 성공적으로 업데이트되었습니다.", "buttonText": "업데이트된 제출 보기"}, "publish": {"title": "해커톤에 게시 중", "successMessage": "프로젝트가 해커톤에 제출되었습니다.", "buttonText": "해커톤 페이지에서 보기"}}, "SupabaseConnect": "Supabase 연결", "SupabaseDashboard": "Supabase 대시보드", "RestoreApp": "복원", "SaveApp": "저장", "ForkChat": "포크", "BielaTerminal": "Biela 터미널", "UnitTesting": "단위 테스트", "InstallDependencies": "의존성 설치", "InstallDependenciesDescription": "프로젝트에 필요한 모든 패키지를 다음을 사용하여 설치합니다", "BuildProject": "프로젝트 빌드", "BuildProjectDescription": "프로젝트를 프로덕션 용도로 컴파일하고 최적화합니다 (사용)", "StartDevelopment": "개발 시작", "StartDevelopmentDescription": "라이브 미리보기를 위한 개발 서버를 시작합니다 (사용)", "NoProjectFound": "해당 이름의 프로젝트를 찾을 수 없습니다.", "NoProjectFoundDescription": " 철자 오류가 있는지 확인하고 다시 시도해 주세요.", "ContextLimitReached": "컨텍스트 한도 도달", "ClaudeContextDescription1": "이 프로젝트에서 Claude의 컨텍스트 용량에 도달했습니다. 걱정하지 마세요. 더 큰 컨텍스트 창을 가진 Gemini 모델로 전환하여 계속할 수 있습니다.", "ClaudeContextDescription2": "Gemini 모델은 최대 5배 더 많은 컨텍스트 용량을 제공하여 코드, 채팅 기록을 유지하고 프로젝트를 중단 없이 계속할 수 있습니다.", "SelectModelToContinue": "계속할 모델 선택:", "Performance": "성능", "UpgradePlan": "프리미엄 플랜", "PremiumFeatureRequired": "프리미엄 기능이 필요합니다", "LargeContextUpgradeInfo": "대용량 컨텍스트 창 모델은 더 높은 구독 등급이 필요합니다. 업그레이드하여 모델을 이용하세요.", "PremiumModelsAvailable": "업그레이드 시 사용 가능한 프리미엄 모델:", "UpgradeTooltip": "업그레이드하여 잠금 해제 ", "UpgradeTooltipSuffix": "패키지", "UpgradeToContinue": "계속하려면 업그레이드하세요", "PremiumBadge": "프리미엄", "AlternativeLimitExplanation": "이 프로젝트에 대해 AI가 처리 한도에 도달한 것으로 보입니다. 대부분의 공간은 채팅이 아닌 가져온 파일에 의해 사용되고 있습니다.", "SuggestedSolutions": "제안된 해결 방법:", "ReimportProject": "새 프로젝트로 다시 가져오기", "ReimportProjectDescription": "채팅 기록을 지우고 컨텍스트 공간을 확보하며, 파일은 그대로 유지됩니다.", "BreakIntoProjects": "여러 프로젝트로 분할", "BreakIntoProjectsDescription": "작업을 별도로 개발할 수 있는 작은 구성 요소로 나눕니다.", "ExportWork": "완료된 작업 내보내기", "ExportWorkDescription": "완성된 파일을 다운로드 및 보관하여 컨텍스트 공간을 확보하세요.", "AlternativeContextNote": "사용 가능한 컨텍스트를 최대한 활용하려면 사용하지 않는 파일이나 라이브러리를 제거하고 현재 개발 단계에 필요한 핵심 파일에 집중하세요.", "ContinueWithSelectedModel": "선택한 모델로 계속하기", "Close": "닫기", "AIModel": "AI 모델", "Active": "활성", "Stats": "통계", "Cost": "비용", "ExtendedThinkingDisabledForModel": "이 모델에서는 사용할 수 없습니다", "ExtendedThinkingAlwaysOn": "이 모델은 항상 활성화되어 있습니다", "limitReached": "한도에 도달했습니다!", "deleteProjectsSupabase": "프로젝트를 일부 삭제하거나 Supabase에서 한도를 늘리세요.", "goTo": "이동하기", "clickProjectSettings": " 프로젝트를 클릭하고, 프로젝트 설정으로 이동하여, 아래로 스크롤 후 클릭", "delete": "삭제", "retrying": "재시도 중…", "retryConnection": "연결 재시도", "RegisterPageTitle": "회원가입 – biela.dev", "RegisterPageDescription": "biela.dev에서 계정을 생성하고 모든 기능을 이용하세요.", "SignUpHeading": "회원가입", "AlreadyLoggedInRedirectHome": "이미 로그인되어 있습니다! 홈으로 이동 중...", "PasswordsMismatch": "비밀번호가 일치하지 않습니다.", "FirstNameRequired": "이름은 필수 항목입니다", "LastNameRequired": "성은 필수 항목입니다", "UsernameRequired": "사용자 이름은 필수입니다", "EmailRequired": "이메일은 필수입니다", "EmailInvalid": "유효한 이메일 주소를 입력하세요", "TooManyRequests": "요청이 너무 많습니다. 나중에 다시 시도해주세요", "SomethingWentWrongMessage": "문제가 발생했습니다. 나중에 다시 시도해주세요", "PasswordRequired": "비밀번호는 필수입니다", "ConfirmPasswordRequired": "비밀번호를 확인해 주세요", "AcceptTermsRequired": "서비스 약관과 개인정보처리방침에 동의해야 합니다", "CaptchaRequired": "CAPTCHA를 완료해 주세요", "RegistrationFailed": "회원가입에 실패했습니다", "EmailConfirmationSent": "확인 이메일이 전송되었습니다! 이메일을 확인한 후 로그인해 주세요.", "RegistrationServerError": "회원가입 실패 (서버에서 false를 반환함).", "SomethingWentWrong": "문제가 발생했습니다", "CheckEmailHeading": "회원가입 확인을 위해 이메일을 확인하세요", "CheckEmailDescription": "확인 링크가 포함된 이메일을 보냈습니다.", "GoToHomepage": "홈페이지로 이동", "ReferralCodeOptional": "추천 코드 (선택사항)", "EnterReferralCode": "다른 사용자로부터 초대받은 경우 추천 코드를 입력하세요.", "PasswordPlaceholder": "비밀번호", "ConfirmPasswordPlaceholder": "비밀번호 확인", "CreateAccount": "계정 생성", "AlreadyHaveAccountPrompt": "이미 계정이 있으신가요?", "Login": "로그인", "AcceptTermsPrefix": "다음에 동의합니다:", "TermsOfService": "서비스 약관", "AndSeparator": "및", "PrivacyPolicy": "개인정보처리방침", "LoginPageTitle": "로그인 – biela.dev", "LoginPageDescription": "계정에 로그인하거나 biela.dev에 로그인하여 모든 기능을 이용하세요.", "LogInHeading": "로그인", "EmailOrUsernamePlaceholder": "이메일 / 사용자 이름", "ForgotPassword?": "비밀번호를 잊으셨나요?", "LoginToProfile": "프로필에 로그인", "UserNotConfirmed": "계정이 확인되지 않았습니다", "ConfirmEmailNotice": "계정을 활성화하려면 이메일을 확인해야 합니다.", "ResendConfirmationEmail": "확인 이메일 다시 보내기", "ResendConfirmationSuccess": "확인 이메일이 다시 전송되었습니다! 받은 편지함을 확인하세요.", "ResendConfirmationError": "확인 이메일 재전송 실패.", "LoginSuccess": "로그인 성공! 이동 중...", "LoginFailed": "로그인 실패", "LoginWithGoogle": "Google로 로그인", "LoginWithGitHub": "GitHub으로 로그인", "SignUpWithGoogle": "Google로 가입하기", "SignUpWithGitHub": "GitHub로 가입하기", "Or": "또는", "NoAccountPrompt": "계정이 없으신가요?", "SignMeUp": "회원가입하기", "ForgotPasswordPageTitle": "비밀번호 찾기 – biela.dev", "ForgotPasswordPageDescription": "biela.dev 계정의 비밀번호를 재설정하고 액세스를 복구하세요.", "BackToLogin": "로그인으로 돌아가기", "ForgotPasswordHeading": "비밀번호 찾기", "ForgotPasswordDescription": "이메일 주소를 입력하시면 비밀번호를 재설정할 수 있는 링크를 보내드립니다.", "VerificationLinkSent": "확인 링크가 전송되었습니다! 이메일을 확인하세요.", "EnterYourEmailPlaceholder": "이메일을 입력하세요", "Sending": "보내는 중...", "SendVerificationCode": "확인 코드 보내기", "SendVerificationLink": "인증 링크 보내기", "InvalidConfirmationLink": "유효하지 않은 확인 링크입니다", "Back": "뒤로가기", "ResetPassword": "비밀번호 재설정", "ResetPasswordDescription": "새 비밀번호를 설정하세요", "NewPasswordPlaceholder": "새 비밀번호", "ConfirmNewPasswordPlaceholder": "새 비밀번호 확인", "ResetPasswordButton": "비밀번호 재설정", "PasswordRequirements": "비밀번호는 최소 8자 이상이며 대문자, 소문자, 숫자, 특수문자를 포함해야 합니다.", "PasswordUpdatedSuccess": "비밀번호가 성공적으로 변경되었습니다!", "affiliateDashboard": "제휴 대시보드", "userDashboard": "사용자 대시보드", "returnToAffiliateDashboard": "제휴 대시보드로 돌아가기", "returnToUserDashboard": "사용자 대시보드로 돌아가기", "myProfile": "내 프로필", "viewAndEditYourProfile": "프로필 보기 및 편집", "billing": "결제", "manageYourBillingInformation": "결제 정보 관리", "logout": "로그아웃", "logoutDescription": "계정에서 로그아웃", "SupabaseNotAvailable": "Supabase를 지금 사용할 수 없습니다. 나중에 다시 시도해 주세요.", "projectActions": {"invalidSlug": "잘못된 프로젝트 슬러그입니다.", "transferLimit": "이 프로젝트의 이 버전을 이미 해당 사용자에게 전송했습니다. 다시 전송하려면 변경 사항을 적용하세요.", "downloadSuccess": "프로젝트가 성공적으로 다운로드되었습니다!", "downloadError": "프로젝트 다운로드에 실패했습니다.", "exportSuccess": "채팅이 내보내졌습니다! 다운로드 폴더를 확인하세요.", "exportError": "채팅 내보내기에 실패했습니다.", "duplicateSuccess": "채팅이 성공적으로 복제되었습니다!", "duplicateError": "채팅 복제에 실패했습니다."}, "enter_new_phone_number": "새 전화번호 입력", "enter_new_phone_number_below": "새 전화번호를 아래에 입력하세요:", "new_phone_placeholder": "새 전화번호", "enter_otp_code": "OTP 코드 입력", "confirm_phone_message": "계정을 사용하려면 전화번호를 확인해야 합니다. ({{phone}})로 전송된 코드를 입력하세요.", "wrong_phone": "전화번호가 틀렸나요?", "resend_sms": "SMS 다시 보내기", "submit": "제출", "tokensAvailable": "사용 가능한 토큰", "sectionTitle": "도메인 관리", "addDomainButton": "도메인 이름 추가", "connectCustomDomainTitle": "사용자 지정 도메인 연결", "disclaimer": "면책 조항:", "disclaimerText": "성공적으로 검증하려면 위의 모든 DNS 규칙을 정확하게 설정해야 합니다", "domainInputDescription": "이 프로젝트에 연결할 도메인 이름을 입력하세요.", "domainLabel": "도메인 이름", "domainPlaceholder": "example.com", "cancelButton": "취소", "continueButton": "도메인 이름 추가", "deployingText": "배포 중...", "addingText": "추가 중...", "verifyButtonText": "검증", "configureDnsTitle": "DNS 레코드 구성", "configureDnsDescription": "소유권 확인 및 이 프로젝트에 연결하기 위해 아래 DNS 레코드를 도메인에 추가하세요.", "tableHeaderType": "유형", "tableHeaderName": "이름", "tableHeaderValue": "값", "note": "참고:", "noteText": "DNS 변경 사항은 최대 48시간이 소요될 수 있습니다. 하지만 일반적으로 몇 분에서 몇 시간 내에 반영됩니다.", "backButton": "뒤로", "showDnsButton": "DNS 설정 보기", "hideDnsButton": "DNS 설정 숨기기", "removeButton": "제거", "dnsSettingsTitle": "도메인 DNS 설정", "removeDomainConfirmTitle": "도메인 이름 제거", "removeConfirmationText": "정말로 이 도메인 이름을 제거하시겠습니까? ", "importantCleanupTitle": "중요한 DNS 정리", "cleanupDescription": "이 도메인을 프로젝트에서 제거한 후 설정 시 생성한 DNS 레코드도 반드시 삭제하세요. 이는 DNS 구성을 깔끔하게 유지하고 향후 충돌을 방지하는 데 도움이 됩니다.", "confirmRemoveButton": "도메인 이름 제거", "customConfigTitle": "사용자 지정 도메인 구성", "customConfigDescription": "자체 도메인을 이 프로젝트에 연결하세요. 기본 Biela 도메인을 통해 항상 접근 가능하지만, 사용자 지정 도메인을 사용하면 전문적인 브랜드 경험을 제공할 수 있습니다.", "defaultLabel": "기본값", "statusActive": "활성", "statusPending": "보류 중", "lastVerifiedText": "방금 확인됨", "errorInvalidDomain": "유효한 도메인 이름을 입력하세요 (예: example.com)", "errorDuplicateDomain": "이 도메인은 이미 프로젝트에 연결되어 있습니다", "errorAddFail": "도메인 이름 추가 실패.", "successAdd": "도메인 이름이 성공적으로 추가되었습니다! 프로젝트에 연결되었습니다.", "benefitsTitle": "도메인 혜택", "benefitSecurityTitle": "강화된 보안", "benefitSecurityDesc": "모든 사용자 지정 도메인은 자동으로 SSL 인증서로 보호됩니다.", "benefitPerformanceTitle": "빠른 성능", "benefitPerformanceDesc": "전 세계 CDN을 통해 빠른 로딩 속도를 제공합니다.", "benefitBrandingTitle": "전문적인 브랜드", "benefitBrandingDesc": "일관된 브랜드 경험을 위해 고유 도메인을 사용하세요.", "benefitAnalyticsTitle": "분석 통합", "benefitAnalyticsDesc": "사용자 지정 도메인은 분석 플랫폼과 원활하게 작동합니다.", "meta": {"index": {"title": "biela.dev | AI 기반 웹 & 앱 빌더 – 프롬프트로 빌드", "description": "biela.dev를 사용하여 아이디어를 실시간 웹사이트나 앱으로 변환하세요. AI 기반 프롬프트를 사용하여 사용자 지정 디지털 제품을 손쉽게 구축하세요."}, "login": {"title": "biela.dev 계정에 로그인", "description": "AI 생성 프로젝트를 관리하고 빌드하려면 biela.dev 대시보드에 액세스하세요."}, "register": {"title": "biela.dev에 가입 – AI로 빌드 시작", "description": "AI 기반 프롬프트를 사용하여 웹사이트와 앱을 빌드하기 위해 biela.dev 계정을 만드세요."}, "dashboard": {"title": "프로젝트 대시보드 – biela.dev", "description": "AI로 빌드된 웹사이트와 앱을 관리하고, 실시간 프로젝트를 편집하고, 빌드 기록을 추적하세요. 모두 한 곳에서!"}, "profile": {"title": "내 프로필 – biela.dev", "description": "biela.dev 계정 정보를 확인하고 업데이트하며, 환경설정을 관리하고 AI 개발 경험을 개인화하세요."}, "billing": {"title": "결제 – biela.dev에서 플랜을 안전하게 관리하세요", "description": "결제 설정에 접근하여 구독을 관리하고, 결제 수단을 업데이트하며, biela.dev 플랜을 효과적으로 제어하세요."}}, "transferProject": "복사본 공유", "transferSecurityNoteDescription": "수신자는 이 프로젝트 사본과 관련된 모든 리소스에 대한 전체 액세스 권한을 받게 됩니다.", "transferProjectDescription": "이 프로젝트의 복사본을 전송하려는 사람의 사용자 이름 또는 이메일을 입력하세요.", "transferProjectLabel": "사용자 이름 또는 이메일", "transferProjectPlaceholder": "johns<PERSON> 또는 <EMAIL>", "transferButton": "전송", "transferSecurityNote": "보안 알림:", "dontHavePermisionToTransfer": "이 프로젝트를 전송할 권한이 없습니다", "transferProjectUserNotFound": "{{ user }} 사용자를 찾을 수 없습니다!", "transferErrorOwnAccount": "프로젝트를 자신의 계정으로 이전할 수 없습니다.", "transferError": "프로젝트 전송 중 오류 발생", "transferSuccess": "{{ user }}에게 성공적으로 전송되었습니다", "enterValidEmailUsername": "사용자 이름 또는 이메일을 입력하세요", "enterMinValidEmailUsername": "유효한 사용자 이름(최소 3자) 또는 이메일 주소를 입력하세요", "youWillStillHaveAccess": "원래 프로젝트에 계속 액세스할 수 있습니다", "newChangesWillNotAffect": "새로운 변경 사항은 다른 사용자의 프로젝트에 영향을 주지 않습니다", "ProjectInformationNotLoaded": "프로젝트 정보를 불러올 수 없습니다.", "projectInfoTitle": "프로젝트 정보", "generalInformationProject": "프로젝트에 대한 일반 정보입니다.", "rename": "이름 변경", "lastlySavedAt": "마지막 저장 시간:", "noSavedAppVersion": "저장된 앱 버전이 없습니다.", "Owner": "소유자", "TechStack": "기술 스택", "FeatureCount": "기능 수", "UniqueComponentCount": "고유 컴포넌트 수", "inviteCollaborator": {"title": "협업자 초대", "message": "{{email}} 님을 이 프로젝트의 협업자로 초대하려고 합니다.", "action": "초대 보내기"}, "removeCollaborator": {"title": "협업자 제거", "message": "{{email}} 님을 이 프로젝트에서 제거하려고 합니다.", "action": "제거"}, "database": {"title": "데이터베이스 연결", "fields": {"created": "생성일", "size": "크기", "region": "지역", "version": "버전"}, "button": {"connected": "연결됨", "connect": "연결", "notConnected": "미연결"}}, "status": {"Online": "온라인", "Degraded": "성능 저하", "Restoring": "복원 중", "Restored": "복원 완료", "Creating": "생성 중", "Provisioning": "프로비저닝 중", "Coming Up": "시작 중", "Deleting": "삭제 중", "Deleted": "삭제됨", "Pausing": "일시 중지 중", "Paused": "일시 중지됨", "Inactive": "비활성", "Suspended": "중단됨", "Resuming": "재개 중", "Updating": "업데이트 중", "Migrating": "마이그레이션 중", "Maintenance": "점검 중", "Restarting": "재시작 중", "Backup in progress": "백업 진행 중", "Restore in progress": "복원 진행 중", "Failed": "실패", "Unknown": "알 수 없음", "Loading...": "로딩 중...", "Not Found": "찾을 수 없음", "Error": "오류"}, "titleChat": "{{typeCapitalized}} 채팅", "titleMessage": "{{typeCapitalized}} 메시지", "dialogDescriptionText": "{{type}} {{description}} 하려는 중입니다.", "confirmText": "이 채팅을 {{type}}하시겠습니까?", "deleteButton": "삭제", "deleteInfinitive": "삭제하다", "deleteNoun": "삭제", "duplicateButton": "복제", "duplicateInfinitive": "복제하다", "duplicateNoun": "복제", "downloadButton": "다운로드", "downloadInfinitive": "다운로드하다", "downloadNoun": "다운로드", "exportButton": "내보내기", "exportInfinitive": "내보내다", "exportNoun": "내보내기", "forkButton": "포크", "forkInfinitive": "포크하다", "forkNoun": "포크", "rollbackButton": "롤백", "rollbackInfinitive": "롤백하다", "rollbackNoun": "롤백", "saveButton": "저장", "saveInfinitive": "저장하다", "saveNoun": "저장", "restoreButton": "복원", "restoreInfinitive": "복원하다", "restoreNoun": "복원", "loadingOverlayChat": "{{type}} 채팅 중입니다. 잠시만 기다려주세요.", "loadingOverlayMessages": "{{type}} 메시지 중입니다. 잠시만 기다려주세요.", "exportProjectChat": "프로젝트 채팅 내보내기", "downloadProjectFiles": "프로젝트 파일 다운로드", "pleaseSelectCommit": "복원할 커밋을 선택하세요:", "refresh": "새로고침", "savingApp": "저장 중...", "confirmationDelete": "삭제", "confirmationDuplicate": "복제", "confirmationDownload": "다운로드", "confirmationExport": "내보내기", "confirmationFork": "포크", "confirmationRollback": "롤백", "confirmationSave": "저장", "confirmationRestore": "복원", "dialogTitleChat": "이 채팅을 {{typeCapitalized}}하시겠습니까?", "dialogTitleData": "데이터를 {{typeCapitalized}}하시겠습니까?", "dialogLoadingChat": "{{typeCapitalized}} 채팅 중...", "dialogLoadingData": "{{typeCapitalized}} 메시지 중...", "dialogDescription": "{{type}}하려고 합니다: {{description}}", "dialogConfirm": "정말로 {{type}}하시겠습니까?", "buttonCancel": "취소", "previousAppVersion": "이전 앱 버전", "CreatingDatabase": "데이터베이스 생성 중...", "DatabaseDashboard": "대시보드", "ConnectDatabase": "연결", "DatabaseConnectionOptions": "연결 옵션", "LoadingYourSupabaseProjects": "프로젝트 로드 중...", "NoSupabaseProjectsFound": "프로젝트를 찾을 수 없습니다. 먼저 연결하세요.", "DatabaseOptions": "옵션", "DisconnectDatabase": "해제", "OpenSupabaseDashboard": "대시보드 열기", "LinkExistingProject": "프로젝트 연결", "IncludeDataWhenDuplicating": "복제 시 데이터 포함", "DuplicateProject": "프로젝트 복제", "CreateNewSupabaseDB": "새 Supabase DB 생성", "UseExistingSupabaseDB": "기존 Supabase DB 사용", "DisconnectSupabaseDB": "Supabase DB 연결 해제", "SharedFrom": "공유됨", "TokensExhausted": "토큰이 모두 소진되었습니다", "TokensExhaustedDescription": "프로젝트를 계속하려면 옵션을 선택하세요:", "upgradePlan": "내 요금제 업그레이드", "buyMoreTokens": "토큰 더 구매하기", "seeScreenshot": "스크린샷 보기"}