{"Claude4Description": "IA mais poderosa para sites premium", "Claude4FeatureFirst": "Código de alta qualidade", "Claude4FeatureSecond": "Funcionalidade avançada", "Claude4FeatureThird": "Design superior", "Claude4Performance": "Excelente", "Claude4Cost": "Premium", "Claude4ContextWindow": "Janela de contexto padrão (200K tokens)", "GeminiProDescription": "IA versátil com amplo contexto", "GeminiProFeatureFirst": "<PERSON><PERSON><PERSON> con<PERSON>", "GeminiProFeatureSecond": "Lida com entradas complexas", "GeminiProFeatureThird": "Boa qualidade de design", "GeminiProPerformance": "<PERSON><PERSON> bom", "GeminiProCost": "Premium intermediário", "GeminiProContextWindow": "Janela de contexto grande (1M+ tokens)", "GeminiFlashDescription": "Opção mais rápida e econômica", "GeminiFlashFeatureFirst": "Geração rápida", "GeminiFlashFeatureSecond": "Funcionalidade básica", "GeminiFlashFeatureThird": "Designs simples", "GeminiFlashPerformance": "Bo<PERSON>", "GeminiFlashCost": "Econômico", "GeminiFlashContextWindow": "Janela de contexto grande (1M+ tokens)"}