{"title": "Estúdio de Conteúdo", "searchPlaceholder": "Procurar imagens...", "loading": "A carregar...", "cannotDeleteNonEmptyFolder": "Não é possível eliminar uma pasta que não está vazia. Por favor, mova ou elimine o conteúdo primeiro.", "confirmDeleteTitle": "Confirmar elimina<PERSON>", "confirmDeleteMessage": "Tem certeza de que deseja eliminar as imagens selecionadas? Esta ação não pode ser desfeita.", "confirmDeleteSingleImageTitle": "Confirmar elimina<PERSON>", "confirmDeleteSingleImageMessage": "Tem certeza de que deseja eliminar esta imagem? Esta ação não pode ser desfeita.", "confirmDeleteFolderTitle": "Eliminar Pasta", "confirmDeleteFolderMessage": "Tem certeza de que deseja eliminar a pasta \"{{folderName}}\"? Esta ação não pode ser desfeita.", "delete": "Eliminar", "deleteFolder": "Eliminar Pasta", "useInProject": "Usar no projeto", "uploadImages": "<PERSON><PERSON><PERSON>", "moveToFolder": "Mover para <PERSON>a", "addTags": "<PERSON><PERSON><PERSON><PERSON>", "clearSelection": "<PERSON><PERSON>", "sort.newest": "<PERSON><PERSON>", "sort.oldest": "<PERSON><PERSON>", "sort.name-asc": "Nome (A-Z)", "sort.name-desc": "Nome (Z-A)", "sort.size-asc": "<PERSON><PERSON><PERSON> (Menor)", "sort.size-desc": "<PERSON><PERSON><PERSON> (Maior)", "view.grid": "Visualização em Grelha", "view.list": "Visualização em Lista", "filters": "<PERSON><PERSON><PERSON>", "applyFilters": "Aplicar Filtros", "noImages": "<PERSON>enhuma imagem encontrada.", "noFolders": "Nenhuma pasta disponível.", "createFolder": "<PERSON><PERSON><PERSON>", "renameFolder": "Renomear Pasta", "confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "folderSearchPlaceholder": "Procurar pastas...", "foldersLabel": "Pastas", "searchResults": "Resultados da Pesquisa", "noFoldersFound": "Nenhuma pasta encontrada", "allFiles": "Todos os Ficheiros", "myFolders": "Minhas Pastas", "createSubfolder": "Criar <PERSON>", "folderNames": {"all": "Todos os Ficheiros", "recent": "<PERSON><PERSON>", "favorites": "<PERSON><PERSON><PERSON><PERSON>"}, "sort": {"label": "Ordenar", "newest": "<PERSON><PERSON>", "oldest": "<PERSON><PERSON>", "nameAsc": "Nome (A-Z)", "nameDesc": "Nome (Z-A)", "sizeAsc": "<PERSON><PERSON><PERSON> (Menor)", "sizeDesc": "<PERSON><PERSON><PERSON> (Maior)"}, "noImagesFound": "Nenhuma imagem encontrada", "noImagesHelp": "Carregue algumas imagens ou selecione outra pasta", "viewDetails": "<PERSON><PERSON>", "view": "<PERSON>er", "tableHeader": {"name": "Nome", "size": "<PERSON><PERSON><PERSON>", "type": "Tipo", "date": "Data"}, "imageDetails": {"title": "<PERSON><PERSON><PERSON>", "videoTitle": "Detalhes do Vídeo"}, "videoUrl": "URL do Vídeo", "edit": "<PERSON><PERSON>", "save": "Guardar", "tags": "Etiquetas", "add": "<PERSON><PERSON><PERSON><PERSON>", "addTagPlaceholder": "Adicionar etiqueta...", "uploaded": "Carregado:", "fileInfo": "Informação do Ficheiro", "type": "Tipo", "size": "<PERSON><PERSON><PERSON>", "dimensions": "Dimensões", "location": "Localização", "imageUrl": "URL da Imagem", "urlCopied": "URL copiado!", "noTags": "Sem etiquetas", "move": "Mover", "deleteImageTitle": "Eliminar Imagem", "deleteImageMessage": "Tem certeza de que deseja eliminar esta imagem? Esta ação não pode ser desfeita.", "deleteImageConfirm": "Eliminar Imagem", "moveDialog": {"title": "Mover <PERSON><PERSON>", "selectDestination": "Selecione a pasta de destino:", "rootOption": "Raiz (Todos os Ficheiros)", "noFolders": "Nenhuma outra pasta disponível", "moveButton": "Mover <PERSON><PERSON>"}, "editConfirm": {"title": "Guardar Alterações", "message": "Tem certeza de que deseja guardar as alterações neste item? Isto irá atualizar os metadados na sua biblioteca.", "confirm": "Guardar Alterações"}, "clearFilters": "<PERSON><PERSON>", "dateRange": "Intervalo de Datas", "dateFrom": "De", "dateTo": "<PERSON><PERSON>", "searchTagsPlaceholder": "Procurar etiquetas...", "noTagsMatchSearch": "Nenhuma etiqueta corresponde à pesquisa", "noTagsAvailable": "Nenhuma etiqueta disponível", "selected": "selecionado", "uploadFiles": "<PERSON><PERSON><PERSON>", "uploading": "A carregar...", "dropFilesHere": "Solte os ficheiros aqui", "dragDropFilesHere": "Arraste e solte os ficheiros aqui", "releaseToUpload": "Solte para carregar os seus ficheiros", "dragDropOrBrowse": "Arraste e solte ficheiros de imagem aqui, ou clique no botão abaixo para procurar", "browseFiles": "<PERSON><PERSON><PERSON>", "fileTitle": "Título do Ficheiro", "tagsForFile": "Etiquetas para", "pressEnterToAddTag": "Pressione Enter para adicionar cada etiqueta", "folderLocation": "Localização da Pasta", "rootAllFiles": "Raiz (Todos os Ficheiros)", "noFilesSelected": "Nenhum ficheiro se<PERSON>", "selectFilesHint": "Selecione ficheiros arrastando-os para a área de carregamento ou utilizando o botão de procurar", "supportedFormats": "Formatos suportados: JPG, PNG, GIF, WebP", "maxFileSize": "Tamanho máximo do ficheiro: 5MB", "filesToUpload": "Ficheiros a carregar", "addMore": "<PERSON><PERSON><PERSON><PERSON>", "selectFilesToUpload": "Selecionar ficheiros para carregar", "upload": "<PERSON><PERSON><PERSON>", "errorMaxFiles": "<PERSON><PERSON> pode selecionar até {{count}} ficheiros.", "errorFileSizeExceeded": "\"{{fileName}}\" excede o tamanho máximo permitido. Ignorado...", "errorNoValidFiles": "Nenhum ficheiro válido para carregar.", "errorMissingFileTitle": "Todos os ficheiros devem ter um nome.", "errorFileTypeNotSupported": "O tipo de arquivo de {{fileName}} não é suportado. Formatos suportados: JPG, PNG, GIF, WebP.", "uploadError": "Ocorreu um erro ao fazer o upload.", "placeholderAddTag": "Adicionar uma etiqueta e pressionar Enter", "enterTagsForSingle": "Inserir etiquetas para 1 item", "enterTagsForMultiple": "Inserir etiquetas para vários itens", "addAnotherTagPlaceholder": "Adicionar outra etiqueta...", "commonlyUsedTags": "Etiquetas mais usadas", "addTagsButton": "<PERSON><PERSON><PERSON><PERSON>", "suggestedTags": {"app": "App", "code": "Código", "coding": "Programação", "coffee": "Café", "css": "CSS", "design": "Design", "development": "Desenvolvimento", "html": "HTML", "javascript": "JavaScript", "laptop": "<PERSON><PERSON><PERSON>", "mobile": "Mobile", "programming": "Programação", "react": "React", "screen": "Ecrã", "web": "Web", "webdev": "Desenvolvimento Web", "workspace": "Espaço de Trabalho"}, "createNewFolder": "Criar <PERSON>", "folderName": "<PERSON>me da Pasta", "placeholderFolderName": "Inserir nome da pasta", "parentFolder": "Pasta Pai", "errorFolderNameRequired": "Nome da pasta é obrigatório", "errorFolderAlreadyExists": "Já existe uma pasta com este nome", "newFolderName": "Novo Nome da Pasta", "placeholderNewFolderName": "Inserir novo nome da pasta", "errorFileMustHaveName": "O ficheiro deve ter um nome.", "chooseAnImage": "Escolha uma imagem", "uploadSomeImages": "Envie algumas imagens ou selecione uma pasta diferente"}