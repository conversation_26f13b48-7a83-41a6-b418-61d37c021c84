{"userInformation": "Informações do Usuário", "firstName": "Primeiro Nome", "lastName": "Sobrenome", "username": "Nome de Usuário", "phone": "Telefone", "preferredLanguage": "Idioma Preferido", "billingInformation": "Informações de Faturamento", "addressLine1": "Endereço Linha 1", "addressLine2": "Endereço Linha 2", "city": "Cidade", "zipCode": "CEP", "referralCode": "Código de Referência", "referralLink": "Link de Referência", "referral": "Referência", "location": "Localização", "site": "Site", "email": "E-mail", "contactInfo": "Informações de contato", "websiteUrl": "URL do Site", "accountDeletion": "Exclusão de Conta", "deleteAccount": "Excluir Conta", "deleteAccountWarning": "Em caso de exclusão, você removerá todos os seus projetos e dados pessoais. <PERSON><PERSON><PERSON> disso, você perderá o controle sobre os projetos nas suas organizações.", "connectAccount": "Conecte sua conta {{platform}}", "connectNow": "Conectar Agora", "connected": "Conectado", "personalWebsite": "Site pessoal", "facebook": "Facebook", "twitterX": "Twitter/X", "linkedin": "LinkedIn", "youtube": "YouTube", "instagram": "Instagram", "tiktok": "TikTok", "Country": "<PERSON><PERSON>", "ChangePassword": "<PERSON><PERSON><PERSON><PERSON>", "Updating": "Atualizando...", "Save": "<PERSON><PERSON>", "Edit": "<PERSON><PERSON>", "CurrentPassword": "<PERSON><PERSON> atual", "NewPassword": "Nova senha", "RepeatNewPassword": "<PERSON>etir nova senha", "AllPasswordFieldsAreRequired": "Todos os campos de senha são obrigatórios", "NewPasswordsDoNotMatch": "As novas senhas não coincidem", "PasswordMustBeAtLeastCharactersLong": "A senha deve ter pelo menos {{minLength}} caracteres", "UserNotFound": "Usuário não encontrado", "CurrentPasswordIsIncorrect": "A senha atual está incorreta", "PasswordUpdated": "Sua senha foi atualizada com sucesso!", "ErrorWhileUpdatingPassword": "Erro ao atualizar a senha", "Success": "Sucesso", "Error": "Erro", "updateAccount": "Sua conta foi atualizada com sucesso!", "failedUpdateAccount": "Algo deu errado. Por favor, tente novamente.", "Close": "<PERSON><PERSON><PERSON>", "firstNameRequired": "O nome é obrigatório", "lastNameRequired": "O sobrenome é obrigatório", "usernameRequired": "O nome de usuário é obrigatório", "phoneRequired": "O número de telefone é obrigatório", "address1Required": "O endereço é obrigatório", "cityRequired": "A cidade é obrigatória", "zipCodeRequired": "O código postal é obrigatório", "countryRequired": "O país é obrigatório", "emailRequired": "O e-mail é obrigatório", "EmailInvalid": "Por favor, insira um endereço de e-mail válido", "URLInvalid": "Por favor, insira uma URL válida", "copyReferralCode": "Copiar código de indicação", "copyReferralLink": "Copiar link de indicação", "purchaseDetails": "<PERSON><PERSON><PERSON> da compra", "dateLabel": "Data", "planLabel": "Plano", "amountLabel": "Valor", "tokensLabel": "Tokens", "whatsNext": "O que vem a seguir?", "yourTokensAvailable": "Seus tokens já estão disponíveis na sua conta", "purchaseDetailsStored": "Os de<PERSON>hes da compra foram armazenados na sua conta", "viewPurchaseHistory": "Você pode ver seu histórico de compras na seção de faturamento", "thankYou": "<PERSON><PERSON><PERSON>!", "purchaseSuccessful": "Sua compra foi bem-sucedida", "startCoding": "Comece a programar", "tokenTopUpComingSoon": "Recarga de tokens em breve", "finalisingPricing": "Estamos finalizando nosso sistema de preços para oferecer as melhores tarifas e descontos por volume.", "getReady": "Prepare-se para pacotes de tokens flexíveis que atendem às suas necessidades de desenvolvimento.", "gotIt": "<PERSON><PERSON><PERSON>", "checkBackSoon": "Volte em breve para comprar tokens", "buyExtraTokens": "Comprar tokens extras", "paymentMethod": "Método de pagamento", "howManyTokens": "Quantos tokens você deseja?", "placeholderEnterTokens": "Digite a quantidade de tokens...", "units": "Unidades", "availableDiscounts": "Descontos disponíveis", "priceLabel": "Preço:", "discountAppliedLabel": "Desconto aplicado:", "at": "Em", "perMillion": "por milhão de tokens", "purchaseTokens": "Comprar tokens", "minimumPurchaseError": "A compra mínima é de {minTokens} tokens ($1)", "minimumTokenError": "Compra mínima de {minTokens} tokens", "maximumTokenError": "Compra máxima de {maxTokens} tokens", "modalTitle": "Escolha um plano ou recarregue tokens conforme necessário", "modalSubtitle": "Escolha entre nossos três planos flexíveis ou compre tokens extras a qualquer momento para continuar programando sem limites.", "highlightExtraTokens": "tokens extras", "toggleMonthly": "Mensal", "toggleYearly": "Anual (economize 10%)", "buttonNeedMoreTokens": "Precisa de mais tokens?", "recentInvoicesTitle": "Faturas recentes", "recentInvoicesDescription": "Acompanhe seu histórico de cobrança e baixe faturas", "tableHeaderDate": "Data", "tableHeaderDescription": "Descrição", "tableHeaderAmount": "Valor", "tableHeaderStatus": "Status", "tableHeaderActions": "Ações", "noInvoicesFound": "<PERSON>enhuma fatura encontrada", "actionPreviewInvoice": "<PERSON><PERSON><PERSON> fatura", "actionDownloadInvoice": "Baixar fatura", "planDescription": "Perfeito para quem está explorando desenvolvimento, aprendendo novas habilidades ou trabalhando em projetos pessoais.", "planNameBasicPlus": "Basic Plus", "planNameStarterPlus": "Starter Plus", "planNamePremiumPlus": "Premium Plus", "planPriceBasicPlus": "$100/ano", "planPriceStarterPlus": "$200/ano", "planPricePremiumPlus": "$300/ano", "planTokensBasicPlus": "5M tokens por mês", "planTokensStarterPlus": "25M tokens por mês", "planTokensPremiumPlus": "80M tokens por mês", "priceSeparator": "/", "currentPlanLabel": "Plano atual", "upgradePlanLabel": "Atualizar plano", "nextPage": "Próxima página", "previousPage": "Página anterior", "paginationStatus": "Mostrando de {{from}} até {{to}} de {{total}} resultados"}