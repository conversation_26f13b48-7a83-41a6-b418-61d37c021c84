{"whatWouldYouLikeToBuild": "Transforme sua ideia em um site ou app ao vivo em minutos", "whatWouldYouLikeToBuildSubtitle": "Se você pode <1>imaginar</1> isso, você pode <5>programá-lo</5>.", "fromIdeaToDeployment": "Comece de graça. Programe qualquer coisa. Transforme suas habilidades em oportunidades com cada prompt.", "codePlaceholder": "Se você consegue imaginar, a BIELA pode codificá-lo. O que vamos fazer hoje?", "defaultPlaceholder": "Como posso ajudar você hoje? Vamos fazer algo incrível juntos", "checkingFeatures": "Verificando as funcionalidades", "checklists": "Listas de verificação", "runUnitTestsSuggestionTitle": "Sugestão", "runUnitTestsSuggestionMessage": "Você gostaria de executar testes unitários para o seu projeto?", "runUnitTestsPrimaryButton": "Executar testes unitários", "runUnitTestsSecondaryButton": "<PERSON><PERSON><PERSON>", "createDatabaseTitle": "Criação de Base de Dados", "createDatabaseMessage": "Você gostaria de criar uma base de dados para o seu projeto?", "createDatabasePrimaryButton": "Criar Base de Dados", "createDatabaseSecondaryButton": "<PERSON><PERSON><PERSON>", "extendedThinking": "Pensamento estendido", "extendedThinkingTooltip": "Permitir que a IA pense mais profundamente antes de responder", "firstResponseOnly": "Apenas primeira resposta", "always": "Sempre", "AIReasoning": "Raciocínio de IA", "thinking": "Pensamento", "attachFile": "Anexar arquivo", "voiceInput": "Entrada de voz", "selectLanguage": "Selecionar idioma para entrada de voz", "languageSelectionDisabled": "Seleção de idioma desativada durante a gravação", "notAvailableInFirefox": "Este recurso não está disponível no Firefox, mas está no Chrome e no Safari", "enhancePrompt": "<PERSON><PERSON><PERSON>", "cleanUpProject": "Limpar projeto", "clearChatHistory": "<PERSON><PERSON>", "ChatNotFound": "<PERSON>enhum chat ou usu<PERSON>rio encontrado", "ChatClearFailedServer": "Falha ao limpar o histórico do chat (erro do servidor).", "NoMessagesToClear": "<PERSON>enhuma mensagem para limpar", "ChatClearSuccess": "Histórico do chat limpo!", "ChatClearFailedUnexpected": "Falha ao limpar o histórico do chat (erro inesperado).", "ClearChatTitle": "Limpar histórico do chat", "ClearChatConfirm": "Tem certeza de que deseja limpar o histórico do chat?", "ClearChatIrreversible": "Esta ação não pode ser desfeita.", "ClearChatPro": "Libera tokens para melhor desempenho.", "ClearChatCon": "A IA não lembrará mensagens anteriores, mas ainda terá acesso ao código.", "Advantage": "Vantagem", "Disadvantage": "Desvantagem", "showPrompt": "Mostrar prompt", "hidePrompt": "Ocultar prompt", "sendButton": "Enviar", "abortButton": "<PERSON><PERSON><PERSON>", "inspirationTitle": "Precisa de inspiração? Experimente uma destas opções:", "cleanUpPrompt": "Limpe o projeto certificando-se de que nenhum arquivo individual exceda 300 linhas de código. Refatore arquivos grandes em componentes modulares menores, mantendo a funcionalidade completa. Identifique e remova todos os arquivos, códigos, componentes e dados redundantes que não são mais necessários. Garanta que todos os componentes permaneçam conectados corretamente e funcionais, evitando qualquer interrupção no sistema existente. Mantenha a integridade do código verificando que nenhuma alteração introduz erros ou quebra as funcionalidades atuais. O objetivo é otimizar o projeto em termos de eficiência, manutenibilidade e clareza.", "checklistPrompt": "Analise meu prompt inicial, entenda o objetivo passo a passo e crie para mim uma lista de verificação com uma marca verde para tudo o que foi feito e uma marca vermelha para o que ainda falta ser feito.", "personalPortfolioIdea": "Crie um site de portfólio pessoal com um tema escuro", "recipeFinderIdea": "Crie um aplicativo de busca de receitas que sugira pratos com base nos ingredientes", "weatherDashboardIdea": "Projete um painel meteorológico com fundos animados", "habitTrackerIdea": "Desenvolva um rastreador de hábitos com visualização de progresso", "loading": "Carregando", "checkingYourAuthenticity": "Verificando sua autenticidade", "error": "Erro", "succes": "Sucesso!", "tryAgain": "Tentar Novamente", "dashboard": "<PERSON><PERSON>", "getStartedTitle": "<PERSON><PERSON><PERSON>", "getStartedSub": "Descubra como o Biela.dev funciona", "createProject": "Criar Novo Projeto", "createProjectSub": "Comece do zero", "uploadProject": "<PERSON>egar <PERSON>", "uploadProjectSub": "Importe um projeto existente", "importChat": "Importar chat", "importChatSub": "Importe um chat existente", "createFolder": "Criar uma nova pasta", "createFolderSub": "Organize seus projetos", "editProjectName": "Editar nome do projeto", "editName": "Editar nome", "cancel": "<PERSON><PERSON><PERSON>", "changeFolder": "Alterar <PERSON>", "save": "<PERSON><PERSON>", "importing": "Importando...", "importFolder": "Importar Pasta", "giveTitle": "Forneça um título", "projects": "Projetos", "searchProjects": "Pesquisar projetos...", "becomeAffiliate": "Torne-se um Afiliado", "exclusiveGrowth": "Benefícios Exclusivos de Crescimento", "lifetimeEarnings": "<PERSON><PERSON><PERSON>", "highCommissions": "Comissões <PERSON>eva<PERSON>", "earnCommission": "Ganhe 50% de comissão na sua primeira venda", "joinAffiliateProgram": "Junte-se ao nosso Programa de Afiliados", "folders": "Pastas", "organizeProjects": "Organize seus projetos por categoria", "createNewFolder": "Criar uma nova pasta", "enterFolderName": "Digite o nome da pasta", "editFolder": "<PERSON><PERSON>", "deleteFolder": "Excluir <PERSON>", "all": "Todos", "webProjects": "Projetos Web", "mobilepps": "Aplicativos Móveis", "developmentComparison": "Comparação de Desenvolvimento", "traditionalVsAI": "Tradicional vs IA", "traditional": "Tradicional", "standardApproach": "Abordagem Padrão", "developmentCost": "Custo de Desenvolvimento", "developmentTime": "Tempo de Desenvolvimento", "costSavings": "Economia de Custos", "reducedCosts": "<PERSON>ustos <PERSON>", "timeSaved": "Tempo Economizado", "fasterDelivery": "Entrega Mai<PERSON> R<PERSON>", "bielaDevAI": "Biela.dev IA", "nextGenDevelopment": "Desenvolvimento de Nova Geração", "developmentCosts": "Custos de Desenvolvimento", "openInGitHub": "Abrir no GitHub", "downloadProject": "Baixar Projeto", "duplicateProject": "Duplicar Projeto", "openProject": "Abrir Projeto", "deleteProject": "Excluir Projeto", "confirmDelete": "Excluir esta pasta?", "invoicePreview": "Pré-visualização da Fatura", "settings": {"title": "Configurações", "deployment": {"AdvancedSettings": {"advanced-settings": "Configurações Avançadas", "configure-advanced-deployment-options": "Configure opções avançadas de deployment", "server-configuration": "Configuração do Servidor", "memory-limit": "<PERSON><PERSON>", "region": "Região", "security-settings": "Configurações de Segurança", "enable-ddos-protection": "Ativar Proteção DDoS", "protect-against-distributed": "Proteger contra ataques distribuídos de negação de serviço", "ip-whitelisting": "Lista Branca de IPs", "restrict-acces-to": "Restringir o acesso a endereços IP específicos", "deployment-options": "Opções de Deployment", "auto-deploy": "Deployment Automático", "automatically-deploy-when": "Implantar automaticamente ao dar push na branch principal", "preview-deployments": "Visualizar Deployments de Pré-visualização", "create-preview-deployments": "Criar deployments de pré-visualização para pull requests"}, "BuildSettings": {"build-and-deployment-settings": "Configurações de Build e Deployment", "build-command": "Comando de Build", "override": "Substituir", "output-directory": "Diretório de Saída", "override2": "Substituir"}, "DatabaseConfiguration": {"database-configuration": "Configuração do Banco de Dados", "configure-your-db-connections": "Configure suas conexões e configurações de banco de dados", "database-type": "Tipo de Banco de Dados", "connection-string": "String de Conexão", "your-db-credentials": "Suas credenciais do banco de dados estão criptografadas e armazenadas com segurança", "database-settings": "Configurações do Banco de Dados", "pool-size": "Tamanho do <PERSON>", "require": "<PERSON><PERSON><PERSON>", "prefer": "Preferir", "disable": "Desativar", "add-database": "Adicionar <PERSON>"}, "DomainSettings": {"domain-settings": "Configurações de Domínio", "configure-your-custom-domain": "Configure seus domínios personalizados e certificados SSL", "custom-domain": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "ssl-certificate": "Certificado SSL", "auto-renew-ssl-certificates": "Renovar automaticamente os certificados SSL", "auto-renew-before-expiry": "Renovar automaticamente os certificados SSL antes do vencimento", "force-https": "Forçar HTTPS", "redirect-all-http-traffic": "Redirecionar todo o tráfego HTTP para HTTPS", "active-domain": "<PERSON><PERSON><PERSON>", "remove": "Remover"}, "EnvironmentVariables": {"environment-variables": "Variáveis de Ambiente", "configure-environment-variables": "Configure as variáveis de ambiente para seus deployments", "all-enviroments": "Todos os Ambientes", "environment-variables2": "Variáveis de Ambiente", "preview": "Pré-visualização", "development": "Desenvolvimento", "create-new": "Criar <PERSON>", "key": "Chave", "value": "Valor", "save-variable": "<PERSON><PERSON>", "you-can-also-import": "Você também pode importar variáveis de um arquivo .env:", "import-env-file": "Importar arquivo .env"}, "ProjectConfiguration": {"project-configuration": "Configuração do Projeto", "config-your-project-settings": "Configure as configurações do seu projeto e as opções de deployment", "project-url": "URL do Projeto", "framework": "Framework", "repo": "Repositório", "branch": "Branch", "main": "main", "development": "desenvolvimento", "staging": "staging"}}, "identity": {"unverified": {"title": "Verificação de Identidade", "description": "Verifique sua identidade para usar o Biela.dev", "subtitle": "Processo de Verificação Seguro", "processServers": "<PERSON><PERSON> as informações do cartão são armazenadas com segurança nos servidores do Stripe, não nos servidores do Biela", "processCharge": "Seu cartão não será cobrado sem o seu consentimento explícito para uma assinatura", "processBenefits": "O Biela.dev é completamente gratuito para contas verificadas até 15 de maio de 2025", "verifyStripe": "Verificar com cartão de crédito ou débito", "verifyStripeDescription": "Conecte seu método de pagamento para verificação", "verifyNow": "Verificar Agora"}, "verified": {"title": "Identidade Verificada", "description": "Sua identidade foi verificada com sucesso", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON>", "cardEnding": "Cartão terminado em", "updatePayment": "<PERSON><PERSON><PERSON><PERSON>", "untilDate": "Até 15 de maio de 2025", "freeAccess": "<PERSON><PERSON>", "freeAccessDescription": "Desfrute de acesso completo ao Biela.dev sem custos", "secureStorage": "Armazenamento Seguro", "secureStorageDescription": "As informações do seu cartão são armazenadas com segurança nos servidores da Stripe, não nos servidores da Biela. Seu cartão não será cobrado sem o seu consentimento explícito para uma assinatura.", "subscriptionAvailable": "As assinaturas estarão disponíveis a partir de 15 de maio de 2025."}, "connectingToStripe": "Conectando ao Stripe..."}, "tabs": {"billing": "Faturamento", "profile": "Perfil", "deployment": "Deployment", "identity": "Identidade"}}, "help": {"title": "Como podemos ajudar?", "searchPlaceholder": "Pesquisar na documentação...", "categories": {"getting-started": {"title": "Primeiros Passos", "description": "Aprenda o básico sobre como usar o Biela.dev", "articles": ["<PERSON><PERSON><PERSON> <PERSON> Início <PERSON>", "Visão Geral da Plataforma", "<PERSON><PERSON><PERSON>iro Projeto", "Entendendo o Desenvolvimento com IA"]}, "ai-development": {"title": "Desenvolvimento com IA", "description": "Domine o desenvolvimento impulsionado por IA", "articles": ["<PERSON><PERSON><PERSON><PERSON><PERSON> prompts eficazes", "Melhores práticas para geração de código", "Dicas para depurar a IA", "Recursos avançados de IA"]}, "project-management": {"title": "Gestão de Projetos", "description": "Organize e gerencie seus projetos", "articles": ["Estrutura do Projeto", "Colaboração em Equipe", "Controle de Versões", "Opções de Deployment"]}}, "channels": {"docs": {"name": "Documentação", "description": "Guias completos e referências de API"}, "community": {"name": "Comunidade", "description": "Conecte-se com outros desenvolvedores"}, "github": {"name": "GitHub", "description": "Reporte problemas e contribua"}}, "support": {"title": "Ainda precisa de ajuda?", "description": "Nossa equipe de suporte está disponível 24/7 para ajudar você com qualquer dúvida ou problema que tiver.", "button": "Entrar em contato com o suporte"}}, "getStarted": {"title": "Descubra como funciona o Biela.dev", "description": "Crie seu aplicativo em minutos com o Biela.dev. Nossa IA automatiza todo o processo de desenvolvimento, desde a configuração até o deployment. Veja como nossa IA constrói seu app sem esforço!", "features": {"docs": {"title": "Documentação para Desenvolvedores", "description": "Aprenda a usar o Biela.dev com guias fáceis de seguir, dicas e melhores práticas. Perfeito para iniciantes e desenvolvedores experientes!", "cta": "Explorar Documentação para Desenvolvedores"}, "support": {"title": "Feedback e Suporte", "description": "Precisa de ajuda ou tem um feedback? Entre em contato com o suporte e ajude a melhorar o Biela.dev!", "cta": "<PERSON><PERSON><PERSON>"}, "platform": {"title": "Recursos da Plataforma", "description": "Descubra as poderosas ferramentas que o Biela.dev oferece para ajudar você a criar sites e aplicativos sem esforço. Deixe a IA codificar para você!", "cta": "<PERSON>p<PERSON><PERSON>"}}, "video": {"title": "Vídeo de Início <PERSON>", "description": "Veja como o Biela.dev constrói para você – criação de apps e sites sem esforço!", "cta": "Assist<PERSON>"}, "guide": {"title": "<PERSON><PERSON><PERSON> <PERSON> Início <PERSON>", "steps": {"setup": {"title": "Configure seu projeto instantaneamente", "description": "Prepare seu ambiente de desenvolvimento em segundos"}, "generate": {"title": "Gere código full-stack com IA", "description": "Deixe a IA escrever um código pronto para produção para você"}, "features": {"title": "Geração instantânea de funcionalidades", "description": "Adicione funcionalidades complexas com comandos simples"}, "editor": {"title": "Editor No-Code & Low-Code", "description": "Modifique seu app de forma visual ou por meio do código"}, "optimize": {"title": "Otimize e teste em tempo real", "description": "Certifique-se de que seu app funcione perfeitamente"}, "deploy": {"title": "Realize o deployment com um clique", "description": "Coloque seu app online instantaneamente com o deployment automático"}}}, "faq": {"title": "Perguntas Frequentes", "questions": {"what": {"question": "O que é o Biela.dev?", "answer": "O Biela.dev é uma plataforma alimentada por IA que ajuda você a criar sites e aplicativos – mesmo se você não souber programar. Ela automatiza todo o processo de desenvolvimento, desde a escrita do código até o deployment."}, "experience": {"question": "Preciso ter experiência em programação para usar o Biela.dev?", "answer": "Não! O Biela.dev foi projetado tanto para iniciantes quanto para desenvolvedores experientes. Você pode construir com o auxílio da IA ou personalizar o código gerado conforme necessário."}, "projects": {"question": "Que tipos de projetos posso criar?", "answer": "Você pode criar sites, web apps, aplicativos móveis, plataformas SaaS, lojas de e-commerce, dashboards administrativos e muito mais."}, "edit": {"question": "Posso editar o código gerado pelo Biela.dev?", "answer": "Sim! O Biela.dev permite que você personalize o código gerado pela IA ou utilize o editor no-code/low-code para fazer modificações facilmente."}, "deployment": {"question": "Como funciona o deployment?", "answer": "Com um clique, o Biela.dev realiza o deployment do seu projeto, colocando-o online e pronto para uso. Não é necessária configuração manual do servidor!"}, "pricing": {"question": "O Biela.dev é gratuito?", "answer": "O Biela.dev oferece um plano gratuito com funcionalidades básicas. Para obter ferramentas e recursos mais avançados, você pode fazer o upgrade para um plano premium."}, "integrations": {"question": "Posso integrar ferramentas ou bancos de dados de terceiros?", "answer": "Sim! O Biela.dev suporta integrações com bancos de dados populares (MongoDB, Firebase, Supabase) e APIs de terceiros."}, "help": {"question": "Onde posso obter ajuda se eu ficar travado?", "answer": "Você pode consultar nossa Documentação para Desenvolvedores, o Guia de Início Rápido ou entrar em contato com o Suporte através do nosso centro de ajuda."}}}, "cta": {"title": "Pronto para começar?", "description": "Crie seu primeiro projeto com o Biela.dev e experimente o futuro do desenvolvimento.", "button": "Criar Novo Projeto"}}, "confirmDeleteProject": "Confirmar exclusão do projeto", "confirmRemoveFromFolder": "Confirmar remoção da pasta", "deleteProjectWarning": "Tem certeza de que deseja excluir permanentemente {{projectName}}?", "removeFromFolderWarning": "Tem certeza de que deseja remover {{projectName}} de {{folderName}}?", "confirm": "Confirmar", "confirmDeleteFolder": "Confirmar exclus<PERSON> da pasta", "deleteFolderWarning": "Tem certeza de que deseja excluir {{folderName}}? Esta ação não pode ser desfeita.", "folderDeletedSuccessfully": "Pasta excluída com sucesso", "downloadChat": "Baixar chat", "inactiveTitle": "Esta aba está inativa", "inactiveDescription": "Clique no botão abaixo para ativar esta aba e continuar usando o aplicativo.", "inactiveButton": "Usar esta aba", "suggestions": {"weatherDashboard": "Crie um painel meteorológico", "ecommercePlatform": "Construa uma plataforma de comércio eletrônico", "socialMediaApp": "Projete um aplicativo de rede social", "portfolioWebsite": "Gere um site de portfólio", "taskManagementApp": "Crie um aplicativo de gerenciamento de tarefas", "fitnessTracker": "Construa um monitor de fitness", "recipeSharingPlatform": "Projete uma plataforma para compartilhamento de receitas", "travelBookingSite": "Crie um site de reservas de viagens", "learningPlatform": "Construa uma plataforma de aprendizado", "musicStreamingApp": "Projete um aplicativo de streaming de música", "realEstateListing": "Crie uma listagem imobiliária", "jobBoard": "Crie um quadro de empregos"}, "pleaseWait": "Por favor, aguarde...", "projectsInAll": "Projetos em Todos", "projectsInCurrentFolder": "Projetos em {{folderName}}", "createYourFirstProject": "Crie seu primeiro projeto", "startCreateNewProjectDescription": "Comece a construir algo incrível. Seus projetos serão exibidos aqui assim que você os criar.", "createProjectBtn": "Novo Projeto", "publishedToHackathon": "Publicado no Hackathon", "publishToHackathon": "Publicar no Hackathon", "refreshSubmission": "<PERSON><PERSON><PERSON><PERSON> envio", "hackathonInformation": "Informações do Hackathon", "selectFolder": "Escolha uma pasta", "folder": "Pasta", "selectAFolder": "Selecione uma pasta", "thisProject": "este projeto", "projectDeletedSuccessfully": "Projeto excluído com sucesso", "projectRemovedFromFolder": "Projeto removido da pasta", "unnamedProject": "Projeto sem nome", "permanentDeletion": "Exclusão permanente", "removeFromFolder": "Remover de {{folderName}}", "verifiedAccount": "Conta verificada", "hasSubmittedAMinimumOfOneProject": "Enviou pelo menos um projeto", "haveAtLeastActiveReferrals": "<PERSON>ha pelo menos {{number}} referê<PERSON>s ativas", "GoToAffiliateDashBoard": "Ir para o painel de afiliados", "LikeOneProjectThatBelongsToAnotherUser": "Gostar de um projeto que pertence a outro usuário", "GoToHackathonPage": "Ir para a página do Hackathon", "VibeCodingHackathonStatus": "Status do Hackathon Vibe Coding", "ShowcaseYourBestWork": "<PERSON>re seu melhor trabalho", "submissions": "<PERSON><PERSON><PERSON>", "qualifyConditions": "Condições de qualificação", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collapseQualifyConditions": "Recolher condições de qualificação", "expandQualifyConditions": "Expandir condições de qualificação", "basicParticipation": "Participação básica", "complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "incomplete": "Incompleto", "forTop3Places": "Para os 3 primeiros lugares", "deleteSubmission": "Excluir envio", "view": "Visualizar", "submitMoreProjectsToIncreaseYourChangesOfWining": "Envie mais projetos para aumentar suas chances de ganhar!", "removeFromHackathon": "Remover do Hackathon?", "removeFromHackathonDescription": "Tem certeza de que deseja remover <project>{{project}}</project> do Hackathon Vibe Coding? Esta ação não pode ser desfeita.", "cardVerificationRequired": "Verificação de cartão necessária", "pleaseVerifyCard": "Por favor, verifique seu cartão para continuar", "unlockFeaturesMessage": "Para desbloquear o acesso total a todos os recursos da plataforma, solicitamos uma verificação rápida do cartão. Este processo é completamente seguro e garante uma experiência tranquila no Biela.dev. Nenhuma cobrança será feita no seu cartão durante a verificação.", "freeVerificationNotice": "Benefícios da Verificação", "accessToAllFeatures": "Acesso total aos recursos do Biela.dev.", "enhancedFunctionality": "Desempenho aprimorado da plataforma.", "quickSecureVerification": "Processo de verificação seguro e criptografado.", "noCharges": "*Sem cobranças ou taxas ocultas", "verificationOnly": " — apenas verificação.", "verifyNow": "Verificar agora", "DropFilesToUpload": "Solte os arquivos para enviar", "projectInfo": {"information": "Informações do Projeto", "type": "Tipo de Projeto", "complexity": "Complexidade", "components": "Componentes", "features": "Funcionalidades", "confidenceScore": "Índice de Confiança", "estimationUncertainty": "Incerteza da Estimativa", "keyTechnologies": "Tecnologias-Chave"}, "projectMetrics": {"teamComposition": "Composição da Equipe", "hoursBreakdown": "Distribuição de Horas", "timeToMarket": "Tempo para o Mercado", "maintenance": "Manutenção", "aiPowered": "Impulsionado por IA", "developerLevel": "Nível do Desenvolvedor", "nextGenAI": "IA de Próxima Geração", "keyBenefits": "Principa<PERSON>", "instantDevelopment": "Desenvolvimento Instantâneo", "noMaintenanceCosts": "Sem Custos de Manutenção", "highConfidence": "Alta Confiança", "productionReadyCode": "Código Pronto para Produção", "immediate": "Imediato", "uncertainty": "Incerteza", "minimal": "<PERSON><PERSON><PERSON>"}, "loadingMessages": {"publish": ["Obtendo informações do projeto...", "Capturando imagem...", "Gerando resumo e descrição..."], "refresh": ["Recuperando dados atuais da submissão...", "Atualizando imagem do projeto...", "Atualizando detalhes do projeto..."]}, "errorMessages": {"publish": "Não foi possível publicar \"{{projectName}}\" no hackathon. Ocorreu um problema ao processar sua solicitação.", "refresh": "Não foi possível atualizar a submissão de \"{{projectName}}\". O servidor não conseguiu atualizar as informações do seu projeto."}, "actions": {"refresh": {"title": "Atualizando Submissão", "successMessage": "A submissão do seu projeto foi atualizada com sucesso.", "buttonText": "Ver Submissão Atualizada"}, "publish": {"title": "Publicando no Hackathon", "successMessage": "Seu projeto foi enviado para o hackathon.", "buttonText": "Ver na Página do Hackathon"}}, "SupabaseConnect": "Supabase", "SupabaseDashboard": "Supabase", "RestoreApp": "Restaurar", "SaveApp": "<PERSON><PERSON>", "ForkChat": "Bifurcar", "BielaTerminal": "Terminal Biela", "UnitTesting": "Teste unitário", "InstallDependencies": "Instalar dependências", "InstallDependenciesDescription": "Instala todos os pacotes necessários para o projeto usando", "BuildProject": "Construir projeto", "BuildProjectDescription": "Compila e otimiza o projeto para produção usando", "StartDevelopment": "Iniciar <PERSON>", "StartDevelopmentDescription": "Inicia o servidor de desenvolvimento para visualização ao vivo usando", "NoProjectFound": "Nenhum projeto encontrado com esse nome.", "NoProjectFoundDescription": " Verifique se há erros de digitação e tente novamente.", "ContextLimitReached": "Limite de contexto atingido", "ClaudeContextDescription1": "Você atingiu a capacidade de contexto para este projeto com o Claude. Não se preocupe — podemos continuar mudando para um modelo Gemini com uma janela de contexto significativamente maior.", "ClaudeContextDescription2": "Os modelos Gemini oferecem até 5 vezes mais capacidade de contexto, permitindo que você mantenha todo o seu código, histórico de conversa e continue construindo seu projeto sem interrupções.", "SelectModelToContinue": "Selecione um modelo para continuar:", "Performance": "<PERSON><PERSON><PERSON><PERSON>", "UpgradePlan": "um plano premium", "PremiumFeatureRequired": "Recurso premium necessário", "LargeContextUpgradeInfo": "Modelos com janelas de contexto grandes requerem um nível de assinatura mais alto. Faça o upgrade para desbloqueá-los.", "PremiumModelsAvailable": "Modelos premium disponíveis com upgrade:", "UpgradeTooltip": "Desbloqueie ao fazer upgrade para ", "UpgradeTooltipSuffix": "pacote", "UpgradeToContinue": "Faça o upgrade para continuar", "PremiumBadge": "Premium", "AlternativeLimitExplanation": "Parece que a IA atingiu o limite de processamento para este projeto. A maior parte do espaço está sendo usada pelos arquivos importados, não pela conversa.", "SuggestedSolutions": "Soluções sugeridas:", "ReimportProject": "Reimportar como novo projeto", "ReimportProjectDescription": "<PERSON>so limpará o histórico da conversa e liberará espaço de contexto, mantendo seus arquivos.", "BreakIntoProjects": "Dividir em vários projetos", "BreakIntoProjectsDescription": "Divida seu trabalho em componentes menores que podem ser desenvolvidos separadamente.", "ExportWork": "Exportar trabalho concluído", "ExportWorkDescription": "Baixe e arquive os arquivos concluídos para liberar espaço de contexto.", "AlternativeContextNote": "Para aproveitar ao máximo o contexto disponível, considere remover arquivos ou bibliotecas não utilizados e focar nos arquivos essenciais necessários para a fase atual de desenvolvimento.", "ContinueWithSelectedModel": "Continuar com o modelo selecionado", "Close": "<PERSON><PERSON><PERSON>", "AIModel": "Modelo de IA", "Active": "Ativo", "Stats": "Estatísticas", "Cost": "Custo", "ExtendedThinkingDisabledForModel": "Não disponível para este modelo", "ExtendedThinkingAlwaysOn": "Sempre ativo com este modelo", "limitReached": "Você atingiu seu limite!", "deleteProjectsSupabase": "Exclua alguns projetos ou aumente seu limite no Supabase.", "goTo": "<PERSON>r <PERSON>", "clickProjectSettings": " clique no projeto, Configurações do projeto, role até o final e clique", "delete": "Excluir", "retrying": "Repetindo…", "retryConnection": "Tentar reconexão", "RegisterPageTitle": "Registrar – biela.dev", "RegisterPageDescription": "Crie sua conta na biela.dev e desbloqueie todos os recursos.", "SignUpHeading": "Cadastre-se", "AlreadyLoggedInRedirectHome": "Você já está logado! Redirecionando para a página inicial...", "PasswordsMismatch": "As senhas não coincidem.", "FirstNameRequired": "Nome é obrigatório", "LastNameRequired": "Sobrenome é obrigatório", "UsernameRequired": "Nome de usuário é obrigatório", "EmailRequired": "E-mail é obrigatório", "EmailInvalid": "Por favor, insira um endereço de e-mail válido", "TooManyRequests": "Solicitações em excesso, tente novamente mais tarde", "SomethingWentWrongMessage": "<PERSON>go deu errado, tente novamente mais tarde", "PasswordRequired": "Senha é obrigatória", "ConfirmPasswordRequired": "Por favor, confirme sua senha", "AcceptTermsRequired": "Você deve aceitar os Termos de Serviço e a Política de Privacidade", "CaptchaRequired": "Por favor, complete o CAPTCHA", "RegistrationFailed": "Falha no registro", "EmailConfirmationSent": "E-mail de confirmação enviado! Por favor, confirme seu e-mail e depois faça login.", "RegistrationServerError": "<PERSON>alha no registro (o servidor retornou false).", "SomethingWentWrong": "<PERSON>go deu errado", "CheckEmailHeading": "Verifique seu e-mail para confirmar o registro", "CheckEmailDescription": "Enviamos um e-mail com um link de confirmação.", "GoToHomepage": "Ir para a página inicial", "ReferralCodeOptional": "Código de indicação (opcional)", "EnterReferralCode": "Insira um código de indicação se foi convidado por um usuário existente.", "PasswordPlaceholder": "<PERSON><PERSON>", "ConfirmPasswordPlaceholder": "Confirmar <PERSON><PERSON><PERSON>", "CreateAccount": "C<PERSON><PERSON> conta", "AlreadyHaveAccountPrompt": "Já tem uma conta?", "Login": "Entrar", "AcceptTermsPrefix": "Eu concordo com os", "TermsOfService": "Termos de Serviço", "AndSeparator": "e", "PrivacyPolicy": "Política de Privacidade", "LoginPageTitle": "Entrar – biela.dev", "LoginPageDescription": "Acesse sua conta ou entre na biela.dev para usar todos os recursos.", "LogInHeading": "Entrar", "EmailOrUsernamePlaceholder": "E-mail / Nome de usuário", "ForgotPassword?": "Esque<PERSON>u a senha?", "LoginToProfile": "Entrar no perfil", "UserNotConfirmed": "Conta de usuário não confirmada", "ConfirmEmailNotice": "Você precisa confirmar seu e-mail para ativar a conta.", "ResendConfirmationEmail": "Reenviar e-mail de confirmação", "ResendConfirmationSuccess": "E-mail de verificação reenviado! Por favor, verifique sua caixa de entrada.", "ResendConfirmationError": "Falha ao reenviar e-mail de confirmação.", "LoginSuccess": "Login realizado com sucesso! Redirecionando...", "LoginFailed": "Falha no login", "LoginWithGoogle": "Entrar com Google", "LoginWithGitHub": "Entrar com GitHub", "SignUpWithGoogle": "Inscreva-se com o Google", "SignUpWithGitHub": "Inscreva-se com o GitHub", "Or": "OU", "NoAccountPrompt": "Ainda não tem uma conta?", "SignMeUp": "Quero me cadastrar", "ForgotPasswordPageTitle": "Esqueceu a senha – biela.dev", "ForgotPasswordPageDescription": "Redefina a senha da sua conta biela.dev e recupere o acesso.", "BackToLogin": "Voltar ao login", "ForgotPasswordHeading": "Esqueceu a senha", "ForgotPasswordDescription": "Digite seu e-mail e enviaremos um link de verificação para redefinir sua senha.", "VerificationLinkSent": "Link de verificação enviado! Verifique seu e-mail.", "EnterYourEmailPlaceholder": "Digite seu e-mail", "Sending": "Enviando...", "SendVerificationCode": "Enviar código de verificação", "SendVerificationLink": "Enviar link de verificação", "InvalidConfirmationLink": "Link de confirmação inválido", "Back": "Voltar", "ResetPassword": "<PERSON><PERSON><PERSON><PERSON>", "ResetPasswordDescription": "Crie uma nova senha para sua conta", "NewPasswordPlaceholder": "Nova senha", "ConfirmNewPasswordPlaceholder": "Confirmar nova senha", "ResetPasswordButton": "<PERSON><PERSON><PERSON><PERSON>", "PasswordRequirements": "A senha deve ter pelo menos 8 caracteres, incluindo uma letra mai<PERSON>, uma minúscula, um número e um caractere especial.", "PasswordUpdatedSuccess": "Senha atualizada com sucesso!", "affiliateDashboard": "Painel de a<PERSON>liados", "userDashboard": "Painel do usuário", "returnToAffiliateDashboard": "Voltar ao painel de afiliados", "returnToUserDashboard": "Voltar ao painel do usuário", "myProfile": "<PERSON><PERSON> perfil", "viewAndEditYourProfile": "Ver e editar seu perfil", "billing": "Faturamento", "manageYourBillingInformation": "Gerenciar suas informações de faturamento", "logout": "<PERSON><PERSON>", "logoutDescription": "<PERSON>r da sua conta", "SupabaseNotAvailable": "O Supabase não está disponível no momento. Por favor, tente novamente mais tarde.", "projectActions": {"invalidSlug": "Slug do projeto inválido.", "transferLimit": "Você já transferiu esta versão do projeto para este usuário. Faça alterações para transferir novamente.", "downloadSuccess": "Projeto baixado com sucesso!", "downloadError": "Falha ao baixar o projeto.", "exportSuccess": "Chat exportado! Verifique sua pasta de downloads.", "exportError": "Falha ao exportar o chat.", "duplicateSuccess": "Chat duplicado com sucesso!", "duplicateError": "<PERSON>alha ao duplicar o chat."}, "enter_new_phone_number": "Insira o novo número", "enter_new_phone_number_below": "Digite seu novo número de telefone abaixo:", "new_phone_placeholder": "Novo número de telefone", "enter_otp_code": "Digite o código OTP", "confirm_phone_message": "Para usar sua conta, é necessário confirmar seu número. Insira o código enviado para ({{phone}}).", "wrong_phone": "Número errado?", "resend_sms": "Reenviar SMS", "submit": "Enviar", "tokensAvailable": "tokens disponíveis", "sectionTitle": "Gerenciamento de Domínio", "addDomainButton": "Adicionar <PERSON>", "connectCustomDomainTitle": "Conectar um Nome de Domínio Personalizado", "disclaimer": "Aviso:", "disclaimerText": "Para verificar com sucesso, você precisa configurar corretamente todas as regras de DNS acima", "domainInputDescription": "Insira o nome do domínio que deseja conectar a este projeto.", "domainLabel": "Nome de Domínio", "domainPlaceholder": "example.com", "cancelButton": "<PERSON><PERSON><PERSON>", "continueButton": "Adicionar <PERSON>", "deployingText": "Implantando...", "addingText": "Adicionando...", "verifyButtonText": "Verificar", "configureDnsTitle": "Configurar Registros DNS", "configureDnsDescription": "Adicione os seguintes registros DNS ao seu domínio para verificar a propriedade e conectá-lo a este projeto.", "tableHeaderType": "Tipo", "tableHeaderName": "Nome", "tableHeaderValue": "Valor", "note": "Observação:", "noteText": "As alterações de DNS podem levar até 48 horas para se propagar. No entanto, muitas vezes entram em vigor em poucos minutos ou horas.", "backButton": "Voltar", "showDnsButton": "Mostrar Configurações de DNS", "hideDnsButton": "Ocultar Configurações de DNS", "removeButton": "Remover", "dnsSettingsTitle": "Configurações de DNS do Domínio", "removeDomainConfirmTitle": "Remover Nome de Domínio", "removeConfirmationText": "Tem certeza de que deseja remover o nome de domínio ", "importantCleanupTitle": "Limpeza Importante de DNS", "cleanupDescription": "Após remover este nome de domínio do seu projeto, lembre-se também de remover os registros DNS criados durante a configuração. <PERSON><PERSON> ajuda a manter uma configuração de DNS limpa e evita possíveis conflitos futuros.", "confirmRemoveButton": "Remover Nome de Domínio", "customConfigTitle": "Configuração de Domínio Personalizado", "customConfigDescription": "Conecte seus próprios nomes de domínio a este projeto. Seu projeto continuará acessível através do domínio padrão da Biela, mas os domínios personalizados oferecem uma experiência de marca profissional para seus usuários.", "defaultLabel": "Padrão", "statusActive": "Ativo", "statusPending": "Pendente", "lastVerifiedText": "Última verificação agora mesmo", "errorInvalidDomain": "Por favor, insira um nome de domínio válido (ex: example.com)", "errorDuplicateDomain": "Este nome de domínio já está conectado ao seu projeto", "errorAddFail": "Falha ao adicionar o nome de domínio.", "successAdd": "Nome de domínio adicionado com sucesso! Seu domínio foi adicionado a este projeto.", "benefitsTitle": "Benefícios do Domínio", "benefitSecurityTitle": "<PERSON><PERSON><PERSON><PERSON>", "benefitSecurityDesc": "Todos os domínios personalizados são automaticamente protegidos com certificados SSL.", "benefitPerformanceTitle": "<PERSON><PERSON><PERSON><PERSON>", "benefitPerformanceDesc": "A CDN global garante carregamento rápido do seu projeto para usuários em todo o mundo.", "benefitBrandingTitle": "Branding Profissional", "benefitBrandingDesc": "Use seu próprio domínio para uma experiência de marca consistente.", "benefitAnalyticsTitle": "Integração com Análises", "benefitAnalyticsDesc": "Domínios personalizados funcionam perfeitamente com plataformas de análise.", "meta": {"index": {"title": "biela.dev | Construtor de Web e Apps com IA – Construa com Prompts", "description": "Transforme suas ideias em sites ou aplicativos funcionais com biela.dev. Use prompts orientados por IA para criar produtos digitais personalizados sem esforço."}, "login": {"title": "Acesse Sua Conta biela.dev", "description": "Acesse seu painel biela.dev para gerenciar e construir seus projetos gerados por IA."}, "register": {"title": "Cadastre-se no biela.dev – Comece a Construir com IA", "description": "Crie sua conta biela.dev para começar a construir sites e aplicativos usando prompts alimentados por IA."}, "dashboard": {"title": "Seu <PERSON>el de Projetos – biela.dev", "description": "Gerencie seus sites e aplicativos construídos com IA, edite projetos ao vivo e acompanhe seu histórico de construção, tudo em um só lugar."}, "profile": {"title": "Profilul tău – biela.dev", "description": "Vizualizează și actualizează detaliile contului tău biela.dev, gestionează preferințele și personalizează experiența ta de dezvoltare AI."}, "billing": {"title": "Cobrança – Gerencie seu plano com segurança no biela.dev", "description": "Acesse as configurações de cobrança para gerenciar sua assinatura, atualizar métodos de pagamento e manter o controle do seu plano no biela.dev."}}, "transferProject": "Compartilhar uma c<PERSON>pia", "transferSecurityNoteDescription": "O destinatário receberá acesso total a uma cópia deste projeto e a todos os seus recursos associados.", "transferProjectDescription": "Digite o nome de usuário ou e-mail da pessoa para quem deseja transferir uma cópia deste projeto.", "transferProjectLabel": "Nome de usuário ou e-mail", "transferProjectPlaceholder": "johns<PERSON> ou <EMAIL>", "transferButton": "Transferir", "transferSecurityNote": "Nota de segurança:", "dontHavePermisionToTransfer": "Você não tem permissão para transferir este projeto", "transferProjectUserNotFound": "<PERSON><PERSON><PERSON><PERSON> {{ user }} não encontrado!", "transferErrorOwnAccount": "Você não pode transferir um projeto para sua própria conta.", "transferError": "Erro ao transferir o projeto", "transferSuccess": "Transferido com sucesso para {{ user }}", "enterValidEmailUsername": "Por favor, insira um nome de usuário ou e-mail", "enterMinValidEmailUsername": "Insira um nome de usuário v<PERSON> (mínimo 3 caracteres) ou endereço de e-mail", "youWillStillHaveAccess": "Você ainda terá acesso ao projeto original", "newChangesWillNotAffect": "As novas alterações não afetarão o projeto do outro usuário", "ProjectInformationNotLoaded": "As informações do projeto não puderam ser carregadas.", "projectInfoTitle": "Informações do Projeto", "generalInformationProject": "Informações gerais sobre o seu projeto.", "rename": "Renomear", "lastlySavedAt": "Última gravação em:", "noSavedAppVersion": "Não há versão do aplicativo salva.", "Owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TechStack": "Pilha de tecnologia", "FeatureCount": "Quantidade de funcionalidades", "UniqueComponentCount": "Quantidade de componentes únicos", "inviteCollaborator": {"title": "CONVIDAR COLABORADOR", "message": "Você está prestes a convidar {{email}} para colaborar neste projeto.", "action": "Enviar convite"}, "removeCollaborator": {"title": "REMOVER COLABORADOR", "message": "Você está prestes a remover {{email}} deste projeto.", "action": "Remover"}, "database": {"title": "Conexões de Banco de Dados", "fields": {"created": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON>", "region": "Região", "version": "Vers<PERSON>"}, "button": {"connected": "Conectado", "connect": "Conectar", "notConnected": "Não conectado"}}, "status": {"Online": "Online", "Degraded": "Degradado", "Restoring": "Restaurando", "Restored": "Restaurado", "Creating": "<PERSON><PERSON><PERSON>", "Provisioning": "Provisionando", "Coming Up": "Inicializando", "Deleting": "Excluindo", "Deleted": "Excluído", "Pausing": "Pausando", "Paused": "<PERSON><PERSON><PERSON>", "Inactive": "Inativo", "Suspended": "Suspenso", "Resuming": "<PERSON><PERSON><PERSON><PERSON>", "Updating": "Atualizando", "Migrating": "<PERSON><PERSON><PERSON>", "Maintenance": "Manutenção", "Restarting": "<PERSON><PERSON><PERSON><PERSON>", "Backup in progress": "Backup em andamento", "Restore in progress": "Restauração em andamento", "Failed": "Fal<PERSON>", "Unknown": "Desconhecido", "Loading...": "Carregando...", "Not Found": "Não encontrado", "Error": "Erro"}, "titleChat": "Chat de {{typeCapitalized}}", "titleMessage": "Mensagem de {{typeCapitalized}}", "dialogDescriptionText": "<PERSON>oc<PERSON> está prestes a {{type}} {{description}}.", "confirmText": "Tem certeza de que deseja {{type}} este chat?", "deleteButton": "Excluir", "deleteInfinitive": "excluir", "deleteNoun": "exclusão", "duplicateButton": "Duplicar", "duplicateInfinitive": "duplicar", "duplicateNoun": "duplicação", "downloadButton": "Baixar", "downloadInfinitive": "baixar", "downloadNoun": "download", "exportButton": "Exportar", "exportInfinitive": "exportar", "exportNoun": "exportação", "forkButton": "<PERSON><PERSON>", "forkInfinitive": "ramificar", "forkNoun": "ramificação", "rollbackButton": "<PERSON><PERSON><PERSON>", "rollbackInfinitive": "reverter", "rollbackNoun": "reversão", "saveButton": "<PERSON><PERSON>", "saveInfinitive": "salvar", "saveNoun": "salvamento", "restoreButton": "Restaurar", "restoreInfinitive": "restaurar", "restoreNoun": "restauração", "loadingOverlayChat": "Aguarde enquanto {{type}} o chat.", "loadingOverlayMessages": "Aguarde enquanto {{type}} as mensagens.", "exportProjectChat": "Exportar chat do projeto", "downloadProjectFiles": "Baixar arquivos do projeto", "pleaseSelectCommit": "Selecione um commit para restaurar:", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "savingApp": "Salvando...", "confirmationDelete": "Excluir", "confirmationDuplicate": "Duplicar", "confirmationDownload": "Baixar", "confirmationExport": "Exportar", "confirmationFork": "<PERSON><PERSON>", "confirmationRollback": "<PERSON><PERSON><PERSON>", "confirmationSave": "<PERSON><PERSON>", "confirmationRestore": "Restaurar", "dialogTitleChat": "{{typeCapitalized}} este chat?", "dialogTitleData": "{{typeCapitalized}} seus dados?", "dialogLoadingChat": "{{typeCapitalized}} o chat...", "dialogLoadingData": "{{typeCapitalized}} as mensagens...", "dialogDescription": "Voc<PERSON> está prestes a {{type}}: {{description}}", "dialogConfirm": "Tem certeza de que deseja {{type}}?", "buttonCancel": "<PERSON><PERSON><PERSON>", "previousAppVersion": "uma versão anterior do aplicativo", "CreatingDatabase": "Criando banco de dados...", "DatabaseDashboard": "<PERSON><PERSON>", "ConnectDatabase": "Conectar", "DatabaseConnectionOptions": "Opções de conexão", "LoadingYourSupabaseProjects": "Carregando projetos...", "NoSupabaseProjectsFound": "Nenhum projeto encontrado. Conecte-se primeiro.", "DatabaseOptions": "Opções", "DisconnectDatabase": "Desconectar banco", "OpenSupabaseDashboard": "<PERSON><PERSON><PERSON>el", "LinkExistingProject": "Vincular projeto", "IncludeDataWhenDuplicating": "Incluir dados ao duplicar", "DuplicateProject": "Duplicar projeto", "CreateNewSupabaseDB": "Criar novo BD Supabase", "DisconnectSupabaseDB": "Desconectar BD do Supabase", "UseExistingSupabaseDB": "Usar BD Supabase existente", "SharedFrom": "Compart<PERSON><PERSON><PERSON>", "TokensExhausted": "Você ficou sem tokens", "TokensExhaustedDescription": "Para continuar o seu projeto, escolha uma opção:", "upgradePlan": "Atualizar meu plano", "buyMoreTokens": "Comprar mais tokens", "seeScreenshot": "Ver captura de tela"}