{"userInformation": "Информация о пользователе", "firstName": "Имя", "lastName": "Фамилия", "username": "Имя пользователя", "phone": "Телефон", "preferredLanguage": "Предпочитаемый язык", "billingInformation": "Информация о платежах", "addressLine1": "Адре<PERSON>, строка 1", "addressLine2": "Ад<PERSON><PERSON><PERSON>, строка 2", "city": "Город", "zipCode": "Почтовый индекс", "referralCode": "Реферальный код", "referralLink": "Реферальная ссылка", "referral": "Реферал", "location": "Местоположение", "site": "Сайт", "email": "Электронная почта", "contactInfo": "Контактная информация", "websiteUrl": "URL сайта", "accountDeletion": "Удаление аккаунта", "deleteAccount": "Удалить аккаунт", "deleteAccountWarning": "При удалении вы удалите все свои проекты и личные данные. Также вы потеряете контроль над проектами в ваших организациях.", "connectAccount": "Подключите свою учетную запись {{platform}}", "connectNow": "Подключить сейчас", "connected": "Подключено", "personalWebsite": "<PERSON>ичный сайт", "facebook": "Facebook", "twitterX": "Twitter/X", "linkedin": "LinkedIn", "youtube": "YouTube", "instagram": "Instagram", "tiktok": "TikTok", "Country": "Страна", "ChangePassword": "Изменить пароль", "Updating": "Обновление...", "Save": "Сохранить", "Edit": "Редактировать", "CurrentPassword": "Текущий пароль", "NewPassword": "Новый пароль", "RepeatNewPassword": "Повторите новый пароль", "AllPasswordFieldsAreRequired": "Все поля пароля обязательны для заполнения", "NewPasswordsDoNotMatch": "Новые пароли не совпадают", "PasswordMustBeAtLeastCharactersLong": "Пароль должен содержать не менее {{minLength}} символов", "UserNotFound": "Пользователь не найден", "CurrentPasswordIsIncorrect": "Текущий пароль неверен", "PasswordUpdated": "Ваш пароль успешно обновлен!", "ErrorWhileUpdatingPassword": "Ошибка при обновлении пароля", "Success": "Успех", "Error": "Ошибка", "updateAccount": "Ваш аккаунт был успешно обновлен!", "failedUpdateAccount": "Что-то пошло не так. Пожалуйста, попробуйте еще раз.", "Close": "Закрыть", "firstNameRequired": "Имя обязательно", "lastNameRequired": "Фамилия обязательна", "usernameRequired": "Имя пользователя обязательно", "phoneRequired": "Требуется номер телефона", "address1Required": "Адрес обязателен", "cityRequired": "Город обязателен", "zipCodeRequired": "Почтовый индекс обязателен", "countryRequired": "Страна обязательна", "emailRequired": "Электронная почта обязательна", "EmailInvalid": "Пожалуйста, введите действительный адрес электронной почты", "URLInvalid": "Пожалуйста, введите действительный URL", "copyReferralCode": "Скопировать реферальный код", "copyReferralLink": "Скопировать реферальную ссылку", "purchaseDetails": "Детали покупки", "dateLabel": "Дата", "planLabel": "<PERSON><PERSON><PERSON><PERSON>", "amountLabel": "Сумма", "tokensLabel": "Токены", "whatsNext": "Что дальше?", "yourTokensAvailable": "Ваши токены теперь доступны в вашем аккаунте", "purchaseDetailsStored": "Детали покупки сохранены в вашем аккаунте", "viewPurchaseHistory": "Вы можете просмотреть историю покупок в разделе оплаты", "thankYou": "Спасибо!", "purchaseSuccessful": "Покупка прошла успешно", "startCoding": "Начать программировать", "tokenTopUpComingSoon": "Скоро пополнение токенов", "finalisingPricing": "Мы завершаем настройку цен, чтобы предложить лучшие тарифы и оптовые скидки.", "getReady": "Подготовьтесь к гибким пакетам токенов для ваших нужд в разработке.", "gotIt": "Понятно", "checkBackSoon": "Зайдите позже, чтобы купить токены", "buyExtraTokens": "Купить дополнительные токены", "paymentMethod": "Способ оплаты", "howManyTokens": "Сколько токенов вы хотите?", "placeholderEnterTokens": "Введите количество токенов...", "units": "Единицы", "availableDiscounts": "Доступные скидки", "priceLabel": "Цена:", "discountAppliedLabel": "Применённая скидка:", "at": "по", "perMillion": "за миллион токенов", "purchaseTokens": "Купить токены", "minimumPurchaseError": "Минимальная покупка — {minTokens} токенов ($1)", "minimumTokenError": "Минимальное количество токенов: {minTokens}", "maximumTokenError": "Максимальное количество токенов: {maxTokens}", "modalTitle": "Выберите план или пополните токены при необходимости", "modalSubtitle": "Выберите один из трех гибких планов или покупайте дополнительные токены в любое время, чтобы продолжать кодировать без ограничений.", "highlightExtraTokens": "дополнительные токены", "toggleMonthly": "Ежемесячно", "toggleYearly": "Ежегодно (скидка 10%)", "buttonNeedMoreTokens": "Нужно больше токенов?", "recentInvoicesTitle": "Недавние счета", "recentInvoicesDescription": "Отслеживайте историю платежей и скачивайте счета", "tableHeaderDate": "Дата", "tableHeaderDescription": "Описание", "tableHeaderAmount": "Сумма", "tableHeaderStatus": "Статус", "tableHeaderActions": "Действия", "noInvoicesFound": "Счета не найдены", "actionPreviewInvoice": "Просмотр счета", "actionDownloadInvoice": "Скачать счет", "planDescription": "Идеально для тех, кто изучает разработку, учится новому или работает над личными проектами.", "planNameBasicPlus": "Basic Plus", "planNameStarterPlus": "Starter Plus", "planNamePremiumPlus": "Premium Plus", "planPriceBasicPlus": "$100/год", "planPriceStarterPlus": "$200/год", "planPricePremiumPlus": "$300/год", "planTokensBasicPlus": "5M токенов в месяц", "planTokensStarterPlus": "25M токенов в месяц", "planTokensPremiumPlus": "80M токенов в месяц", "priceSeparator": "/", "currentPlanLabel": "Текущий план", "upgradePlanLabel": "Обновить план", "nextPage": "Следующая страница", "previousPage": "Предыдущая страница", "paginationStatus": "Показано с {{from}} по {{to}} из {{total}} результатов"}