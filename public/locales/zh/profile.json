{"userInformation": "用户信息", "firstName": "名", "lastName": "姓", "username": "用户名", "phone": "电话", "preferredLanguage": "首选语言", "billingInformation": "账单信息", "addressLine1": "地址行 1", "addressLine2": "地址行 2", "city": "城市", "zipCode": "邮政编码", "referralCode": "推荐码", "referralLink": "推荐链接", "referral": "推荐", "location": "位置", "site": "站点", "email": "电子邮件", "contactInfo": "联系信息", "websiteUrl": "网站 URL", "accountDeletion": "账户删除", "deleteAccount": "删除账户", "deleteAccountWarning": "删除账户后，您将删除所有项目和个人数据。此外，您将失去对您组织中的项目的控制。", "connectAccount": "连接您的 {{platform}} 账户", "connectNow": "立即连接", "connected": "已连接", "personalWebsite": "个人网站", "facebook": "Facebook", "twitterX": "Twitter/X", "linkedin": "LinkedIn", "youtube": "YouTube", "instagram": "Instagram", "tiktok": "TikTok", "Country": "国家", "ChangePassword": "更改密码", "Updating": "更新中...", "Save": "保存", "Edit": "编辑", "CurrentPassword": "当前密码", "NewPassword": "新密码", "RepeatNewPassword": "重复新密码", "AllPasswordFieldsAreRequired": "所有密码字段均为必填项", "NewPasswordsDoNotMatch": "新密码不匹配", "PasswordMustBeAtLeastCharactersLong": "密码必须至少包含{{minLength}}个字符", "UserNotFound": "用户未找到", "CurrentPasswordIsIncorrect": "当前密码不正确", "PasswordUpdated": "您的密码已成功更新！", "ErrorWhileUpdatingPassword": "更新密码时出错", "Success": "成功", "Error": "错误", "updateAccount": "您的账户已成功更新！", "failedUpdateAccount": "出现问题。请再试一次。", "Close": "关闭", "firstNameRequired": "名字为必填项", "lastNameRequired": "姓氏为必填项", "usernameRequired": "用户名为必填项", "phoneRequired": "电话号码为必填项", "address1Required": "地址为必填项", "cityRequired": "城市为必填项", "zipCodeRequired": "邮政编码为必填项", "countryRequired": "国家为必填项", "emailRequired": "电子邮件为必填项", "EmailInvalid": "请输入有效的电子邮件地址", "URLInvalid": "请输入有效的URL", "copyReferralCode": "复制推荐码", "copyReferralLink": "复制推荐链接", "purchaseDetails": "购买详情", "dateLabel": "日期", "planLabel": "计划", "amountLabel": "金额", "tokensLabel": "代币", "whatsNext": "接下来做什么？", "yourTokensAvailable": "您的代币现在已存入账户", "purchaseDetailsStored": "购买详情已存储到您的账户中", "viewPurchaseHistory": "您可以在账单部分查看购买记录", "thankYou": "谢谢！", "purchaseSuccessful": "购买成功", "startCoding": "开始编程", "tokenTopUpComingSoon": "代币充值即将推出", "finalisingPricing": "我们正在完善定价系统，为您提供最佳价格和批量折扣。", "getReady": "准备好使用灵活的代币套餐来满足您的开发需求。", "gotIt": "知道了", "checkBackSoon": "请稍后回来购买代币", "buyExtraTokens": "购买额外代币", "paymentMethod": "支付方式", "howManyTokens": "您想要多少代币？", "placeholderEnterTokens": "输入代币数量...", "units": "单位", "availableDiscounts": "可用折扣", "priceLabel": "价格：", "discountAppliedLabel": "已应用折扣：", "at": "以", "perMillion": "每百万代币", "purchaseTokens": "购买代币", "minimumPurchaseError": "最低购买为 {minTokens} 个代币（$1）", "minimumTokenError": "最少购买 {minTokens} 个代币", "maximumTokenError": "最多购买 {maxTokens} 个代币", "modalTitle": "选择套餐或按需充值代币", "modalSubtitle": "从我们灵活的三种套餐中选择，或随时购买额外代币，畅享无限开发。", "highlightExtraTokens": "额外代币", "toggleMonthly": "每月", "toggleYearly": "每年（节省 10%）", "buttonNeedMoreTokens": "需要更多代币？", "recentInvoicesTitle": "最近发票", "recentInvoicesDescription": "查看账单记录并下载发票", "tableHeaderDate": "日期", "tableHeaderDescription": "描述", "tableHeaderAmount": "金额", "tableHeaderStatus": "状态", "tableHeaderActions": "操作", "noInvoicesFound": "未找到发票", "actionPreviewInvoice": "预览发票", "actionDownloadInvoice": "下载发票", "planDescription": "适合学习开发、提升技能或从事个人项目的用户。", "planNameBasicPlus": "Basic Plus", "planNameStarterPlus": "Starter Plus", "planNamePremiumPlus": "Premium Plus", "planPriceBasicPlus": "$100/年", "planPriceStarterPlus": "$200/年", "planPricePremiumPlus": "$300/年", "planTokensBasicPlus": "每月 5M 代币", "planTokensStarterPlus": "每月 25M 代币", "planTokensPremiumPlus": "每月 80M 代币", "priceSeparator": "/", "currentPlanLabel": "当前套餐", "upgradePlanLabel": "升级套餐", "nextPage": "下一页", "previousPage": "上一页", "paginationStatus": "显示第 {{from}} 到 {{to}} 条，共 {{total}} 条结果"}