import UnoCSS from 'unocss/vite';
import { defineConfig, loadEnv, type ViteDevServer } from 'vite';
import { nodePolyfills } from 'vite-plugin-node-polyfills';
import { optimizeCssModules } from 'vite-plugin-optimize-css-modules';
import tsconfigPaths from 'vite-tsconfig-paths';
import * as dotenv from 'dotenv';
import { execSync } from 'child_process';
import react from '@vitejs/plugin-react';
import path from 'path';

dotenv.config();

// Get git hash with fallback
const getGitHash = () => {
  try {
    return execSync('git rev-parse --short HEAD').toString().trim();
  } catch {
    return 'no-git-info';
  }
};

const ReactCompilerConfig = {
  /* ... */
};

export default defineConfig((config) => {
  return {
    define: {
      __COMMIT_HASH: JSON.stringify(getGitHash()),
      __APP_VERSION: JSON.stringify(process.env.npm_package_version),

      // 'process.env': JSON.stringify(process.env)
    },
    server: {
      host: true,
      allowedHosts: true,
      hmr: {
        protocol: 'ws',
        overlay: false, // Disable error overlays that trigger reloads
      },
      watch: {
        usePolling: false, // Disable polling to reduce unnecessary triggers
        ignored: ['**/node_modules/**', '**/.git/**'], // Ignore folders that trigger false events
      },
    },
    preview: {
      host: true,
      allowedHosts: true,
    },
    build: {
      target: 'esnext',
      rollupOptions: {
        input: path.resolve(__dirname, 'index.html'),
      },
    },
    plugins: [
      nodePolyfills({
        include: ['path', 'buffer', 'process'],
      }),
      UnoCSS(),
      tsconfigPaths(),
      react({
        babel: {
          plugins: [['babel-plugin-react-compiler', ReactCompilerConfig]],
        },
      }),
      chrome129IssuePlugin(),
      htmlEnvPlugin(config.mode),
      config.mode === 'production' && optimizeCssModules({ apply: 'build' }),
    ],
    envPrefix: ['VITE_', 'OPENAI_LIKE_API_', 'OLLAMA_API_BASE_URL', 'LMSTUDIO_API_BASE_URL', 'CUSTOM_', 'SUPA_'],
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
        },
      },
    },
  };
});

function htmlEnvPlugin(mode: string) {
  const env = loadEnv(mode, process.cwd(), '');
  return {
    name: 'html-transform',
    transformIndexHtml(html: string) {
      return html.replace(/%(\w+)%/g, (_, key) => env[key] || '');
    },
  };
}

function chrome129IssuePlugin() {
  return {
    name: 'chrome129IssuePlugin',
    server: {
      allowedHosts: ['dev-ide.biela.dev'], // Add the required host
    },
    configureServer(server: ViteDevServer) {
      server.middlewares.use((req, res, next) => {
        const raw = req.headers['user-agent']?.match(/Chrom(e|ium)\/([0-9]+)\./);

        if (raw) {
          const version = parseInt(raw[2], 10);

          if (version === 129) {
            res.setHeader('content-type', 'text/html');
            res.end(
              '<body><h1>Please use Chrome Canary for testing.</h1><p>Chrome 129 has an issue with JavaScript modules & Vite local development</p></body>',
            );

            return;
          }
        }

        next();
      });
    },
  };
}
